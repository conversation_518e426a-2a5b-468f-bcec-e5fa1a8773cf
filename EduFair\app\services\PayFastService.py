"""
PayFast Payment Gateway Integration for EduFair Platform

This service handles PayFast payment processing for event registrations and subscriptions.
PayFast is a South African payment gateway that supports multiple payment methods.

Features:
- Event ticket payments
- Subscription payments
- Payment verification
- Webhook handling
- Sandbox and production modes
"""

import os
import uuid
import hashlib
import urllib.parse
from typing import Dict, Any, Optional
from decimal import Decimal
from datetime import datetime, timezone
from sqlalchemy.orm import Session
from fastapi import HTTPException
import requests

from Models.Events import EventPayment, EventRegistration, PaymentStatusEnum, PaymentGatewayEnum, RegistrationStatusEnum
from Models.users import UserSubscription
from Schemas.Events.EventManagement import EventPaymentOut


class PayFastService:
    """
    PayFast payment gateway integration service

    IMPORTANT: All payments are processed through the admin's PayFast account.
    Institutes do not receive money directly. Revenue distribution is handled
    manually by admins through the payout system.
    """

    def __init__(self):
        # Load PayFast credentials from environment
        self.merchant_id = os.getenv("PAYFAST_MERCHANT_ID", "")
        self.merchant_key = os.getenv("PAYFAST_MERCHANT_KEY", "")
        self.passphrase = os.getenv("PAYFAST_PASSPHRASE", "")  # Optional but recommended
        self.sandbox_url = os.getenv("PAYFAST_SANDBOX_URL", "https://sandbox.payfast.co.za/eng/process")
        self.production_url = "https://www.payfast.co.za/eng/process"
        
        # Determine if we're in sandbox mode
        self.is_sandbox = os.getenv("PAYFAST_SANDBOX", "true").lower() == "true"
        self.base_url = self.sandbox_url if self.is_sandbox else self.production_url
        
        # Validate credentials
        if not self.merchant_id or not self.merchant_key:
            raise ValueError("PayFast credentials not found in environment variables")
    
    def generate_signature(self, data: Dict[str, Any]) -> str:
        """Generate PayFast signature for payment verification"""
        
        # Create parameter string
        param_string = ""
        for key in sorted(data.keys()):
            if key != 'signature':
                param_string += f"{key}={urllib.parse.quote_plus(str(data[key]))}&"
        
        # Remove trailing &
        param_string = param_string.rstrip('&')
        
        # Add passphrase if provided
        if self.passphrase:
            param_string += f"&passphrase={urllib.parse.quote_plus(self.passphrase)}"
        
        # Generate MD5 hash
        signature = hashlib.md5(param_string.encode()).hexdigest()
        return signature
    
    def create_payment_form_data(
        self,
        amount: Decimal,
        item_name: str,
        item_description: str,
        custom_str1: str = None,
        custom_str2: str = None,
        custom_str3: str = None,
        email_address: str = None,
        name_first: str = None,
        name_last: str = None,
        return_url: str = None,
        cancel_url: str = None,
        notify_url: str = None
    ) -> Dict[str, Any]:
        """Create PayFast payment form data"""
        
        # Generate unique payment ID
        m_payment_id = str(uuid.uuid4())
        
        # Prepare payment data
        payment_data = {
            'merchant_id': self.merchant_id,
            'merchant_key': self.merchant_key,
            'return_url': return_url or f"{os.getenv('FRONTEND_URL', 'http://localhost:3000')}/payment/success",
            'cancel_url': cancel_url or f"{os.getenv('FRONTEND_URL', 'http://localhost:3000')}/payment/cancel",
            'notify_url': notify_url or f"{os.getenv('BACKEND_URL', 'http://localhost:8000')}/api/payments/payfast/webhook",
            'name_first': name_first or '',
            'name_last': name_last or '',
            'email_address': email_address or '',
            'm_payment_id': m_payment_id,
            'amount': f"{amount:.2f}",
            'item_name': item_name,
            'item_description': item_description,
            'custom_str1': custom_str1 or '',
            'custom_str2': custom_str2 or '',
            'custom_str3': custom_str3 or '',
        }
        
        # Remove empty values
        payment_data = {k: v for k, v in payment_data.items() if v}
        
        # Generate signature
        signature = self.generate_signature(payment_data)
        payment_data['signature'] = signature
        
        return {
            'payment_data': payment_data,
            'payment_url': self.base_url,
            'payment_id': m_payment_id
        }
    
    def verify_payment_signature(self, data: Dict[str, Any]) -> bool:
        """Verify PayFast payment notification signature"""
        
        if 'signature' not in data:
            return False
        
        received_signature = data['signature']
        calculated_signature = self.generate_signature(data)
        
        return received_signature == calculated_signature
    
    def validate_payment_data(self, data: Dict[str, Any]) -> bool:
        """Validate PayFast payment notification data"""
        
        # Check if payment is from PayFast
        if not self.is_sandbox:
            # In production, validate the source IP
            # PayFast IPs: **************/28, *************/27
            # This should be implemented based on request IP
            pass
        
        # Verify signature
        if not self.verify_payment_signature(data):
            return False
        
        # Verify merchant details
        if data.get('merchant_id') != self.merchant_id:
            return False
        
        return True
    
    def create_event_payment(
        self,
        db: Session,
        registration_id: uuid.UUID,
        amount: Decimal,
        currency: str = "PKR",
        user_email: str = None,
        user_name: str = None
    ) -> Dict[str, Any]:
        """Create PayFast payment for event registration"""
        
        # Get registration details
        registration = db.query(EventRegistration).filter(
            EventRegistration.id == registration_id
        ).first()
        
        if not registration:
            raise HTTPException(status_code=404, detail="Registration not found")
        
        if registration.payment_status == PaymentStatusEnum.COMPLETED:
            raise HTTPException(status_code=400, detail="Payment already completed")
        
        # Create payment record
        payment = EventPayment(
            event_id=registration.event_id,
            registration_id=registration_id,
            user_id=registration.user_id,
            amount=amount,
            currency=currency,
            payment_method=PaymentGatewayEnum.PAYFAST,
            payment_gateway="payfast",
            status=PaymentStatusEnum.PENDING
        )
        
        db.add(payment)
        db.commit()
        db.refresh(payment)
        
        # Create PayFast payment form data
        payment_form = self.create_payment_form_data(
            amount=amount,
            item_name=f"Event Registration - {registration.event.title if hasattr(registration, 'event') else 'Event'}",
            item_description=f"Registration for event (ID: {registration.event_id})",
            custom_str1=str(payment.id),  # Our payment ID
            custom_str2=str(registration_id),  # Registration ID
            custom_str3="event_payment",  # Payment type
            email_address=user_email,
            name_first=user_name.split(' ')[0] if user_name else '',
            name_last=' '.join(user_name.split(' ')[1:]) if user_name and ' ' in user_name else ''
        )
        
        # Update payment with PayFast payment ID
        payment.gateway_payment_intent_id = payment_form['payment_id']
        payment.gateway_response = payment_form['payment_data']
        
        db.commit()
        db.refresh(payment)
        
        return {
            'payment_id': payment.id,
            'payfast_payment_id': payment_form['payment_id'],
            'payment_url': payment_form['payment_url'],
            'payment_data': payment_form['payment_data'],
            'status': 'pending'
        }
    
    def create_subscription_payment(
        self,
        db: Session,
        subscription_id: uuid.UUID,
        amount: Decimal,
        currency: str = "PKR",
        user_email: str = None,
        user_name: str = None
    ) -> Dict[str, Any]:
        """Create PayFast payment for subscription"""
        
        # Get subscription details
        subscription = db.query(UserSubscription).filter(
            UserSubscription.id == subscription_id
        ).first()
        
        if not subscription:
            raise HTTPException(status_code=404, detail="Subscription not found")
        
        # Create PayFast payment form data
        payment_form = self.create_payment_form_data(
            amount=amount,
            item_name=f"Subscription - {subscription.plan.name if hasattr(subscription, 'plan') else 'Plan'}",
            item_description=f"Subscription payment (ID: {subscription_id})",
            custom_str1=str(subscription_id),  # Subscription ID
            custom_str2="subscription_payment",  # Payment type
            custom_str3="",
            email_address=user_email,
            name_first=user_name.split(' ')[0] if user_name else '',
            name_last=' '.join(user_name.split(' ')[1:]) if user_name and ' ' in user_name else ''
        )
        
        return {
            'payfast_payment_id': payment_form['payment_id'],
            'payment_url': payment_form['payment_url'],
            'payment_data': payment_form['payment_data'],
            'status': 'pending'
        }

    def process_webhook_notification(
        self,
        db: Session,
        notification_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process PayFast webhook notification"""

        # Validate notification
        if not self.validate_payment_data(notification_data):
            raise HTTPException(status_code=400, detail="Invalid payment notification")

        payment_status = notification_data.get('payment_status')
        custom_str1 = notification_data.get('custom_str1')  # Our payment ID or subscription ID
        custom_str2 = notification_data.get('custom_str2')  # Registration ID or payment type
        custom_str3 = notification_data.get('custom_str3')  # Payment type

        if custom_str3 == "event_payment":
            return self._process_event_payment_notification(db, notification_data)
        elif custom_str2 == "subscription_payment":
            return self._process_subscription_payment_notification(db, notification_data)
        else:
            raise HTTPException(status_code=400, detail="Unknown payment type")

    def _process_event_payment_notification(
        self,
        db: Session,
        notification_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process event payment notification"""

        # Handle both custom field mappings for compatibility
        custom_str1 = notification_data.get('custom_str1')
        custom_str2 = notification_data.get('custom_str2')
        custom_str3 = notification_data.get('custom_str3')
        payment_status = notification_data.get('payment_status')

        payment = None
        registration = None

        # Try to find payment by different field mappings
        if custom_str3 == "event_payment":
            # New format: custom_str1 = payment_id, custom_str2 = registration_id
            payment_id = custom_str1
            registration_id = custom_str2

            if payment_id:
                payment = db.query(EventPayment).filter(EventPayment.id == payment_id).first()

        elif custom_str3 == "event_ticket":
            # Old format: custom_str1 = registration_id, custom_str2 = payment_id
            registration_id = custom_str1
            payment_id = custom_str2

            if payment_id:
                payment = db.query(EventPayment).filter(EventPayment.id == payment_id).first()

        # If payment not found by ID, try to find by registration_id
        if not payment and registration_id:
            registration = db.query(EventRegistration).filter(
                EventRegistration.id == registration_id
            ).first()

            if registration:
                payment = db.query(EventPayment).filter(
                    EventPayment.registration_id == registration_id
                ).first()

        if not payment:
            raise HTTPException(status_code=404, detail=f"Payment not found for custom fields: {custom_str1}, {custom_str2}, {custom_str3}")

        # Get registration if not already found
        if not registration:
            registration = db.query(EventRegistration).filter(
                EventRegistration.id == payment.registration_id
            ).first()

        # Update payment status based on PayFast status
        if payment_status == "COMPLETE":
            payment.status = PaymentStatusEnum.COMPLETED
            payment.processed_at = datetime.now(timezone.utc)
            payment.gateway_transaction_id = notification_data.get('pf_payment_id')

            # Update registration payment status
            if registration:
                registration.status = RegistrationStatusEnum.CONFIRMED
                registration.payment_status = PaymentStatusEnum.COMPLETED
                registration.payment_reference = payment.gateway_transaction_id
                registration.payment_method = "payfast"
                registration.confirmed_at = datetime.now(timezone.utc)

                print(f"✅ Payment COMPLETED: Registration {registration.id} confirmed, Payment {payment.id} completed")

        elif payment_status == "FAILED":
            payment.status = PaymentStatusEnum.FAILED
            payment.failed_at = datetime.now(timezone.utc)
            payment.failure_reason = "Payment failed at PayFast"

            if registration:
                registration.status = RegistrationStatusEnum.CANCELLED
                registration.payment_status = PaymentStatusEnum.FAILED

            print(f"❌ Payment FAILED: Registration {registration.id if registration else 'N/A'}, Payment {payment.id}")

        elif payment_status == "CANCELLED":
            payment.status = PaymentStatusEnum.FAILED
            payment.failed_at = datetime.now(timezone.utc)
            payment.failure_reason = "Payment cancelled by user"

            if registration:
                registration.status = RegistrationStatusEnum.CANCELLED
                registration.payment_status = PaymentStatusEnum.FAILED

            print(f"❌ Payment CANCELLED: Registration {registration.id if registration else 'N/A'}, Payment {payment.id}")

        else:
            print(f"⚠️ Unknown payment status: {payment_status}")

        # Store full notification data
        payment.gateway_response = notification_data

        try:
            db.commit()
            db.refresh(payment)
            print(f"✅ Database updated successfully for payment {payment.id}")
        except Exception as commit_error:
            print(f"❌ Database commit failed: {commit_error}")
            db.rollback()
            raise

        # Try to send WebSocket update (non-blocking)
        try:
            from websocket.payment_manager import payment_manager
            import asyncio

            # Prepare webhook data for WebSocket processing
            webhook_data = {
                'custom_str1': str(payment.registration_id),  # booking_id
                'payment_status': payment_status,
                'pf_payment_id': notification_data.get('pf_payment_id')
            }

            # Only send WebSocket update if there's an active connection
            booking_id = str(payment.registration_id)
            if booking_id in payment_manager.get_connected_bookings():
                # Create new event loop for WebSocket update
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(payment_manager.broadcast_payment_update(
                        booking_id,
                        "confirmed" if payment_status == "COMPLETE" else "failed"
                    ))
                    loop.close()
                except Exception as ws_error:
                    print(f"WebSocket update failed: {ws_error}")

        except Exception as e:
            # WebSocket is optional - don't fail the payment processing
            print(f"WebSocket notification failed (non-critical): {e}")

        return {
            'payment_id': payment.id,
            'status': payment.status.value,
            'message': f"Payment {payment.status.value}"
        }

    def _process_subscription_payment_notification(
        self,
        db: Session,
        notification_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process subscription payment notification"""

        subscription_id = notification_data.get('custom_str1')
        payment_status = notification_data.get('payment_status')

        if not subscription_id:
            raise HTTPException(status_code=400, detail="Subscription ID not found in notification")

        # Get subscription record
        subscription = db.query(UserSubscription).filter(
            UserSubscription.id == subscription_id
        ).first()

        if not subscription:
            raise HTTPException(status_code=404, detail="Subscription not found")

        # Update subscription status based on PayFast status
        if payment_status == "COMPLETE":
            subscription.status = "active"
            subscription.payment_reference = notification_data.get('pf_payment_id')

            # Extend subscription if it's a renewal
            if subscription.end_date and subscription.end_date < datetime.now(timezone.utc):
                from datetime import timedelta
                if hasattr(subscription, 'plan') and subscription.plan:
                    subscription.end_date = datetime.now(timezone.utc) + timedelta(days=subscription.plan.duration_days)

        elif payment_status in ["FAILED", "CANCELLED"]:
            # Keep subscription status as is, but log the failed payment
            pass

        db.commit()
        db.refresh(subscription)

        return {
            'subscription_id': subscription.id,
            'status': subscription.status,
            'message': f"Subscription payment {payment_status.lower()}"
        }

    def get_payment_status(
        self,
        db: Session,
        payment_id: uuid.UUID
    ) -> EventPaymentOut:
        """Get payment status"""

        payment = db.query(EventPayment).filter(
            EventPayment.id == payment_id
        ).first()

        if not payment:
            raise HTTPException(status_code=404, detail="Payment not found")

        return EventPaymentOut.model_validate(payment)

    def refund_payment(
        self,
        db: Session,
        payment_id: uuid.UUID,
        refund_amount: Optional[Decimal] = None,
        reason: str = "Refund requested"
    ) -> Dict[str, Any]:
        """Process payment refund (Note: PayFast doesn't support automatic refunds via API)"""

        payment = db.query(EventPayment).filter(
            EventPayment.id == payment_id
        ).first()

        if not payment:
            raise HTTPException(status_code=404, detail="Payment not found")

        if payment.status != PaymentStatusEnum.COMPLETED:
            raise HTTPException(status_code=400, detail="Can only refund completed payments")

        # PayFast doesn't support automatic refunds via API
        # This would need to be processed manually through PayFast dashboard
        # For now, we'll mark it as requiring manual processing

        refund_amount = refund_amount or payment.amount

        payment.refund_amount = refund_amount
        payment.refund_reason = reason
        payment.status = PaymentStatusEnum.REFUNDED
        payment.refunded_at = datetime.now(timezone.utc)

        # Update registration status
        registration = db.query(EventRegistration).filter(
            EventRegistration.id == payment.registration_id
        ).first()
        if registration:
            registration.payment_status = PaymentStatusEnum.REFUNDED
            registration.cancelled_at = datetime.now(timezone.utc)

        db.commit()
        db.refresh(payment)

        return {
            'payment_id': payment.id,
            'refund_amount': float(refund_amount),
            'status': 'refunded',
            'message': 'Refund processed (manual verification required)',
            'note': 'PayFast refunds require manual processing through the PayFast dashboard'
        }
