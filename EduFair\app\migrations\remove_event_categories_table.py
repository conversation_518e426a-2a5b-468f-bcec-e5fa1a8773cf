"""
Migration to remove event_categories table and update events table to use enum directly.

This migration:
1. Adds a new category column with enum type to events table
2. Migrates existing data from category_id to category enum
3. Drops the foreign key constraint and category_id column
4. Drops the event_categories table
"""

from sqlalchemy import text
from config.session import engine


def migrate_event_categories():
    """
    Execute the migration to remove event_categories table and use enum directly.
    """
    
    with engine.connect() as connection:
        # Start transaction
        trans = connection.begin()
        
        try:
            print("Starting migration to remove event_categories table...")
            
            # Step 1: Create the enum type if it doesn't exist
            print("1. Creating EventCategoryEnum type...")
            connection.execute(text("""
                DO $$ BEGIN
                    CREATE TYPE eventcategoryenum AS ENUM ('workshop', 'conference', 'webinar', 'competition');
                EXCEPTION
                    WHEN duplicate_object THEN null;
                END $$;
            """))
            
            # Step 2: Add new category column to events table
            print("2. Adding category column to events table...")
            connection.execute(text("""
                ALTER TABLE events 
                ADD COLUMN IF NOT EXISTS category eventcategoryenum;
            """))
            
            # Step 3: Migrate existing data from category_id to category enum
            print("3. Migrating existing category data...")

            # Check if event_categories table exists
            table_exists = connection.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_name = 'event_categories'
                );
            """)).scalar()

            if table_exists:
                # Map category names to enum values
                category_mappings = {
                    'workshop': 'workshop',
                    'conference': 'conference',
                    'webinar': 'webinar',
                    'competition': 'competition',
                    'workshops': 'workshop',  # Handle plural forms
                    'conferences': 'conference',
                    'webinars': 'webinar',
                    'competitions': 'competition'
                }

                # Update events with category enum based on category name
                for category_name, enum_value in category_mappings.items():
                    connection.execute(text(f"""
                        UPDATE events
                        SET category = '{enum_value}'
                        WHERE category_id IN (
                            SELECT id FROM event_categories
                            WHERE LOWER(name) = '{category_name.lower()}'
                        );
                    """))
            else:
                print("   event_categories table does not exist, skipping data migration...")

            # Set default category for any remaining null values
            connection.execute(text("""
                UPDATE events
                SET category = 'workshop'
                WHERE category IS NULL;
            """))
            
            # Step 4: Make category column NOT NULL
            print("4. Making category column NOT NULL...")
            connection.execute(text("""
                ALTER TABLE events 
                ALTER COLUMN category SET NOT NULL;
            """))
            
            # Step 5: Drop foreign key constraint
            print("5. Dropping foreign key constraint...")
            # First, find the constraint name
            constraint_result = connection.execute(text("""
                SELECT constraint_name 
                FROM information_schema.table_constraints 
                WHERE table_name = 'events' 
                AND constraint_type = 'FOREIGN KEY'
                AND constraint_name LIKE '%category%';
            """))
            
            constraint_names = [row[0] for row in constraint_result]
            for constraint_name in constraint_names:
                connection.execute(text(f"""
                    ALTER TABLE events DROP CONSTRAINT IF EXISTS {constraint_name};
                """))
            
            # Step 6: Drop category_id column
            print("6. Dropping category_id column...")
            connection.execute(text("""
                ALTER TABLE events DROP COLUMN IF EXISTS category_id;
            """))
            
            # Step 7: Drop event_categories table
            print("7. Dropping event_categories table...")
            connection.execute(text("""
                DROP TABLE IF EXISTS event_categories CASCADE;
            """))
            
            # Commit transaction
            trans.commit()
            print("Migration completed successfully!")
            
        except Exception as e:
            # Rollback on error
            trans.rollback()
            print(f"Migration failed: {e}")
            raise


def rollback_migration():
    """
    Rollback the migration (recreate event_categories table and restore relationships).
    """
    
    with engine.connect() as connection:
        trans = connection.begin()
        
        try:
            print("Starting rollback of event categories migration...")
            
            # Step 1: Recreate event_categories table
            print("1. Recreating event_categories table...")
            connection.execute(text("""
                CREATE TABLE IF NOT EXISTS event_categories (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    name VARCHAR(100) NOT NULL UNIQUE,
                    display_name VARCHAR(100) NOT NULL,
                    description TEXT,
                    color VARCHAR(7),
                    icon VARCHAR(50),
                    banner_image_url VARCHAR(500),
                    is_active BOOLEAN DEFAULT TRUE,
                    sort_order INTEGER DEFAULT 0,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                );
            """))
            
            # Step 2: Insert default categories
            print("2. Inserting default categories...")
            connection.execute(text("""
                INSERT INTO event_categories (name, display_name, description, is_active, sort_order)
                VALUES 
                    ('workshop', 'Workshop', 'Interactive learning sessions', TRUE, 1),
                    ('conference', 'Conference', 'Professional conferences and seminars', TRUE, 2),
                    ('webinar', 'Webinar', 'Online educational sessions', TRUE, 3),
                    ('competition', 'Competition', 'Academic and skill competitions', TRUE, 4)
                ON CONFLICT (name) DO NOTHING;
            """))
            
            # Step 3: Add category_id column back to events
            print("3. Adding category_id column back to events...")
            connection.execute(text("""
                ALTER TABLE events 
                ADD COLUMN IF NOT EXISTS category_id UUID;
            """))
            
            # Step 4: Update category_id based on category enum
            print("4. Updating category_id based on category enum...")
            connection.execute(text("""
                UPDATE events 
                SET category_id = ec.id
                FROM event_categories ec
                WHERE events.category::text = ec.name;
            """))
            
            # Step 5: Add foreign key constraint
            print("5. Adding foreign key constraint...")
            connection.execute(text("""
                ALTER TABLE events 
                ADD CONSTRAINT fk_events_category_id 
                FOREIGN KEY (category_id) REFERENCES event_categories(id);
            """))
            
            # Step 6: Make category_id NOT NULL
            print("6. Making category_id NOT NULL...")
            connection.execute(text("""
                ALTER TABLE events 
                ALTER COLUMN category_id SET NOT NULL;
            """))
            
            # Step 7: Drop category enum column
            print("7. Dropping category enum column...")
            connection.execute(text("""
                ALTER TABLE events DROP COLUMN IF EXISTS category;
            """))
            
            trans.commit()
            print("Rollback completed successfully!")
            
        except Exception as e:
            trans.rollback()
            print(f"Rollback failed: {e}")
            raise


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "rollback":
        rollback_migration()
    else:
        migrate_event_categories()
