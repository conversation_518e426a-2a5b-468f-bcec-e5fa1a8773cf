# PayFast Payment Gateway Setup

EduFair uses PayFast as the primary payment gateway for processing all payments including event registrations and subscriptions.

## Current Status

✅ **PayFast is the primary payment system**
✅ **Dummy payment service has been deprecated**
✅ **All payment flows use PayFast**

## Required Environment Variables

Set these environment variables for PayFast to work:

```bash
# PayFast Credentials (Required)
PAYFAST_MERCHANT_ID=your_merchant_id_here
PAYFAST_MERCHANT_KEY=your_merchant_key_here
PAYFAST_PASSPHRASE=your_passphrase_here  # Optional but recommended

# PayFast Mode
PAYFAST_SANDBOX=true  # Set to "false" for production

# Application URLs (Required for webhooks and redirects)
FRONTEND_URL=https://yourdomain.com
BACKEND_URL=https://api.yourdomain.com
```

## PayFast Account Setup

1. **Create PayFast Account**
   - Go to https://www.payfast.co.za/
   - Sign up for a merchant account
   - Complete the verification process

2. **Get Credentials**
   - Login to PayFast dashboard
   - Go to Settings > Integration
   - Copy your Merchant ID and Merchant Key
   - Set a passphrase (recommended for security)

3. **Configure Webhooks**
   - In PayFast dashboard, set the ITN (Instant Transaction Notification) URL to:
   - `https://api.yourdomain.com/api/payments/payfast/webhook`

4. **Test in Sandbox**
   - Use sandbox mode for testing: `PAYFAST_SANDBOX=true`
   - PayFast provides test card numbers for sandbox testing

## API Endpoints

The main PayFast endpoints are available at:

- **Create Event Payment**: `POST /api/payments/payfast/events/payment`
- **Create Subscription Payment**: `POST /api/payments/payfast/subscriptions/payment`
- **Webhook Handler**: `POST /api/payments/payfast/webhook`
- **Payment Status**: `GET /api/payments/payfast/events/payment/{payment_id}/status`
- **Configuration**: `GET /api/payments/payfast/config`

## File Structure

```
app/
├── services/
│   ├── PayFastService.py          # Main PayFast service class
│   ├── DummyPaymentService.py     # DEPRECATED - kept for reference
│   └── README_PaymentServices.md  # Payment services documentation
├── Routes/
│   ├── PayFast.py                 # Main PayFast API routes
│   └── PayFast/
│       └── PayFastPayments.py     # DEPRECATED - not used
├── Schemas/
│   └── PayFast.py                 # PayFast request/response schemas
└── Models/
    └── Events.py                  # Payment and registration models
```

## Key Features

- **Event Ticket Payments**: Users can purchase event tickets through PayFast
- **Subscription Payments**: Handle subscription renewals and upgrades
- **Webhook Processing**: Automatic payment status updates via PayFast webhooks
- **Multi-currency Support**: ZAR, USD, EUR, GBP
- **Sandbox Testing**: Full sandbox environment for testing
- **Security**: Signature validation for all webhook notifications

## Migration from Dummy Payment Service

The dummy payment service has been deprecated. All payment processing now uses PayFast:

- ❌ `DummyPaymentService` - No longer used
- ✅ `PayFastService` - Primary payment service
- ✅ Environment-based configuration
- ✅ Real payment processing
- ✅ Webhook notifications

## Troubleshooting

### PayFast Service Not Initialized
If you see "PayFast service not initialized", check:
1. Environment variables are set correctly
2. `PAYFAST_MERCHANT_ID` and `PAYFAST_MERCHANT_KEY` are not empty
3. No typos in environment variable names

### Webhook Not Working
If payment status is not updating:
1. Check webhook URL is configured in PayFast dashboard
2. Ensure webhook endpoint is accessible from internet
3. Verify signature validation is working
4. Check server logs for webhook errors

### Payment Failures
If payments are failing:
1. Verify you're using correct sandbox/production mode
2. Check PayFast account status and limits
3. Ensure payment amounts are within allowed ranges
4. Verify currency is supported

## Support

For PayFast-specific issues:
- PayFast Documentation: https://developers.payfast.co.za/
- PayFast Support: <EMAIL>

For EduFair integration issues:
- Check the PayFastService class in `app/services/PayFastService.py`
- Review API routes in `app/Routes/PayFast.py`
- Check payment models in `app/Models/Events.py`
