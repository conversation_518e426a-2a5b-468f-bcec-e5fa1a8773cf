"""
Competition CRUD Operations for EduFair Platform

This module contains all CRUD operations for the Competition system including:
- Competition creation and management
- Mentor assignment and workload management
- Competition submission handling
- Result calculation and leaderboard generation
- Integration with exam system
"""

import uuid
from datetime import datetime, timezone, timedelta
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, and_, or_, desc
from fastapi import HTTPException, status
from typing import List, Optional, Dict, Any, Tuple

# Import Models
from Models.Events import Event, EventStatusEnum
from Models.Competitions import (
    CompetitionMentorAssignment, CompetitionResult,
    MentorAssignmentStatusEnum, CompetitionStatusEnum
)
from Models.Exam import Exam, StudentExamAttempt, StudentExamAnswer
from Models.users import User, UserTypeEnum

# Import Schemas
from Schemas.Events.Competitions import (
    CompetitionEventCreate, CompetitionEventOut, CompetitionEventUpdate,
    CompetitionMentorAssignmentCreate, CompetitionMentorAssignmentOut,
    CompetitionMentorAssignmentUpdate
)


# ==================== COMPETITION CREATION AND MANAGEMENT ====================

def create_competition_event(db: Session, competition_data: CompetitionEventCreate, institute_id: uuid.UUID) -> CompetitionEventOut:
    """Create a new competition event with exam assignment"""
    try:
        # Validate exam exists
        exam = db.query(Exam).filter(Exam.id == competition_data.exam_id).first()
        if not exam:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Exam not found"
            )

        # Check if we need to copy the exam
        competition_exam_id = competition_data.exam_id

        if competition_data.copy_exam:
            # Create a copy of the exam for this competition
            from Cruds.Exams.Exam import copy_exam
            copied_exam = copy_exam(
                db,
                competition_data.exam_id,
                institute_id,
                f"{exam.title} (Competition Copy)"
            )
            competition_exam_id = copied_exam.id

            # Mark the copied exam as competition exam
            copied_exam.is_competition_exam = True
            copied_exam.manual_review_required = True
        else:
            # Use the original exam but mark it as competition exam
            exam.is_competition_exam = True
            exam.manual_review_required = True  # Competitions require mentor review

        # Create event
        event_data = {
            "title": competition_data.title,
            "description": competition_data.description,
            "short_description": competition_data.short_description,
            "start_datetime": competition_data.start_datetime,
            "end_datetime": competition_data.end_datetime,
            "category_id": competition_data.category_id,
            "location_id": competition_data.location_id,
            "max_attendees": competition_data.max_attendees,
            "institute_id": institute_id,
            "organizer_id": institute_id,
            "is_competition": True,
            "competition_exam_id": competition_exam_id,
            "competition_rules": competition_data.competition_rules,
            "prize_details": competition_data.prize_details,
            "status": EventStatusEnum.DRAFT
        }

        event = Event(**event_data)
        db.add(event)
        db.flush()  # Get the event ID

        # Store competition-specific settings
        competition_settings = {
            "judging_type": competition_data.judging_type,
            "mentor_assignment_strategy": competition_data.mentor_assignment_strategy,
            "min_mentors_required": competition_data.min_mentors_required,
            "judging_deadline": competition_data.judging_deadline,
            "result_publication_date": competition_data.result_publication_date,
            "evaluation_criteria": competition_data.evaluation_criteria
        }

        # Store settings in event metadata or separate table
        event.competition_settings = competition_settings

        db.commit()
        db.refresh(event)

        return CompetitionEventOut.model_validate(event)

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating competition: {str(e)}"
        )


def get_competition_by_id(db: Session, competition_id: uuid.UUID) -> CompetitionEventOut:
    """Get competition details by ID"""
    competition = db.query(Event).filter(
        Event.id == competition_id,
        Event.is_competition == True
    ).options(
        joinedload(Event.competition_exam),
        joinedload(Event.mentor_assignments)
    ).first()
    
    if not competition:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Competition not found"
        )
    
    # Add competition statistics
    stats = get_competition_statistics(db, competition_id)
    
    competition_out = CompetitionEventOut.model_validate(competition)
    competition_out.total_participants = stats.get("total_participants", 0)
    competition_out.total_submissions = stats.get("total_submissions", 0)
    competition_out.competition_status = _determine_competition_status(competition)
    
    return competition_out


def update_competition_event(db: Session, competition_id: uuid.UUID, update_data: CompetitionEventUpdate, user_id: uuid.UUID) -> CompetitionEventOut:
    """Update competition event"""
    competition = db.query(Event).filter(
        Event.id == competition_id,
        Event.is_competition == True
    ).first()
    
    if not competition:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Competition not found"
        )
    
    # Check authorization
    if competition.institute_id != user_id and competition.organizer_id != user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to update this competition"
        )
    
    try:
        # Update fields
        update_dict = update_data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            setattr(competition, field, value)
        
        competition.updated_at = datetime.now(timezone.utc)
        db.commit()
        db.refresh(competition)
        
        return CompetitionEventOut.model_validate(competition)
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating competition: {str(e)}"
        )


def delete_competition_event(db: Session, competition_id: uuid.UUID, user_id: uuid.UUID) -> Dict[str, str]:
    """Delete competition event"""
    competition = db.query(Event).filter(
        Event.id == competition_id,
        Event.is_competition == True
    ).first()
    
    if not competition:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Competition not found"
        )
    
    # Check authorization
    if competition.institute_id != user_id and competition.organizer_id != user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to delete this competition"
        )
    
    # Check if competition has participants
    participant_count = db.query(StudentExamAttempt).filter(
        StudentExamAttempt.exam_id == competition.competition_exam_id
    ).count()
    
    if participant_count > 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete competition with participants"
        )
    
    try:
        db.delete(competition)
        db.commit()
        return {"message": "Competition deleted successfully"}
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting competition: {str(e)}"
        )


def get_competitions_by_institute(db: Session, institute_id: uuid.UUID, skip: int = 0, limit: int = 100) -> List[CompetitionEventOut]:
    """Get all competitions for an institute"""
    competitions = db.query(Event).filter(
        Event.institute_id == institute_id,
        Event.is_competition == True
    ).options(
        joinedload(Event.competition_exam)
    ).offset(skip).limit(limit).all()
    
    result = []
    for competition in competitions:
        stats = get_competition_statistics(db, competition.id)
        competition_out = CompetitionEventOut.model_validate(competition)
        competition_out.total_participants = stats.get("total_participants", 0)
        competition_out.total_submissions = stats.get("total_submissions", 0)
        competition_out.competition_status = _determine_competition_status(competition)
        result.append(competition_out)
    
    return result


# ==================== MENTOR ASSIGNMENT SYSTEM ====================

def assign_mentor_to_competition(db: Session, assignment_data: CompetitionMentorAssignmentCreate) -> CompetitionMentorAssignmentOut:
    """Assign a mentor to a competition"""
    try:
        # Validate competition exists
        competition = db.query(Event).filter(
            Event.id == assignment_data.competition_id,
            Event.is_competition == True
        ).first()
        
        if not competition:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Competition not found"
            )
        
        # Validate mentor exists and is qualified
        mentor = db.query(User).filter(
            User.id == assignment_data.mentor_id,
            User.user_type.in_([UserTypeEnum.mentor, UserTypeEnum.teacher])
        ).first()
        
        if not mentor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Mentor not found or not qualified"
            )
        
        # Check if mentor is already assigned
        existing_assignment = db.query(CompetitionMentorAssignment).filter(
            CompetitionMentorAssignment.competition_id == assignment_data.competition_id,
            CompetitionMentorAssignment.mentor_id == assignment_data.mentor_id
        ).first()
        
        if existing_assignment:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Mentor already assigned to this competition"
            )
        
        # Create assignment
        assignment = CompetitionMentorAssignment(
            **assignment_data.model_dump(),
            status=MentorAssignmentStatusEnum.ASSIGNED,
            assigned_at=datetime.now(timezone.utc)
        )
        
        db.add(assignment)
        db.commit()
        db.refresh(assignment)
        
        return CompetitionMentorAssignmentOut.model_validate(assignment)
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error assigning mentor: {str(e)}"
        )


def auto_assign_mentors_to_competition(db: Session, competition_id: uuid.UUID, min_mentors: int = 1) -> List[CompetitionMentorAssignmentOut]:
    """Automatically assign mentors to a competition based on availability and expertise"""
    try:
        # Get competition details
        competition = db.query(Event).filter(
            Event.id == competition_id,
            Event.is_competition == True
        ).options(joinedload(Event.competition_exam)).first()

        if not competition:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Competition not found"
            )

        # Get available mentors with enhanced filtering
        available_mentors = _get_available_mentors_for_competition(db, competition_id)

        # Score and rank mentors based on suitability
        scored_mentors = _score_mentors_for_competition(db, available_mentors)

        # Select top mentors
        selected_mentors = scored_mentors[:min_mentors]

        assignments = []
        for mentor_data in selected_mentors:
            mentor = mentor_data["mentor"]
            match_score = mentor_data["score"]

            assignment_data = CompetitionMentorAssignmentCreate(
                competition_id=competition_id,
                mentor_id=mentor.id,
                assigned_by=competition.institute_id,
                institute_id=competition.institute_id,
                assignment_type="auto",
                workload_capacity=10,
                specialization_match_score=match_score
            )

            assignment = assign_mentor_to_competition(db, assignment_data)
            assignments.append(assignment)

        return assignments

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error auto-assigning mentors: {str(e)}"
        )


def _get_available_mentors_for_competition(db: Session, competition_id: uuid.UUID) -> List[User]:
    """Get available mentors for a competition with enhanced filtering"""
    # Get mentors who aren't already assigned to this competition
    assigned_mentor_ids = db.query(CompetitionMentorAssignment.mentor_id).filter(
        CompetitionMentorAssignment.competition_id == competition_id
    ).all()
    assigned_ids = [id[0] for id in assigned_mentor_ids]

    # Get active mentors and teachers
    available_mentors = db.query(User).filter(
        User.user_type.in_([UserTypeEnum.mentor, UserTypeEnum.teacher]),
        User.is_active == True,
        ~User.id.in_(assigned_ids)
    ).all()

    # Filter by current workload (mentors with capacity)
    filtered_mentors = []
    for mentor in available_mentors:
        current_workload = db.query(CompetitionMentorAssignment).filter(
            CompetitionMentorAssignment.mentor_id == mentor.id,
            CompetitionMentorAssignment.status.in_([
                MentorAssignmentStatusEnum.ASSIGNED,
                MentorAssignmentStatusEnum.ACCEPTED
            ])
        ).count()

        # Assume max workload of 5 competitions per mentor
        if current_workload < 5:
            filtered_mentors.append(mentor)

    return filtered_mentors


def _score_mentors_for_competition(db: Session, mentors: List[User]) -> List[Dict[str, Any]]:
    """Score mentors based on their suitability for the competition"""
    scored_mentors = []

    for mentor in mentors:
        score = 0.0

        # Base score for being available
        score += 0.3

        # Score based on experience (simplified - could use actual metrics)
        # This could be enhanced with actual mentor ratings, past performance, etc.
        score += 0.2

        # Score based on subject expertise (if available)
        # This would require a mentor-subject relationship table
        score += 0.3

        # Score based on current workload (lower workload = higher score)
        current_workload = db.query(CompetitionMentorAssignment).filter(
            CompetitionMentorAssignment.mentor_id == mentor.id,
            CompetitionMentorAssignment.status.in_([
                MentorAssignmentStatusEnum.ASSIGNED,
                MentorAssignmentStatusEnum.ACCEPTED
            ])
        ).count()

        workload_score = max(0, (5 - current_workload) / 5 * 0.2)
        score += workload_score

        scored_mentors.append({
            "mentor": mentor,
            "score": min(score, 1.0)  # Cap at 1.0
        })

    # Sort by score (highest first)
    scored_mentors.sort(key=lambda x: x["score"], reverse=True)

    return scored_mentors


def get_mentor_workload_summary(db: Session, mentor_id: uuid.UUID) -> Dict[str, Any]:
    """Get mentor's current workload summary"""
    try:
        # Get active assignments
        active_assignments = db.query(CompetitionMentorAssignment).filter(
            CompetitionMentorAssignment.mentor_id == mentor_id,
            CompetitionMentorAssignment.status.in_([
                MentorAssignmentStatusEnum.ASSIGNED,
                MentorAssignmentStatusEnum.ACCEPTED
            ])
        ).all()

        # Calculate workload metrics
        total_assignments = len(active_assignments)
        total_capacity = sum(assignment.workload_capacity for assignment in active_assignments)
        total_current_load = sum(assignment.current_workload for assignment in active_assignments)

        # Get completed assignments for experience metric
        completed_assignments = db.query(CompetitionMentorAssignment).filter(
            CompetitionMentorAssignment.mentor_id == mentor_id,
            CompetitionMentorAssignment.status == MentorAssignmentStatusEnum.COMPLETED
        ).count()

        return {
            "mentor_id": mentor_id,
            "active_assignments": total_assignments,
            "total_capacity": total_capacity,
            "current_workload": total_current_load,
            "available_capacity": total_capacity - total_current_load,
            "utilization_rate": (total_current_load / total_capacity * 100) if total_capacity > 0 else 0,
            "completed_assignments": completed_assignments,
            "is_available": total_assignments < 5 and (total_capacity - total_current_load) > 0
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting mentor workload: {str(e)}"
        )


def update_mentor_workload(db: Session, assignment_id: uuid.UUID, new_workload: int) -> CompetitionMentorAssignmentOut:
    """Update mentor's current workload for an assignment"""
    try:
        assignment = db.query(CompetitionMentorAssignment).filter(
            CompetitionMentorAssignment.id == assignment_id
        ).first()

        if not assignment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Assignment not found"
            )

        # Validate workload doesn't exceed capacity
        if new_workload > assignment.workload_capacity:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Workload cannot exceed capacity"
            )

        assignment.current_workload = new_workload
        assignment.progress_percentage = (new_workload / assignment.workload_capacity * 100) if assignment.workload_capacity > 0 else 0

        db.commit()
        db.refresh(assignment)

        return CompetitionMentorAssignmentOut.model_validate(assignment)

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating mentor workload: {str(e)}"
        )


def get_mentor_assignments_for_competition(db: Session, competition_id: uuid.UUID) -> List[CompetitionMentorAssignmentOut]:
    """Get all mentor assignments for a competition"""
    assignments = db.query(CompetitionMentorAssignment).filter(
        CompetitionMentorAssignment.competition_id == competition_id
    ).options(
        joinedload(CompetitionMentorAssignment.mentor),
        joinedload(CompetitionMentorAssignment.competition)
    ).all()
    
    return [CompetitionMentorAssignmentOut.model_validate(assignment) for assignment in assignments]


def update_mentor_assignment(db: Session, assignment_id: uuid.UUID, update_data: CompetitionMentorAssignmentUpdate) -> CompetitionMentorAssignmentOut:
    """Update mentor assignment details"""
    assignment = db.query(CompetitionMentorAssignment).filter(
        CompetitionMentorAssignment.id == assignment_id
    ).first()
    
    if not assignment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Assignment not found"
        )
    
    try:
        # Update fields
        update_dict = update_data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            setattr(assignment, field, value)
        
        db.commit()
        db.refresh(assignment)
        
        return CompetitionMentorAssignmentOut.model_validate(assignment)
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating assignment: {str(e)}"
        )


# ==================== HELPER FUNCTIONS ====================

def _determine_competition_status(competition: Event) -> str:
    """Determine competition status based on dates and submissions"""
    now = datetime.now(timezone.utc)
    
    if competition.status == EventStatusEnum.DRAFT:
        return "draft"
    elif competition.status == EventStatusEnum.CANCELLED:
        return "cancelled"
    elif now < competition.start_datetime:
        return "upcoming"
    elif now >= competition.start_datetime and now <= competition.end_datetime:
        return "active"
    else:
        return "completed"


def get_competition_statistics(db: Session, competition_id: uuid.UUID) -> Dict[str, Any]:
    """Get competition statistics"""
    competition = db.query(Event).filter(
        Event.id == competition_id,
        Event.is_competition == True
    ).first()

    if not competition:
        return {}

    # Get participant count
    total_participants = db.query(StudentExamAttempt).filter(
        StudentExamAttempt.exam_id == competition.competition_exam_id
    ).count()

    # Get submission count
    total_submissions = db.query(StudentExamAttempt).filter(
        StudentExamAttempt.exam_id == competition.competition_exam_id,
        StudentExamAttempt.submitted_at.isnot(None)
    ).count()

    # Get mentor count
    total_mentors = db.query(CompetitionMentorAssignment).filter(
        CompetitionMentorAssignment.competition_id == competition_id
    ).count()

    return {
        "total_participants": total_participants,
        "total_submissions": total_submissions,
        "total_mentors": total_mentors,
        "completion_rate": (total_submissions / total_participants * 100) if total_participants > 0 else 0
    }


# ==================== COMPETITION EXAM INTEGRATION ====================

def create_competition_exam(db: Session, exam_data: Dict[str, Any]) -> Exam:
    """Create an exam specifically for a competition"""
    try:
        # Set competition-specific exam settings
        exam_data.update({
            "is_competition_exam": True,
            "auto_grading_enabled": True,
            "manual_review_required": True,  # Competitions require mentor review
            "competition_specific_settings": {
                "proctoring_enabled": True,
                "randomize_questions": True,
                "security_level": "high",
                "cheating_detection": True
            }
        })

        exam = Exam(**exam_data)
        db.add(exam)
        db.commit()
        db.refresh(exam)

        return exam

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating competition exam: {str(e)}"
        )


def register_student_for_competition(db: Session, competition_id: uuid.UUID, student_id: uuid.UUID) -> Dict[str, Any]:
    """Register a student for a competition"""
    try:
        # Get competition details
        competition = db.query(Event).filter(
            Event.id == competition_id,
            Event.is_competition == True
        ).first()

        if not competition:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Competition not found"
            )

        # Check if competition is open for registration
        now = datetime.now(timezone.utc)
        if now >= competition.start_datetime:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Competition registration is closed"
            )

        # Check if student is already registered
        existing_attempt = db.query(StudentExamAttempt).filter(
            StudentExamAttempt.exam_id == competition.competition_exam_id,
            StudentExamAttempt.student_id == student_id
        ).first()

        if existing_attempt:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Student already registered for this competition"
            )

        # Check max attendees limit
        if competition.max_attendees:
            current_participants = db.query(StudentExamAttempt).filter(
                StudentExamAttempt.exam_id == competition.competition_exam_id
            ).count()

            if current_participants >= competition.max_attendees:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Competition is full"
                )

        # Create exam attempt record
        exam_attempt = StudentExamAttempt(
            exam_id=competition.competition_exam_id,
            student_id=student_id,
            started_at=None,  # Will be set when student starts the exam
            submitted_at=None,
            is_submitted=False,
            total_score=0,
            max_possible_score=0,
            time_taken=0,
            is_disqualified=False
        )

        db.add(exam_attempt)
        db.commit()
        db.refresh(exam_attempt)

        return {
            "message": "Successfully registered for competition",
            "competition_id": competition_id,
            "student_id": student_id,
            "attempt_id": exam_attempt.id
        }

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error registering for competition: {str(e)}"
        )


def get_student_competition_submission(db: Session, competition_id: uuid.UUID, student_id: uuid.UUID) -> Dict[str, Any]:
    """Get student's competition submission and evaluation status"""
    try:
        # Get competition details
        competition = db.query(Event).filter(
            Event.id == competition_id,
            Event.is_competition == True
        ).first()

        if not competition:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Competition not found"
            )

        # Get student's exam attempt
        attempt = db.query(StudentExamAttempt).filter(
            StudentExamAttempt.exam_id == competition.competition_exam_id,
            StudentExamAttempt.student_id == student_id
        ).first()

        if not attempt:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Student not registered for this competition"
            )

        # Get student's answers
        answers = db.query(StudentExamAnswer).filter(
            StudentExamAnswer.attempt_id == attempt.id
        ).all()

        # Get evaluation results if available
        from Models.Exam import StudentExamAIResult, StudentExamTeacherResult
        ai_result = db.query(StudentExamAIResult).filter(
            StudentExamAIResult.attempt_id == attempt.id
        ).first()

        teacher_results = db.query(StudentExamTeacherResult).filter(
            StudentExamTeacherResult.attempt_id == attempt.id
        ).all()

        return {
            "competition_id": competition_id,
            "student_id": student_id,
            "attempt": {
                "id": attempt.id,
                "started_at": attempt.started_at,
                "submitted_at": attempt.submitted_at,
                "is_submitted": attempt.is_submitted,
                "total_score": attempt.total_score,
                "max_possible_score": attempt.max_possible_score,
                "time_taken": attempt.time_taken,
                "is_disqualified": attempt.is_disqualified
            },
            "answers_count": len(answers),
            "ai_evaluation": {
                "completed": ai_result is not None,
                "score": ai_result.total_score if ai_result else None,
                "feedback": ai_result.overall_feedback if ai_result else None
            },
            "mentor_evaluations": [
                {
                    "mentor_id": result.teacher_id,
                    "score": result.total_score,
                    "feedback": result.overall_feedback,
                    "evaluated_at": result.checked_at
                }
                for result in teacher_results
            ],
            "evaluation_status": _get_evaluation_status(attempt, ai_result, teacher_results)
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting competition submission: {str(e)}"
        )


def get_competition_leaderboard(db: Session, competition_id: uuid.UUID, limit: int = 50) -> List[Dict[str, Any]]:
    """Get competition leaderboard"""
    try:
        # Get competition details
        competition = db.query(Event).filter(
            Event.id == competition_id,
            Event.is_competition == True
        ).first()

        if not competition:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Competition not found"
            )

        # Get all submitted attempts with scores
        attempts = db.query(StudentExamAttempt).filter(
            StudentExamAttempt.exam_id == competition.competition_exam_id,
            StudentExamAttempt.is_submitted == True,
            StudentExamAttempt.is_disqualified == False
        ).options(
            joinedload(StudentExamAttempt.student)
        ).order_by(
            desc(StudentExamAttempt.total_score),
            StudentExamAttempt.time_taken
        ).limit(limit).all()

        leaderboard = []
        for rank, attempt in enumerate(attempts, 1):
            leaderboard.append({
                "rank": rank,
                "student_id": attempt.student_id,
                "student_name": f"{attempt.student.first_name} {attempt.student.last_name}",
                "score": attempt.total_score,
                "max_score": attempt.max_possible_score,
                "percentage": (attempt.total_score / attempt.max_possible_score * 100) if attempt.max_possible_score > 0 else 0,
                "time_taken": attempt.time_taken,
                "submitted_at": attempt.submitted_at
            })

        return leaderboard

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting competition leaderboard: {str(e)}"
        )


def _get_evaluation_status(attempt, ai_result, teacher_results) -> str:
    """Determine evaluation status for a competition submission"""
    if not attempt.is_submitted:
        return "not_submitted"

    if attempt.is_disqualified:
        return "disqualified"

    if not ai_result:
        return "pending_ai_evaluation"

    if not teacher_results:
        return "pending_mentor_evaluation"

    return "evaluation_complete"


# ==================== MENTOR EVALUATION SYSTEM ====================

def get_mentor_assigned_submissions(db: Session, mentor_id: uuid.UUID, competition_id: uuid.UUID = None) -> List[Dict[str, Any]]:
    """Get submissions assigned to a mentor for evaluation"""
    try:
        # Get mentor's assignments
        query = db.query(CompetitionMentorAssignment).filter(
            CompetitionMentorAssignment.mentor_id == mentor_id,
            CompetitionMentorAssignment.status.in_([
                MentorAssignmentStatusEnum.ASSIGNED,
                MentorAssignmentStatusEnum.ACCEPTED
            ])
        )

        if competition_id:
            query = query.filter(CompetitionMentorAssignment.competition_id == competition_id)

        assignments = query.all()

        submissions = []
        for assignment in assignments:
            # Get competition details
            competition = db.query(Event).filter(
                Event.id == assignment.competition_id
            ).first()

            if not competition:
                continue

            # Get submitted attempts for this competition
            attempts = db.query(StudentExamAttempt).filter(
                StudentExamAttempt.exam_id == competition.competition_exam_id,
                StudentExamAttempt.is_submitted == True,
                StudentExamAttempt.is_disqualified == False
            ).options(
                joinedload(StudentExamAttempt.student)
            ).all()

            for attempt in attempts:
                # Check if mentor has already evaluated this submission
                from Models.Exam import StudentExamTeacherResult
                existing_evaluation = db.query(StudentExamTeacherResult).filter(
                    StudentExamTeacherResult.attempt_id == attempt.id,
                    StudentExamTeacherResult.teacher_id == mentor_id
                ).first()

                if not existing_evaluation:
                    submissions.append({
                        "competition_id": assignment.competition_id,
                        "competition_title": competition.title,
                        "attempt_id": attempt.id,
                        "student_id": attempt.student_id,
                        "student_name": f"{attempt.student.first_name} {attempt.student.last_name}",
                        "submitted_at": attempt.submitted_at,
                        "time_taken": attempt.time_taken,
                        "assignment_id": assignment.id
                    })

        return submissions

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting mentor submissions: {str(e)}"
        )


def submit_mentor_evaluation(db: Session, mentor_id: uuid.UUID, attempt_id: uuid.UUID, evaluation_data: Dict[str, Any]) -> Dict[str, Any]:
    """Submit mentor evaluation for a competition submission"""
    try:
        # Validate attempt exists
        attempt = db.query(StudentExamAttempt).filter(
            StudentExamAttempt.id == attempt_id
        ).first()

        if not attempt:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Submission not found"
            )

        # Validate mentor is assigned to this competition
        competition = db.query(Event).filter(
            Event.competition_exam_id == attempt.exam_id,
            Event.is_competition == True
        ).first()

        if not competition:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Competition not found"
            )

        assignment = db.query(CompetitionMentorAssignment).filter(
            CompetitionMentorAssignment.competition_id == competition.id,
            CompetitionMentorAssignment.mentor_id == mentor_id,
            CompetitionMentorAssignment.status.in_([
                MentorAssignmentStatusEnum.ASSIGNED,
                MentorAssignmentStatusEnum.ACCEPTED
            ])
        ).first()

        if not assignment:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Mentor not assigned to this competition"
            )

        # Check if mentor has already evaluated this submission
        from Models.Exam import StudentExamTeacherResult
        existing_evaluation = db.query(StudentExamTeacherResult).filter(
            StudentExamTeacherResult.attempt_id == attempt_id,
            StudentExamTeacherResult.teacher_id == mentor_id
        ).first()

        if existing_evaluation:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Mentor has already evaluated this submission"
            )

        # Create teacher result record
        teacher_result = StudentExamTeacherResult(
            attempt_id=attempt_id,
            teacher_id=mentor_id,
            total_score=evaluation_data.get("total_score", 0),
            max_possible_score=evaluation_data.get("max_possible_score", 0),
            overall_feedback=evaluation_data.get("overall_feedback", ""),
            question_feedbacks=evaluation_data.get("question_feedbacks", []),
            strengths=evaluation_data.get("strengths", []),
            improvements=evaluation_data.get("improvements", []),
            checked_at=datetime.now(timezone.utc)
        )

        db.add(teacher_result)

        # Update mentor workload
        assignment.current_workload += 1
        assignment.progress_percentage = (assignment.current_workload / assignment.workload_capacity * 100) if assignment.workload_capacity > 0 else 0

        # Update attempt score if this is the final evaluation
        # For now, use the mentor's score as the final score
        attempt.total_score = evaluation_data.get("total_score", 0)
        attempt.max_possible_score = evaluation_data.get("max_possible_score", 0)

        db.commit()
        db.refresh(teacher_result)

        return {
            "message": "Evaluation submitted successfully",
            "evaluation_id": teacher_result.id,
            "attempt_id": attempt_id,
            "mentor_id": mentor_id,
            "score": teacher_result.total_score
        }

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error submitting evaluation: {str(e)}"
        )


def get_submission_details_for_mentor(db: Session, mentor_id: uuid.UUID, attempt_id: uuid.UUID) -> Dict[str, Any]:
    """Get detailed submission information for mentor evaluation"""
    try:
        # Validate attempt exists
        attempt = db.query(StudentExamAttempt).filter(
            StudentExamAttempt.id == attempt_id
        ).options(
            joinedload(StudentExamAttempt.student),
            joinedload(StudentExamAttempt.exam)
        ).first()

        if not attempt:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Submission not found"
            )

        # Validate mentor is assigned to this competition
        competition = db.query(Event).filter(
            Event.competition_exam_id == attempt.exam_id,
            Event.is_competition == True
        ).first()

        if not competition:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Competition not found"
            )

        assignment = db.query(CompetitionMentorAssignment).filter(
            CompetitionMentorAssignment.competition_id == competition.id,
            CompetitionMentorAssignment.mentor_id == mentor_id
        ).first()

        if not assignment:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Mentor not assigned to this competition"
            )

        # Get student's answers
        answers = db.query(StudentExamAnswer).filter(
            StudentExamAnswer.attempt_id == attempt_id
        ).options(
            joinedload(StudentExamAnswer.question)
        ).all()

        # Get AI evaluation if available
        from Models.Exam import StudentExamAIResult
        ai_result = db.query(StudentExamAIResult).filter(
            StudentExamAIResult.attempt_id == attempt_id
        ).first()

        return {
            "attempt": {
                "id": attempt.id,
                "student_id": attempt.student_id,
                "student_name": f"{attempt.student.first_name} {attempt.student.last_name}",
                "exam_title": attempt.exam.title,
                "submitted_at": attempt.submitted_at,
                "time_taken": attempt.time_taken,
                "is_disqualified": attempt.is_disqualified
            },
            "competition": {
                "id": competition.id,
                "title": competition.title,
                "description": competition.description
            },
            "answers": [
                {
                    "question_id": answer.question_id,
                    "question_text": answer.question.question_text,
                    "question_type": answer.question.question_type,
                    "student_answer": answer.answer_text,
                    "marks_obtained": answer.marks_obtained,
                    "max_marks": answer.question.marks
                }
                for answer in answers
            ],
            "ai_evaluation": {
                "total_score": ai_result.total_score if ai_result else None,
                "overall_feedback": ai_result.overall_feedback if ai_result else None,
                "question_feedbacks": ai_result.question_feedbacks if ai_result else []
            } if ai_result else None
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting submission details: {str(e)}"
        )


# ==================== COMPETITION RESULTS AND LEADERBOARD ====================

def calculate_final_competition_results(db: Session, competition_id: uuid.UUID) -> Dict[str, Any]:
    """Calculate and finalize competition results"""
    try:
        # Get competition details
        competition = db.query(Event).filter(
            Event.id == competition_id,
            Event.is_competition == True
        ).first()

        if not competition:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Competition not found"
            )

        # Get all submitted attempts
        attempts = db.query(StudentExamAttempt).filter(
            StudentExamAttempt.exam_id == competition.competition_exam_id,
            StudentExamAttempt.is_submitted == True,
            StudentExamAttempt.is_disqualified == False
        ).options(
            joinedload(StudentExamAttempt.student)
        ).all()

        results = []
        for attempt in attempts:
            # Get mentor evaluations
            from Models.Exam import StudentExamTeacherResult, StudentExamAIResult
            mentor_evaluations = db.query(StudentExamTeacherResult).filter(
                StudentExamTeacherResult.attempt_id == attempt.id
            ).all()

            # Get AI evaluation
            ai_evaluation = db.query(StudentExamAIResult).filter(
                StudentExamAIResult.attempt_id == attempt.id
            ).first()

            # Calculate final score (average of mentor evaluations or AI if no mentors)
            final_score = 0
            max_score = 0

            if mentor_evaluations:
                # Use average of mentor scores
                total_mentor_score = sum(eval.total_score for eval in mentor_evaluations)
                total_max_score = sum(eval.max_possible_score for eval in mentor_evaluations)
                final_score = total_mentor_score / len(mentor_evaluations)
                max_score = total_max_score / len(mentor_evaluations)
            elif ai_evaluation:
                # Use AI score if no mentor evaluations
                final_score = ai_evaluation.total_score
                max_score = ai_evaluation.max_possible_score

            # Create or update competition result
            existing_result = db.query(CompetitionResult).filter(
                CompetitionResult.competition_id == competition_id,
                CompetitionResult.participant_id == attempt.student_id
            ).first()

            if existing_result:
                existing_result.final_score = final_score
                existing_result.max_possible_score = max_score
                existing_result.percentage_score = (final_score / max_score * 100) if max_score > 0 else 0
                existing_result.time_taken = attempt.time_taken
                existing_result.is_final = True
                result_record = existing_result
            else:
                result_record = CompetitionResult(
                    competition_id=competition_id,
                    participant_id=attempt.student_id,
                    final_score=final_score,
                    max_possible_score=max_score,
                    percentage_score=(final_score / max_score * 100) if max_score > 0 else 0,
                    time_taken=attempt.time_taken,
                    submission_time=attempt.submitted_at,
                    is_final=True,
                    is_published=False
                )
                db.add(result_record)

            results.append({
                "participant_id": attempt.student_id,
                "participant_name": f"{attempt.student.first_name} {attempt.student.last_name}",
                "final_score": final_score,
                "max_score": max_score,
                "percentage": (final_score / max_score * 100) if max_score > 0 else 0,
                "time_taken": attempt.time_taken,
                "mentor_evaluations": len(mentor_evaluations),
                "ai_evaluated": ai_evaluation is not None
            })

        # Sort by score and time
        results.sort(key=lambda x: (-x["percentage"], x["time_taken"]))

        # Assign ranks
        for i, result in enumerate(results, 1):
            result["rank"] = i

        db.commit()

        return {
            "competition_id": competition_id,
            "total_participants": len(results),
            "results_calculated": True,
            "results": results
        }

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error calculating competition results: {str(e)}"
        )


def publish_competition_results(db: Session, competition_id: uuid.UUID, institute_id: uuid.UUID) -> Dict[str, Any]:
    """Publish competition results to make them visible to participants"""
    try:
        # Validate competition belongs to institute
        competition = db.query(Event).filter(
            Event.id == competition_id,
            Event.is_competition == True,
            Event.institute_id == institute_id
        ).first()

        if not competition:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Competition not found or access denied"
            )

        # Update all results to published
        updated_count = db.query(CompetitionResult).filter(
            CompetitionResult.competition_id == competition_id,
            CompetitionResult.is_final == True
        ).update({
            "is_published": True,
            "published_at": datetime.now(timezone.utc)
        })

        # Update competition status
        competition.status = EventStatusEnum.COMPLETED

        db.commit()

        return {
            "message": "Competition results published successfully",
            "competition_id": competition_id,
            "published_results": updated_count,
            "published_at": datetime.now(timezone.utc)
        }

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error publishing competition results: {str(e)}"
        )


def get_competition_final_results(db: Session, competition_id: uuid.UUID, published_only: bool = True) -> List[Dict[str, Any]]:
    """Get final competition results with rankings"""
    try:
        # Get competition results
        query = db.query(CompetitionResult).filter(
            CompetitionResult.competition_id == competition_id,
            CompetitionResult.is_final == True
        )

        if published_only:
            query = query.filter(CompetitionResult.is_published == True)

        results = query.options(
            joinedload(CompetitionResult.participant)
        ).order_by(
            desc(CompetitionResult.percentage_score),
            CompetitionResult.time_taken
        ).all()

        final_results = []
        for rank, result in enumerate(results, 1):
            final_results.append({
                "rank": rank,
                "participant_id": result.participant_id,
                "participant_name": f"{result.participant.first_name} {result.participant.last_name}",
                "final_score": result.final_score,
                "max_possible_score": result.max_possible_score,
                "percentage_score": result.percentage_score,
                "time_taken": result.time_taken,
                "submission_time": result.submission_time,
                "is_published": result.is_published,
                "published_at": result.published_at
            })

        return final_results

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting competition results: {str(e)}"
        )


def get_competition_analytics(db: Session, competition_id: uuid.UUID) -> Dict[str, Any]:
    """Get comprehensive competition analytics"""
    try:
        # Get basic statistics
        stats = get_competition_statistics(db, competition_id)

        # Get score distribution
        results = db.query(CompetitionResult).filter(
            CompetitionResult.competition_id == competition_id,
            CompetitionResult.is_final == True
        ).all()

        if results:
            scores = [result.percentage_score for result in results]
            analytics = {
                **stats,
                "score_analytics": {
                    "average_score": sum(scores) / len(scores),
                    "highest_score": max(scores),
                    "lowest_score": min(scores),
                    "score_distribution": {
                        "90-100": len([s for s in scores if s >= 90]),
                        "80-89": len([s for s in scores if 80 <= s < 90]),
                        "70-79": len([s for s in scores if 70 <= s < 80]),
                        "60-69": len([s for s in scores if 60 <= s < 70]),
                        "below_60": len([s for s in scores if s < 60])
                    }
                },
                "evaluation_status": {
                    "ai_evaluated": len([r for r in results if r.ai_evaluated]),
                    "mentor_evaluated": len([r for r in results if r.mentor_evaluations > 0]),
                    "pending_evaluation": stats["total_submissions"] - len(results)
                }
            }
        else:
            analytics = {
                **stats,
                "score_analytics": None,
                "evaluation_status": {
                    "ai_evaluated": 0,
                    "mentor_evaluated": 0,
                    "pending_evaluation": stats["total_submissions"]
                }
            }

        return analytics

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting competition analytics: {str(e)}"
        )


# ==================== COMPETITION SECURITY AND MONITORING ====================

def enable_competition_security_features(db: Session, competition_id: uuid.UUID, security_settings: Dict[str, Any]) -> Dict[str, Any]:
    """Enable security features for a competition"""
    try:
        # Get competition details
        competition = db.query(Event).filter(
            Event.id == competition_id,
            Event.is_competition == True
        ).first()

        if not competition:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Competition not found"
            )

        # Update exam security settings
        exam = db.query(Exam).filter(
            Exam.id == competition.competition_exam_id
        ).first()

        if exam:
            current_settings = exam.competition_specific_settings or {}
            current_settings.update({
                "proctoring_enabled": security_settings.get("proctoring_enabled", True),
                "randomize_questions": security_settings.get("randomize_questions", True),
                "security_level": security_settings.get("security_level", "high"),
                "cheating_detection": security_settings.get("cheating_detection", True),
                "tab_switching_detection": security_settings.get("tab_switching_detection", True),
                "copy_paste_detection": security_settings.get("copy_paste_detection", True),
                "screenshot_detection": security_settings.get("screenshot_detection", True),
                "time_limit_strict": security_settings.get("time_limit_strict", True),
                "browser_lockdown": security_settings.get("browser_lockdown", False)
            })

            exam.competition_specific_settings = current_settings
            db.commit()

        return {
            "message": "Security features updated successfully",
            "competition_id": competition_id,
            "security_settings": current_settings
        }

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating security features: {str(e)}"
        )


def get_competition_security_violations(db: Session, competition_id: uuid.UUID) -> List[Dict[str, Any]]:
    """Get security violations for a competition"""
    try:
        # Get competition details
        competition = db.query(Event).filter(
            Event.id == competition_id,
            Event.is_competition == True
        ).first()

        if not competition:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Competition not found"
            )

        # Get all attempts for this competition
        attempts = db.query(StudentExamAttempt).filter(
            StudentExamAttempt.exam_id == competition.competition_exam_id
        ).options(
            joinedload(StudentExamAttempt.student)
        ).all()

        violations = []
        for attempt in attempts:
            # Check for disqualifications
            if attempt.is_disqualified:
                violations.append({
                    "student_id": attempt.student_id,
                    "student_name": f"{attempt.student.first_name} {attempt.student.last_name}",
                    "violation_type": "disqualification",
                    "severity": "high",
                    "timestamp": attempt.submitted_at or attempt.started_at,
                    "details": "Student was disqualified during the exam"
                })

            # Check for suspicious time patterns
            if attempt.time_taken and attempt.time_taken < 300:  # Less than 5 minutes
                violations.append({
                    "student_id": attempt.student_id,
                    "student_name": f"{attempt.student.first_name} {attempt.student.last_name}",
                    "violation_type": "suspicious_timing",
                    "severity": "medium",
                    "timestamp": attempt.submitted_at,
                    "details": f"Completed exam in {attempt.time_taken} seconds (unusually fast)"
                })

        # This could be enhanced with actual security logs from MongoDB
        # For now, return the basic violations we can detect from the database

        return violations

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting security violations: {str(e)}"
        )


def monitor_competition_session(db: Session, competition_id: uuid.UUID, student_id: uuid.UUID, session_data: Dict[str, Any]) -> Dict[str, Any]:
    """Monitor and log competition session activity"""
    try:
        # This would integrate with the existing exam session monitoring
        # For now, provide a basic implementation

        # Get competition details
        competition = db.query(Event).filter(
            Event.id == competition_id,
            Event.is_competition == True
        ).first()

        if not competition:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Competition not found"
            )

        # Get student's attempt
        attempt = db.query(StudentExamAttempt).filter(
            StudentExamAttempt.exam_id == competition.competition_exam_id,
            StudentExamAttempt.student_id == student_id
        ).first()

        if not attempt:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Student not registered for this competition"
            )

        # Log session activity (this would typically go to MongoDB)
        activity_log = {
            "competition_id": competition_id,
            "student_id": student_id,
            "attempt_id": attempt.id,
            "timestamp": datetime.now(timezone.utc),
            "activity_type": session_data.get("activity_type", "unknown"),
            "details": session_data.get("details", {}),
            "security_flags": session_data.get("security_flags", [])
        }

        # Check for security violations
        security_violations = []
        if session_data.get("tab_switched"):
            security_violations.append("tab_switching")
        if session_data.get("copy_detected"):
            security_violations.append("copy_paste")
        if session_data.get("screenshot_detected"):
            security_violations.append("screenshot")

        # If violations detected, increment strike count or disqualify
        if security_violations:
            # This would typically update the exam session record
            # For now, just log the violation
            activity_log["violations"] = security_violations

            # Auto-disqualify for serious violations
            if "screenshot" in security_violations or len(security_violations) >= 3:
                attempt.is_disqualified = True
                db.commit()

                return {
                    "status": "disqualified",
                    "message": "Student disqualified due to security violations",
                    "violations": security_violations
                }

        return {
            "status": "monitored",
            "message": "Session activity logged",
            "violations": security_violations,
            "activity_log": activity_log
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error monitoring competition session: {str(e)}"
        )


def get_competition_monitoring_dashboard(db: Session, competition_id: uuid.UUID) -> Dict[str, Any]:
    """Get real-time monitoring dashboard for a competition"""
    try:
        # Get competition details
        competition = db.query(Event).filter(
            Event.id == competition_id,
            Event.is_competition == True
        ).first()

        if not competition:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Competition not found"
            )

        # Get current active sessions
        active_attempts = db.query(StudentExamAttempt).filter(
            StudentExamAttempt.exam_id == competition.competition_exam_id,
            StudentExamAttempt.started_at.isnot(None),
            StudentExamAttempt.submitted_at.is_(None),
            StudentExamAttempt.is_disqualified == False
        ).options(
            joinedload(StudentExamAttempt.student)
        ).all()

        # Get completed submissions
        completed_attempts = db.query(StudentExamAttempt).filter(
            StudentExamAttempt.exam_id == competition.competition_exam_id,
            StudentExamAttempt.is_submitted == True
        ).count()

        # Get disqualified students
        disqualified_attempts = db.query(StudentExamAttempt).filter(
            StudentExamAttempt.exam_id == competition.competition_exam_id,
            StudentExamAttempt.is_disqualified == True
        ).count()

        # Get security violations
        violations = get_competition_security_violations(db, competition_id)

        # Prepare dashboard data
        dashboard = {
            "competition_id": competition_id,
            "competition_title": competition.title,
            "status": _determine_competition_status(competition),
            "monitoring_summary": {
                "active_participants": len(active_attempts),
                "completed_submissions": completed_attempts,
                "disqualified_participants": disqualified_attempts,
                "security_violations": len(violations),
                "high_severity_violations": len([v for v in violations if v["severity"] == "high"])
            },
            "active_sessions": [
                {
                    "student_id": attempt.student_id,
                    "student_name": f"{attempt.student.first_name} {attempt.student.last_name}",
                    "started_at": attempt.started_at,
                    "duration": (datetime.now(timezone.utc) - attempt.started_at).total_seconds() if attempt.started_at else 0,
                    "status": "active"
                }
                for attempt in active_attempts
            ],
            "recent_violations": violations[-10:],  # Last 10 violations
            "security_settings": competition.competition_exam.competition_specific_settings if competition.competition_exam else {}
        }

        return dashboard

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting monitoring dashboard: {str(e)}"
        )
