from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import logging

# Configure logging
logger = logging.getLogger(__name__)

# Make sure to URL-encode special characters in the password!
password = "qDl#XjWW4M]69O8"

SQLALCHEMY_DATABASE_URL = f"postgresql://postgres:{password}@*************:5432/edufair"
#SQLALCHEMY_DATABASE_URL = f"postgresql://postgres:1234@localhost:5432/edufair"
# Create engine with connection pool settings and SSL configuration for Hostinger
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    pool_size=5,           # Reduced for hosted database
    max_overflow=10,       # Reduced for hosted database
    pool_pre_ping=True,    # Test connections before use
    pool_recycle=1800,     # Recycle connections every 30 minutes (reduced from 1 hour)
    pool_timeout=30,       # Timeout for getting connection from pool
    echo_pool=False,       # Disable pool logging to reduce noise
    echo=False,
    # SSL and connection settings for Hostinger
    connect_args={
        "sslmode": "prefer",           # Use SSL if available, fallback to non-SSL
        "connect_timeout": 10,         # Connection timeout
        "application_name": "EduFair", # Application identifier
        "options": "-c statement_timeout=30000"  # 30 second statement timeout
    }
)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

def get_db():
    db = SessionLocal()
    try:
        yield db
        db.commit()   # auto-commit if nothing failed
    except Exception as e:
        logger.error(f"Database session error: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()