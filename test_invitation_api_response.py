#!/usr/bin/env python3
"""
Test script to verify invitation API response with complete mentor data
"""
import sys
import os
import uuid

# Add the EduFair app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'EduFair', 'app'))

try:
    from sqlalchemy.orm import Session
    from config.session import get_db
    from Models.users import User, MentorProfile, UserTypeEnum, MentorInstituteInvite
    from Cruds.Institute.Mentor import list_received_invitations
    
    def test_invitation_api_response():
        """Test invitation API response with complete mentor data"""
        print("🧪 Testing Invitation API Response")
        print("=" * 50)
        
        # Get database session
        db = next(get_db())
        
        try:
            # Find a mentor user who has received invitations
            mentor_user = db.query(User).filter(
                User.user_type == UserTypeEnum.mentor
            ).first()
            
            if not mentor_user:
                print("❌ No mentor users found in database")
                return
            
            print(f"✅ Found mentor user: {mentor_user.username} (ID: {mentor_user.id})")
            
            # Check if there are any invitations for this mentor
            invitations = db.query(MentorInstituteInvite).filter(
                MentorInstituteInvite.receiver_id == mentor_user.id,
                MentorInstituteInvite.received_by == "mentor"
            ).all()
            
            if not invitations:
                print("❌ No invitations found for this mentor")
                return
            
            print(f"✅ Found {len(invitations)} invitation(s) for this mentor")
            
            # Test the list_received_invitations function
            print(f"\n🔍 Testing list_received_invitations function...")
            invitation_response = list_received_invitations(
                db=db,
                receiver_id=mentor_user.id,
                page=1,
                size=20
            )
            
            if invitation_response and invitation_response.invitations:
                print(f"✅ Function returned {len(invitation_response.invitations)} invitation(s)")
                
                # Analyze the first invitation
                first_invitation = invitation_response.invitations[0]
                print(f"\n📊 Analyzing first invitation:")
                print(f"   - Invitation ID: {first_invitation.id}")
                print(f"   - Status: {first_invitation.status}")
                print(f"   - Proposed Rate: ${first_invitation.proposed_hourly_rate}")
                
                # Check sender details (should be institute)
                if first_invitation.sender:
                    print(f"\n👤 Sender Details (Institute):")
                    print(f"   - ID: {first_invitation.sender.id}")
                    print(f"   - Username: {first_invitation.sender.username}")
                    print(f"   - Email: {first_invitation.sender.email}")
                    print(f"   - Institute Name: {first_invitation.sender.institute_name}")
                    print(f"   - Institute Description: {first_invitation.sender.institute_description}")
                    print(f"   - Institute Website: {first_invitation.sender.institute_website}")
                    print(f"   - Institute City: {first_invitation.sender.institute_city}")
                    print(f"   - Institute Verified: {first_invitation.sender.institute_is_verified}")
                else:
                    print("❌ No sender details found")
                
                # Check receiver details (should be mentor with complete data)
                if first_invitation.receiver:
                    print(f"\n🎯 Receiver Details (Mentor):")
                    print(f"   - ID: {first_invitation.receiver.id}")
                    print(f"   - Username: {first_invitation.receiver.username}")
                    print(f"   - Email: {first_invitation.receiver.email}")
                    print(f"   - Profile Picture: {first_invitation.receiver.profile_picture}")
                    
                    print(f"\n📋 Mentor Profile Details:")
                    print(f"   - Bio: {first_invitation.receiver.mentor_bio}")
                    print(f"   - Experience: {first_invitation.receiver.mentor_experience_years} years")
                    print(f"   - Hourly Rate: ${first_invitation.receiver.mentor_hourly_rate}")
                    print(f"   - Full Name: {first_invitation.receiver.mentor_full_name}")
                    print(f"   - Current Position: {first_invitation.receiver.mentor_current_position}")
                    print(f"   - Current Organization: {first_invitation.receiver.mentor_current_organization}")
                    print(f"   - Phone: {first_invitation.receiver.mentor_phone}")
                    print(f"   - LinkedIn: {first_invitation.receiver.mentor_linkedin_url}")
                    print(f"   - Website: {first_invitation.receiver.mentor_website}")
                    print(f"   - Education: {first_invitation.receiver.mentor_education}")
                    print(f"   - Certifications: {first_invitation.receiver.mentor_certifications}")
                    print(f"   - Portfolio: {first_invitation.receiver.mentor_portfolio_url}")
                    print(f"   - Resume: {first_invitation.receiver.mentor_resume_url}")
                    print(f"   - Languages: {first_invitation.receiver.mentor_languages}")
                    print(f"   - Availability: {first_invitation.receiver.mentor_availability_hours}")
                    print(f"   - Verified: {first_invitation.receiver.mentor_is_verified}")
                    print(f"   - Verification Status: {first_invitation.receiver.mentor_verification_status}")
                    print(f"   - Rating: {first_invitation.receiver.mentor_rating}")
                    print(f"   - Total Reviews: {first_invitation.receiver.mentor_total_reviews}")
                    
                    # Check for null values
                    null_fields = []
                    important_fields = [
                        ('mentor_bio', first_invitation.receiver.mentor_bio),
                        ('mentor_experience_years', first_invitation.receiver.mentor_experience_years),
                        ('mentor_hourly_rate', first_invitation.receiver.mentor_hourly_rate),
                        ('mentor_full_name', first_invitation.receiver.mentor_full_name),
                        ('mentor_current_position', first_invitation.receiver.mentor_current_position),
                        ('mentor_current_organization', first_invitation.receiver.mentor_current_organization),
                        ('mentor_phone', first_invitation.receiver.mentor_phone),
                        ('mentor_linkedin_url', first_invitation.receiver.mentor_linkedin_url),
                        ('mentor_website', first_invitation.receiver.mentor_website),
                        ('mentor_education', first_invitation.receiver.mentor_education),
                        ('mentor_certifications', first_invitation.receiver.mentor_certifications),
                        ('mentor_languages', first_invitation.receiver.mentor_languages),
                        ('mentor_availability_hours', first_invitation.receiver.mentor_availability_hours),
                    ]
                    
                    for field_name, field_value in important_fields:
                        if field_value is None:
                            null_fields.append(field_name)
                    
                    if null_fields:
                        print(f"\n⚠️  Null fields detected: {', '.join(null_fields)}")
                    else:
                        print(f"\n✅ All important mentor fields have values!")
                        
                    # Check JSON fields specifically
                    if first_invitation.receiver.mentor_languages:
                        print(f"\n🔍 JSON Fields Analysis:")
                        print(f"   - Languages type: {type(first_invitation.receiver.mentor_languages)}")
                        print(f"   - Languages value: {first_invitation.receiver.mentor_languages}")
                        
                    if first_invitation.receiver.mentor_availability_hours:
                        print(f"   - Availability type: {type(first_invitation.receiver.mentor_availability_hours)}")
                        print(f"   - Availability keys: {list(first_invitation.receiver.mentor_availability_hours.keys()) if isinstance(first_invitation.receiver.mentor_availability_hours, dict) else 'Not a dict'}")
                        
                else:
                    print("❌ No receiver details found")
                    
                print(f"\n🎉 Test Summary:")
                if first_invitation.receiver and first_invitation.receiver.mentor_full_name:
                    print("✅ Invitation API is returning complete mentor profile data!")
                    print("✅ JSON fields are properly parsed")
                    print("✅ All mentor information is available for decision making")
                else:
                    print("❌ Invitation API is still missing mentor profile data")
                    
            else:
                print("❌ Function returned no invitations")
                
        except Exception as e:
            print(f"❌ Error during testing: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            db.close()
    
    if __name__ == "__main__":
        test_invitation_api_response()
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running this from the correct directory and all dependencies are installed")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
