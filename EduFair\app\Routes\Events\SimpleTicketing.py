"""
Simple Event Ticketing System

Clean, straightforward flow:
1. User browses events
2. User buys ticket (creates registration)
3. User gets confirmation with ticket details
4. Optional: Payment integration later

No unnecessary complexity, just core functionality.
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
from decimal import Decimal
from datetime import datetime, timezone
import uuid

# Import dependencies
from config.session import get_db
from config.deps import oauth2_scheme, get_current_user

# Import models
from Models.Events import Event, EventTicket, EventRegistration, RegistrationStatusEnum
from Models.users import User

# Import schemas
from pydantic import BaseModel, Field

router = APIRouter()


# ==================== SCHEMAS ====================

class TicketInfo(BaseModel):
    id: str
    name: str
    price: float
    available_quantity: int
    description: Optional[str] = None


class EventInfo(BaseModel):
    id: str
    title: str
    description: str
    start_datetime: str
    end_datetime: str
    location: str
    tickets: List[TicketInfo]


class BuyTicketRequest(BaseModel):
    ticket_id: str = Field(..., description="Ticket ID to purchase")
    quantity: int = Field(1, ge=1, le=10, description="Number of tickets (1-10)")
    attendee_name: str = Field(..., description="Name of attendee")
    attendee_email: str = Field(..., description="Email of attendee")
    attendee_phone: str = Field(..., description="Phone of attendee")


class TicketConfirmation(BaseModel):
    registration_id: str
    registration_number: str
    event_title: str
    ticket_name: str
    quantity: int
    total_amount: float
    attendee_name: str
    attendee_email: str
    event_date: str
    event_location: str
    status: str
    message: str


class MyTicket(BaseModel):
    registration_id: str
    registration_number: str
    event_title: str
    ticket_name: str
    quantity: int
    total_amount: float
    event_date: str
    event_location: str
    status: str
    purchased_at: str


# ==================== ENDPOINTS ====================

@router.get("/browse", response_model=List[EventInfo])
def browse_events(
    limit: int = 20,
    db: Session = Depends(get_db)
):
    """
    Browse available events with their tickets
    
    Simple endpoint to show all events with ticket information
    """
    try:
        # Get events with their tickets
        events = db.query(Event).filter(
            Event.start_datetime > datetime.now(timezone.utc)
        ).limit(limit).all()
        
        result = []
        for event in events:
            # Get tickets for this event
            tickets = db.query(EventTicket).filter(
                EventTicket.event_id == event.id
            ).all()
            
            ticket_info = []
            for ticket in tickets:
                available = ticket.total_quantity - ticket.sold_quantity if ticket.total_quantity else 999
                ticket_info.append(TicketInfo(
                    id=str(ticket.id),
                    name=ticket.name,
                    price=float(ticket.price),
                    available_quantity=available,
                    description=ticket.description
                ))
            
            result.append(EventInfo(
                id=str(event.id),
                title=event.title,
                description=event.description or "",
                start_datetime=event.start_datetime.isoformat(),
                end_datetime=event.end_datetime.isoformat() if event.end_datetime else "",
                location=event.location or "",
                tickets=ticket_info
            ))
        
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to browse events: {str(e)}"
        )


@router.post("/buy-ticket", response_model=TicketConfirmation)
def buy_ticket(
    request: BuyTicketRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Buy a ticket for an event
    
    Creates registration and returns confirmation details
    """
    current_user = get_current_user(token, db)
    
    try:
        # 1. Get ticket details (source of truth for price)
        ticket = db.query(EventTicket).filter(EventTicket.id == request.ticket_id).first()
        if not ticket:
            raise HTTPException(status_code=404, detail="Ticket not found")
        
        # 2. Get event details
        event = db.query(Event).filter(Event.id == ticket.event_id).first()
        if not event:
            raise HTTPException(status_code=404, detail="Event not found")
        
        # 3. Check availability
        available = ticket.total_quantity - ticket.sold_quantity if ticket.total_quantity else 999
        if available < request.quantity:
            raise HTTPException(status_code=400, detail=f"Only {available} tickets available")
        
        # 4. Calculate total (from database, never trust frontend)
        total_amount = ticket.price * request.quantity
        
        # 5. Generate registration number
        registration_number = f"TKT-{uuid.uuid4().hex[:8].upper()}"
        
        # 6. Create registration directly (bypass CRUD to avoid automatic payment creation)
        registration = EventRegistration(
            event_id=event.id,
            user_id=current_user.id,
            ticket_id=ticket.id,
            quantity=request.quantity,
            total_amount=Decimal('0.00'),  # Set to 0 to avoid payment creation
            currency="ZAR",
            status=RegistrationStatusEnum.CONFIRMED,  # Confirmed immediately for now
            registration_number=registration_number,
            attendee_info={
                "name": request.attendee_name,
                "email": request.attendee_email,
                "phone": request.attendee_phone
            },
            confirmed_at=datetime.now(timezone.utc)
        )

        db.add(registration)

        # 7. Update ticket sold quantity
        ticket.sold_quantity += request.quantity

        db.commit()
        db.refresh(registration)
        
        # 8. Return confirmation (show actual calculated amount, not stored amount)
        return TicketConfirmation(
            registration_id=str(registration.id),
            registration_number=registration_number,
            event_title=event.title,
            ticket_name=ticket.name,
            quantity=request.quantity,
            total_amount=float(total_amount),  # Use calculated amount
            attendee_name=request.attendee_name,
            attendee_email=request.attendee_email,
            event_date=event.start_datetime.strftime("%Y-%m-%d %H:%M"),
            event_location=event.location or "TBA",
            status="CONFIRMED",
            message=f"Ticket purchased successfully! Your registration number is {registration_number}"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to buy ticket: {str(e)}"
        )


@router.get("/my-tickets", response_model=List[MyTicket])
def get_my_tickets(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get user's purchased tickets
    
    Shows all tickets the user has purchased
    """
    current_user = get_current_user(token, db)
    
    try:
        # Get user's registrations with event and ticket details
        registrations = db.query(EventRegistration).filter(
            EventRegistration.user_id == current_user.id
        ).all()
        
        result = []
        for reg in registrations:
            # Get event and ticket details
            event = db.query(Event).filter(Event.id == reg.event_id).first()
            ticket = db.query(EventTicket).filter(EventTicket.id == reg.ticket_id).first()
            
            if event and ticket:
                # Calculate actual amount (since we store 0 to avoid payment creation)
                actual_amount = ticket.price * reg.quantity
                result.append(MyTicket(
                    registration_id=str(reg.id),
                    registration_number=reg.registration_number,
                    event_title=event.title,
                    ticket_name=ticket.name,
                    quantity=reg.quantity,
                    total_amount=float(actual_amount),
                    event_date=event.start_datetime.strftime("%Y-%m-%d %H:%M"),
                    event_location=event.location or "TBA",
                    status=reg.status.value,
                    purchased_at=reg.created_at.strftime("%Y-%m-%d %H:%M")
                ))
        
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get tickets: {str(e)}"
        )


@router.get("/ticket/{registration_id}", response_model=TicketConfirmation)
def get_ticket_details(
    registration_id: str,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get specific ticket details
    
    Shows detailed information for a specific ticket
    """
    current_user = get_current_user(token, db)
    
    try:
        # Get registration
        registration = db.query(EventRegistration).filter(
            EventRegistration.id == registration_id,
            EventRegistration.user_id == current_user.id  # Security: only user's own tickets
        ).first()
        
        if not registration:
            raise HTTPException(status_code=404, detail="Ticket not found")
        
        # Get event and ticket details
        event = db.query(Event).filter(Event.id == registration.event_id).first()
        ticket = db.query(EventTicket).filter(EventTicket.id == registration.ticket_id).first()
        
        if not event or not ticket:
            raise HTTPException(status_code=404, detail="Event or ticket details not found")
        
        attendee_info = registration.attendee_info or {}

        # Calculate actual amount (since we store 0 to avoid payment creation)
        actual_amount = ticket.price * registration.quantity

        return TicketConfirmation(
            registration_id=str(registration.id),
            registration_number=registration.registration_number,
            event_title=event.title,
            ticket_name=ticket.name,
            quantity=registration.quantity,
            total_amount=float(actual_amount),
            attendee_name=attendee_info.get("name", ""),
            attendee_email=attendee_info.get("email", ""),
            event_date=event.start_datetime.strftime("%Y-%m-%d %H:%M"),
            event_location=event.location or "TBA",
            status=registration.status.value,
            message=f"Ticket details for registration {registration.registration_number}"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get ticket details: {str(e)}"
        )
