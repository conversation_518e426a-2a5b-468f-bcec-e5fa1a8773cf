"""
Migration to make payment_method column nullable in event_payments table.

This migration addresses the issue where payment records are created with 
payment_method='pending' which is not a valid PaymentGatewayEnum value.
"""

from sqlalchemy import create_engine, text
import sys
import os

# Add the app directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.session import SQLALCHEMY_DATABASE_URL


def run_migration():
    """
    Make payment_method column nullable in event_payments table.
    """
    
    engine = create_engine(SQLALCHEMY_DATABASE_URL)
    
    with engine.connect() as connection:
        trans = connection.begin()
        
        try:
            print("Starting migration to make payment_method nullable...")
            
            # Check if event_payments table exists
            table_exists = connection.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'event_payments'
                );
            """)).scalar()
            
            if not table_exists:
                print("event_payments table does not exist. Skipping migration.")
                trans.commit()
                return
            
            # Check current column definition
            column_info = connection.execute(text("""
                SELECT is_nullable, data_type, udt_name
                FROM information_schema.columns 
                WHERE table_name = 'event_payments' 
                AND column_name = 'payment_method';
            """)).fetchone()
            
            if not column_info:
                print("payment_method column does not exist. Skipping migration.")
                trans.commit()
                return
            
            is_nullable, data_type, udt_name = column_info
            print(f"Current payment_method column: nullable={is_nullable}, type={data_type}, udt_name={udt_name}")
            
            if is_nullable == 'YES':
                print("payment_method column is already nullable!")
                trans.commit()
                return
            
            # Update any existing invalid payment_method values to NULL
            print("Updating invalid payment_method values to NULL...")
            result = connection.execute(text("""
                UPDATE event_payments 
                SET payment_method = NULL 
                WHERE payment_method NOT IN ('STRIPE', 'PAYPAL', 'RAZORPAY', 'PAYFAST', 'BANK_TRANSFER', 'CASH')
                OR payment_method IS NULL;
            """))
            print(f"Updated {result.rowcount} rows with invalid payment_method values")
            
            # Make the column nullable
            print("Making payment_method column nullable...")
            connection.execute(text("""
                ALTER TABLE event_payments 
                ALTER COLUMN payment_method DROP NOT NULL;
            """))
            
            # Verify the change
            print("Verifying column is now nullable...")
            updated_column_info = connection.execute(text("""
                SELECT is_nullable 
                FROM information_schema.columns 
                WHERE table_name = 'event_payments' 
                AND column_name = 'payment_method';
            """)).scalar()
            
            print(f"payment_method column is now nullable: {updated_column_info == 'YES'}")
            
            # Commit transaction
            trans.commit()
            print("Migration completed successfully!")
            
        except Exception as e:
            # Rollback on error
            trans.rollback()
            print(f"Migration failed: {e}")
            raise


def rollback_migration():
    """
    Rollback the migration (make payment_method NOT NULL again).
    Note: This will fail if there are NULL values in the column.
    """
    
    engine = create_engine(SQLALCHEMY_DATABASE_URL)
    
    with engine.connect() as connection:
        trans = connection.begin()
        
        try:
            print("Starting rollback to make payment_method NOT NULL...")
            
            # Check for NULL values
            null_count = connection.execute(text("""
                SELECT COUNT(*) 
                FROM event_payments 
                WHERE payment_method IS NULL;
            """)).scalar()
            
            if null_count > 0:
                print(f"Cannot rollback: {null_count} rows have NULL payment_method values")
                print("Please update these rows first or use a default value")
                trans.rollback()
                return
            
            # Make the column NOT NULL
            print("Making payment_method column NOT NULL...")
            connection.execute(text("""
                ALTER TABLE event_payments 
                ALTER COLUMN payment_method SET NOT NULL;
            """))
            
            # Commit transaction
            trans.commit()
            print("Rollback completed successfully!")
            
        except Exception as e:
            # Rollback on error
            trans.rollback()
            print(f"Rollback failed: {e}")
            raise


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "rollback":
        rollback_migration()
    else:
        run_migration()
