"""
User Event Actions Routes for EduFair Platform

This module contains API routes for user event actions including:
- Event registration cancellation
- Ticket transfers and modifications
- Event history tracking
- QR code and ticket downloads
"""

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID
from datetime import datetime, timezone, timedelta

# Import dependencies
from config.session import get_db
from config.deps import oauth2_scheme, get_current_user

# Import models
from Models.Events import EventRegistration, Event, RegistrationStatusEnum, PaymentStatusEnum
from Models.users import User

# Import schemas
from Schemas.Users.EventDashboard import UserEventDetails
from pydantic import BaseModel

router = APIRouter()


class CancellationRequest(BaseModel):
    """Request to cancel a registration"""
    reason: Optional[str] = None
    request_refund: bool = True


class CancellationResponse(BaseModel):
    """Response for registration cancellation"""
    success: bool
    message: str
    refund_eligible: bool = False
    refund_amount: Optional[float] = None
    cancellation_fee: Optional[float] = None
    processing_time: Optional[str] = None


class TicketDownloadResponse(BaseModel):
    """Response for ticket download"""
    ticket_url: str
    qr_code: str
    check_in_code: str
    event_title: str
    event_date: datetime
    event_location: str


class EventHistoryEntry(BaseModel):
    """Single entry in user's event history"""
    action_type: str  # "registered", "cancelled", "attended", "refunded"
    action_date: datetime
    event_title: str
    event_id: UUID
    registration_id: UUID
    description: str
    amount_involved: Optional[float] = None


class EventHistoryResponse(BaseModel):
    """User's complete event history"""
    user_id: UUID
    total_entries: int
    history: List[EventHistoryEntry]
    summary: dict


# ==================== USER EVENT ACTIONS ====================

@router.post("/registrations/{registration_id}/cancel", response_model=CancellationResponse)
def cancel_my_registration(
    registration_id: UUID,
    cancellation_request: CancellationRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Cancel user's event registration.
    
    Handles refund eligibility based on event timing and cancellation policy.
    Automatically processes refunds where applicable.
    """
    current_user = get_current_user(token, db)
    
    # Get registration
    registration = db.query(EventRegistration).filter(
        EventRegistration.id == registration_id,
        EventRegistration.user_id == current_user.id
    ).first()
    
    if not registration:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Registration not found"
        )
    
    if registration.status == RegistrationStatusEnum.CANCELLED:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Registration is already cancelled"
        )
    
    # Get event details
    event = db.query(Event).filter(Event.id == registration.event_id).first()
    if not event:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Event not found"
        )
    
    # Check if cancellation is allowed
    now = datetime.now(timezone.utc)
    hours_until_event = (event.start_datetime - now).total_seconds() / 3600
    
    if hours_until_event < 24:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot cancel registration less than 24 hours before event"
        )
    
    # Determine refund eligibility
    refund_eligible = False
    refund_amount = 0.0
    cancellation_fee = 0.0
    processing_time = "3-5 business days"
    
    if (registration.payment_status == PaymentStatusEnum.COMPLETED and 
        cancellation_request.request_refund):
        
        if hours_until_event >= 72:  # 3 days
            # Full refund
            refund_eligible = True
            refund_amount = float(registration.total_amount)
        elif hours_until_event >= 48:  # 2 days
            # 75% refund
            refund_eligible = True
            refund_amount = float(registration.total_amount) * 0.75
            cancellation_fee = float(registration.total_amount) * 0.25
        elif hours_until_event >= 24:  # 1 day
            # 50% refund
            refund_eligible = True
            refund_amount = float(registration.total_amount) * 0.50
            cancellation_fee = float(registration.total_amount) * 0.50
    
    try:
        # Update registration status
        registration.status = RegistrationStatusEnum.CANCELLED
        registration.cancelled_at = now
        
        # Update payment status if refund is processed
        if refund_eligible and refund_amount > 0:
            registration.payment_status = PaymentStatusEnum.REFUNDED
        
        db.commit()
        
        return CancellationResponse(
            success=True,
            message="Registration cancelled successfully",
            refund_eligible=refund_eligible,
            refund_amount=refund_amount,
            cancellation_fee=cancellation_fee,
            processing_time=processing_time if refund_eligible else None
        )
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to cancel registration: {str(e)}"
        )


@router.get("/registrations/{registration_id}/ticket", response_model=TicketDownloadResponse)
def download_my_ticket(
    registration_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Download ticket for a confirmed registration.
    
    Returns QR code, check-in code, and downloadable ticket URL.
    """
    current_user = get_current_user(token, db)
    
    # Get registration with event details
    registration = db.query(EventRegistration).join(Event).filter(
        EventRegistration.id == registration_id,
        EventRegistration.user_id == current_user.id,
        EventRegistration.status.in_([RegistrationStatusEnum.CONFIRMED, RegistrationStatusEnum.ATTENDED])
    ).first()
    
    if not registration:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Confirmed registration not found"
        )
    
    event = registration.event
    
    # Generate ticket URL (in real implementation, this would generate a PDF or image)
    ticket_url = f"/api/tickets/{registration_id}/download"
    
    return TicketDownloadResponse(
        ticket_url=ticket_url,
        qr_code=registration.qr_code or f"QR_{registration.registration_number}",
        check_in_code=registration.check_in_code or f"CHK_{registration.registration_number}",
        event_title=event.title,
        event_date=event.start_datetime,
        event_location=event.location or "TBD"
    )


@router.get("/history", response_model=EventHistoryResponse)
def get_my_event_history(
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    action_type: Optional[str] = Query(None, description="Filter by action type"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get complete event history for the user.
    
    Includes all registrations, cancellations, refunds, and attendance records.
    """
    current_user = get_current_user(token, db)
    
    # Get all registrations for the user
    registrations_query = db.query(EventRegistration).join(Event).filter(
        EventRegistration.user_id == current_user.id
    ).order_by(EventRegistration.registered_at.desc())
    
    if action_type:
        if action_type == "registered":
            registrations_query = registrations_query.filter(
                EventRegistration.status.in_([
                    RegistrationStatusEnum.PENDING, 
                    RegistrationStatusEnum.CONFIRMED
                ])
            )
        elif action_type == "cancelled":
            registrations_query = registrations_query.filter(
                EventRegistration.status == RegistrationStatusEnum.CANCELLED
            )
        elif action_type == "attended":
            registrations_query = registrations_query.filter(
                EventRegistration.status == RegistrationStatusEnum.ATTENDED
            )
    
    total_count = registrations_query.count()
    registrations = registrations_query.offset(skip).limit(limit).all()
    
    # Build history entries
    history_entries = []
    
    for registration in registrations:
        event = registration.event
        
        # Registration entry
        history_entries.append(EventHistoryEntry(
            action_type="registered",
            action_date=registration.registered_at,
            event_title=event.title,
            event_id=event.id,
            registration_id=registration.id,
            description=f"Registered for {event.title}",
            amount_involved=float(registration.total_amount) if registration.total_amount > 0 else None
        ))
        
        # Confirmation entry
        if registration.confirmed_at:
            history_entries.append(EventHistoryEntry(
                action_type="confirmed",
                action_date=registration.confirmed_at,
                event_title=event.title,
                event_id=event.id,
                registration_id=registration.id,
                description=f"Registration confirmed for {event.title}"
            ))
        
        # Attendance entry
        if registration.attended_at:
            history_entries.append(EventHistoryEntry(
                action_type="attended",
                action_date=registration.attended_at,
                event_title=event.title,
                event_id=event.id,
                registration_id=registration.id,
                description=f"Attended {event.title}"
            ))
        
        # Cancellation entry
        if registration.cancelled_at:
            refund_amount = None
            if registration.payment_status == PaymentStatusEnum.REFUNDED:
                refund_amount = float(registration.total_amount)
            
            history_entries.append(EventHistoryEntry(
                action_type="cancelled",
                action_date=registration.cancelled_at,
                event_title=event.title,
                event_id=event.id,
                registration_id=registration.id,
                description=f"Cancelled registration for {event.title}",
                amount_involved=refund_amount
            ))
    
    # Sort by date (most recent first)
    history_entries.sort(key=lambda x: x.action_date, reverse=True)
    
    # Create summary
    summary = {
        "total_registrations": len([e for e in history_entries if e.action_type == "registered"]),
        "total_attended": len([e for e in history_entries if e.action_type == "attended"]),
        "total_cancelled": len([e for e in history_entries if e.action_type == "cancelled"]),
        "total_spent": sum([e.amount_involved for e in history_entries if e.amount_involved and e.action_type == "registered"], 0),
        "total_refunded": sum([e.amount_involved for e in history_entries if e.amount_involved and e.action_type == "cancelled"], 0)
    }
    
    return EventHistoryResponse(
        user_id=current_user.id,
        total_entries=len(history_entries),
        history=history_entries[skip:skip+limit],
        summary=summary
    )


@router.get("/registrations/{registration_id}/status")
def get_registration_status(
    registration_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get detailed status of a specific registration.
    """
    current_user = get_current_user(token, db)
    
    registration = db.query(EventRegistration).join(Event).filter(
        EventRegistration.id == registration_id,
        EventRegistration.user_id == current_user.id
    ).first()
    
    if not registration:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Registration not found"
        )
    
    event = registration.event
    now = datetime.now(timezone.utc)
    
    # Calculate time-based information
    time_until_event = None
    can_cancel = False
    can_modify = False
    
    if event.start_datetime > now:
        time_until_event = (event.start_datetime - now).total_seconds()
        hours_until = time_until_event / 3600
        
        can_cancel = (
            hours_until >= 24 and 
            registration.status in [RegistrationStatusEnum.PENDING, RegistrationStatusEnum.CONFIRMED]
        )
        
        can_modify = (
            hours_until >= 48 and 
            registration.status in [RegistrationStatusEnum.PENDING, RegistrationStatusEnum.CONFIRMED]
        )
    
    return {
        "registration_id": registration.id,
        "registration_number": registration.registration_number,
        "status": registration.status.value,
        "payment_status": registration.payment_status.value,
        "event_title": event.title,
        "event_start": event.start_datetime.isoformat(),
        "event_location": event.location,
        "registered_at": registration.registered_at.isoformat(),
        "confirmed_at": registration.confirmed_at.isoformat() if registration.confirmed_at else None,
        "attended_at": registration.attended_at.isoformat() if registration.attended_at else None,
        "cancelled_at": registration.cancelled_at.isoformat() if registration.cancelled_at else None,
        "total_amount": float(registration.total_amount),
        "currency": registration.currency,
        "qr_code": registration.qr_code,
        "check_in_code": registration.check_in_code,
        "can_cancel": can_cancel,
        "can_modify": can_modify,
        "time_until_event_hours": time_until_event / 3600 if time_until_event else None
    }
