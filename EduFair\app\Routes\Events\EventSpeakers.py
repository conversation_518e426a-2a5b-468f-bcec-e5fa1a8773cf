"""
Event Speaker Routes for EduFair Platform

This module contains all API routes for Event Speaker management including:
- Speaker CRUD operations
- Speaker filtering and searching
"""

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from typing import List
from uuid import UUID

# Import dependencies
from config.session import get_db
from config.deps import oauth2_scheme, get_current_user
from config.permission import require_type

# Import CRUD operations
from Cruds.Events.Events import (
    create_event_speaker, get_event_speaker_by_id, get_all_event_speakers,
    update_event_speaker, delete_event_speaker
)

# Import schemas
from Schemas.Events.Events import (
    EventSpeakerCreate, EventSpeakerUpdate, EventSpeakerOut
)

router = APIRouter()


# ==================== EVENT SPEAKER ROUTES ====================

@router.post("/", response_model=EventSpeakerOut)
def create_event_speaker_endpoint(
    speaker: EventSpeakerCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Create a new event speaker.
    
    Only institutes can create event speakers.
    """
    return create_event_speaker(db, speaker)


@router.get("/", response_model=List[EventSpeakerOut])
def get_all_event_speakers_endpoint(
    skip: int = Query(0, ge=0, description="Number of speakers to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of speakers to return"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get all event speakers.
    
    Requires authentication.
    """
    return get_all_event_speakers(db, skip, limit)


@router.get("/{speaker_id}", response_model=EventSpeakerOut)
def get_event_speaker_by_id_endpoint(
    speaker_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get event speaker by ID.
    
    Requires authentication.
    """
    return get_event_speaker_by_id(db, speaker_id)


@router.put("/{speaker_id}", response_model=EventSpeakerOut)
def update_event_speaker_endpoint(
    speaker_id: UUID,
    speaker_update: EventSpeakerUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Update an existing event speaker.
    
    Only institutes can update event speakers.
    """
    return update_event_speaker(db, speaker_id, speaker_update)


@router.delete("/{speaker_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_event_speaker_endpoint(
    speaker_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Delete an event speaker.
    
    Only institutes can delete event speakers.
    """
    delete_event_speaker(db, speaker_id)
    return None
