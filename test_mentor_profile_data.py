#!/usr/bin/env python3
"""
Test script to verify mentor profile data is being fetched correctly
"""
import sys
import os
import uuid
from typing import Optional

# Add the EduFair app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'EduFair', 'app'))

try:
    from sqlalchemy.orm import Session
    from config.session import get_db
    from Models.users import User, MentorProfile, UserTypeEnum
    from Cruds.Institute.Mentor import _get_user_profile_details
    
    def test_mentor_profile_data():
        """Test mentor profile data fetching"""
        print("🔍 Testing Mentor Profile Data Fetching")
        print("=" * 50)
        
        # Get database session
        db = next(get_db())
        
        try:
            # Find a mentor user
            mentor_user = db.query(User).filter(
                User.user_type == UserTypeEnum.mentor
            ).first()
            
            if not mentor_user:
                print("❌ No mentor users found in database")
                return
            
            print(f"✅ Found mentor user: {mentor_user.username} (ID: {mentor_user.id})")
            
            # Check if mentor profile exists
            mentor_profile = db.query(MentorProfile).filter(
                MentorProfile.user_id == mentor_user.id
            ).first()
            
            if mentor_profile:
                print(f"✅ <PERSON>tor profile exists")
                print(f"   - Bio: {mentor_profile.bio}")
                print(f"   - Experience: {mentor_profile.experience_years} years")
                print(f"   - Hourly Rate: ${mentor_profile.hourly_rate}")
                print(f"   - Full Name: {mentor_profile.full_name}")
                print(f"   - Current Position: {mentor_profile.current_position}")
                print(f"   - Current Organization: {mentor_profile.current_organization}")
                print(f"   - Phone: {mentor_profile.phone}")
                print(f"   - LinkedIn: {mentor_profile.linkedin_url}")
                print(f"   - Website: {mentor_profile.website}")
                print(f"   - Education: {mentor_profile.education}")
                print(f"   - Certifications: {mentor_profile.certifications}")
                print(f"   - Languages: {mentor_profile.languages}")
                print(f"   - Availability: {mentor_profile.availability_hours}")
                print(f"   - Is Verified: {mentor_profile.is_verified}")
                print(f"   - Verification Status: {mentor_profile.verification_status}")
                print(f"   - Rating: {mentor_profile.rating}")
                print(f"   - Total Reviews: {mentor_profile.total_reviews}")
            else:
                print("❌ No mentor profile found")
                
                # Create a sample mentor profile for testing
                print("🔧 Creating sample mentor profile...")
                sample_profile = MentorProfile(
                    user_id=mentor_user.id,
                    bio="Experienced software developer with 5+ years in web development",
                    experience_years=5,
                    hourly_rate=50.00,
                    full_name="John Doe",
                    current_position="Senior Developer",
                    current_organization="Tech Corp",
                    phone="******-0123",
                    linkedin_url="https://linkedin.com/in/johndoe",
                    website="https://johndoe.dev",
                    education="BS Computer Science",
                    certifications="AWS Certified Developer",
                    languages='["English", "Spanish"]',
                    availability_hours='{"monday": ["09:00-17:00"], "tuesday": ["09:00-17:00"]}',
                    is_verified=True,
                    verification_status="verified",
                    rating=4.8,
                    total_reviews=25
                )
                db.add(sample_profile)
                db.commit()
                db.refresh(sample_profile)
                print("✅ Sample mentor profile created")
                mentor_profile = sample_profile
            
            # Test the _get_user_profile_details function
            print(f"\n🧪 Testing _get_user_profile_details function...")
            invitation_details = _get_user_profile_details(db, mentor_user.id, "mentor")
            
            if invitation_details:
                print("✅ Function returned data:")
                print(f"   - ID: {invitation_details.id}")
                print(f"   - Username: {invitation_details.username}")
                print(f"   - Email: {invitation_details.email}")
                print(f"   - Mentor Bio: {invitation_details.mentor_bio}")
                print(f"   - Mentor Experience: {invitation_details.mentor_experience_years}")
                print(f"   - Mentor Rate: {invitation_details.mentor_hourly_rate}")
                print(f"   - Mentor Full Name: {invitation_details.mentor_full_name}")
                print(f"   - Mentor Position: {invitation_details.mentor_current_position}")
                print(f"   - Mentor Organization: {invitation_details.mentor_current_organization}")
                print(f"   - Mentor Phone: {invitation_details.mentor_phone}")
                print(f"   - Mentor LinkedIn: {invitation_details.mentor_linkedin_url}")
                print(f"   - Mentor Website: {invitation_details.mentor_website}")
                print(f"   - Mentor Education: {invitation_details.mentor_education}")
                print(f"   - Mentor Certifications: {invitation_details.mentor_certifications}")
                print(f"   - Mentor Languages: {invitation_details.mentor_languages}")
                print(f"   - Mentor Availability: {invitation_details.mentor_availability_hours}")
                print(f"   - Mentor Verified: {invitation_details.mentor_is_verified}")
                print(f"   - Mentor Verification Status: {invitation_details.mentor_verification_status}")
                print(f"   - Mentor Rating: {invitation_details.mentor_rating}")
                print(f"   - Mentor Total Reviews: {invitation_details.mentor_total_reviews}")
                
                # Check for null values
                null_fields = []
                if invitation_details.mentor_bio is None:
                    null_fields.append("mentor_bio")
                if invitation_details.mentor_experience_years is None:
                    null_fields.append("mentor_experience_years")
                if invitation_details.mentor_hourly_rate is None:
                    null_fields.append("mentor_hourly_rate")
                if invitation_details.mentor_full_name is None:
                    null_fields.append("mentor_full_name")
                if invitation_details.mentor_current_position is None:
                    null_fields.append("mentor_current_position")
                if invitation_details.mentor_current_organization is None:
                    null_fields.append("mentor_current_organization")
                
                if null_fields:
                    print(f"⚠️  Null fields detected: {', '.join(null_fields)}")
                else:
                    print("✅ All important fields have values")
                    
            else:
                print("❌ Function returned None")
            
            print(f"\n📊 Database Query Test:")
            # Test direct database query
            direct_query = db.query(MentorProfile).filter(MentorProfile.user_id == mentor_user.id).first()
            if direct_query:
                print("✅ Direct query successful")
                print(f"   - Profile ID: {direct_query.user_id}")
                print(f"   - Has bio: {direct_query.bio is not None}")
                print(f"   - Has full_name: {direct_query.full_name is not None}")
                print(f"   - Has experience: {direct_query.experience_years is not None}")
            else:
                print("❌ Direct query failed")
                
        except Exception as e:
            print(f"❌ Error during testing: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            db.close()
    
    if __name__ == "__main__":
        test_mentor_profile_data()
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running this from the correct directory and all dependencies are installed")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
