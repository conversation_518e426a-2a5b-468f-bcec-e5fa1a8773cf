"""
PayFast Payment Gateway Schemas for EduFair Platform

This module contains Pydantic schemas for PayFast payment integration including:
- Payment creation requests
- Payment form data
- Webhook notifications
- Payment responses
"""

from typing import Dict, Any, Optional
from pydantic import BaseModel, Field, validator
from decimal import Decimal
from uuid import UUID
from datetime import datetime

from Models.Events import PaymentStatusEnum


# ===== PAYFAST PAYMENT REQUEST SCHEMAS =====

class PayFastEventPaymentRequest(BaseModel):
    """Request to create PayFast payment for event registration"""
    
    registration_id: UUID = Field(..., description="Event registration ID")
    amount: Decimal = Field(..., gt=0, description="Payment amount")
    currency: str = Field("ZAR", description="Currency code (default: ZAR)")
    user_email: Optional[str] = Field(None, description="User email address")
    user_name: Optional[str] = Field(None, description="User full name")
    return_url: Optional[str] = Field(None, description="Custom return URL after successful payment")
    cancel_url: Optional[str] = Field(None, description="Custom cancel URL after cancelled payment")
    
    @validator('currency')
    def validate_currency(cls, v):
        if v not in ['ZAR', 'USD', 'EUR', 'GBP']:
            raise ValueError('Currency must be one of: ZAR, USD, EUR, GBP')
        return v


class PayFastSubscriptionPaymentRequest(BaseModel):
    """Request to create PayFast payment for subscription"""
    
    subscription_id: UUID = Field(..., description="Subscription ID")
    amount: Decimal = Field(..., gt=0, description="Payment amount")
    currency: str = Field("ZAR", description="Currency code (default: ZAR)")
    user_email: Optional[str] = Field(None, description="User email address")
    user_name: Optional[str] = Field(None, description="User full name")
    return_url: Optional[str] = Field(None, description="Custom return URL after successful payment")
    cancel_url: Optional[str] = Field(None, description="Custom cancel URL after cancelled payment")
    
    @validator('currency')
    def validate_currency(cls, v):
        if v not in ['ZAR', 'USD', 'EUR', 'GBP']:
            raise ValueError('Currency must be one of: ZAR, USD, EUR, GBP')
        return v


# ===== PAYFAST PAYMENT RESPONSE SCHEMAS =====

class PayFastPaymentFormData(BaseModel):
    """PayFast payment form data for frontend integration"""
    
    merchant_id: str
    merchant_key: str
    return_url: str
    cancel_url: str
    notify_url: str
    name_first: str
    name_last: str
    email_address: str
    m_payment_id: str
    amount: str
    item_name: str
    item_description: str
    custom_str1: str = ""
    custom_str2: str = ""
    custom_str3: str = ""
    signature: str


class PayFastPaymentResponse(BaseModel):
    """Response from PayFast payment creation"""
    
    payment_id: UUID = Field(..., description="Internal payment ID")
    payfast_payment_id: str = Field(..., description="PayFast payment ID")
    payment_url: str = Field(..., description="PayFast payment URL")
    payment_data: PayFastPaymentFormData = Field(..., description="Payment form data")
    status: str = Field(..., description="Payment status")
    
    class Config:
        from_attributes = True


class PayFastSubscriptionPaymentResponse(BaseModel):
    """Response from PayFast subscription payment creation"""
    
    payfast_payment_id: str = Field(..., description="PayFast payment ID")
    payment_url: str = Field(..., description="PayFast payment URL")
    payment_data: PayFastPaymentFormData = Field(..., description="Payment form data")
    status: str = Field(..., description="Payment status")


# ===== PAYFAST WEBHOOK SCHEMAS =====

class PayFastWebhookNotification(BaseModel):
    """PayFast webhook notification data"""
    
    m_payment_id: str = Field(..., description="Merchant payment ID")
    pf_payment_id: str = Field(..., description="PayFast payment ID")
    payment_status: str = Field(..., description="Payment status (COMPLETE, FAILED, CANCELLED)")
    item_name: str = Field(..., description="Item name")
    item_description: Optional[str] = Field(None, description="Item description")
    amount_gross: str = Field(..., description="Gross amount")
    amount_fee: str = Field(..., description="PayFast fee")
    amount_net: str = Field(..., description="Net amount")
    custom_str1: Optional[str] = Field(None, description="Custom string 1")
    custom_str2: Optional[str] = Field(None, description="Custom string 2")
    custom_str3: Optional[str] = Field(None, description="Custom string 3")
    name_first: Optional[str] = Field(None, description="Customer first name")
    name_last: Optional[str] = Field(None, description="Customer last name")
    email_address: Optional[str] = Field(None, description="Customer email")
    merchant_id: str = Field(..., description="Merchant ID")
    signature: str = Field(..., description="PayFast signature")
    
    @validator('payment_status')
    def validate_payment_status(cls, v):
        if v not in ['COMPLETE', 'FAILED', 'CANCELLED']:
            raise ValueError('Payment status must be one of: COMPLETE, FAILED, CANCELLED')
        return v


class PayFastWebhookResponse(BaseModel):
    """Response from PayFast webhook processing"""
    
    payment_id: Optional[UUID] = Field(None, description="Internal payment ID")
    subscription_id: Optional[UUID] = Field(None, description="Subscription ID")
    status: str = Field(..., description="Processing status")
    message: str = Field(..., description="Processing message")


# ===== PAYFAST REFUND SCHEMAS =====

class PayFastRefundRequest(BaseModel):
    """Request to refund PayFast payment"""
    
    payment_id: UUID = Field(..., description="Payment ID to refund")
    refund_amount: Optional[Decimal] = Field(None, gt=0, description="Refund amount (full refund if not specified)")
    reason: str = Field("Refund requested", description="Refund reason")


class PayFastRefundResponse(BaseModel):
    """Response from PayFast refund request"""
    
    payment_id: UUID = Field(..., description="Payment ID")
    refund_amount: Decimal = Field(..., description="Refund amount")
    status: str = Field(..., description="Refund status")
    message: str = Field(..., description="Refund message")
    note: Optional[str] = Field(None, description="Additional notes")


# ===== PAYFAST STATUS SCHEMAS =====

class PayFastPaymentStatusResponse(BaseModel):
    """PayFast payment status response"""
    
    payment_id: UUID
    payfast_payment_id: Optional[str]
    status: PaymentStatusEnum
    amount: Decimal
    currency: str
    created_at: datetime
    processed_at: Optional[datetime]
    failed_at: Optional[datetime]
    failure_reason: Optional[str]
    gateway_response: Optional[Dict[str, Any]]
    
    class Config:
        from_attributes = True


# ===== PAYFAST CONFIGURATION SCHEMAS =====

class PayFastConfigResponse(BaseModel):
    """PayFast configuration information"""
    
    is_sandbox: bool = Field(..., description="Whether PayFast is in sandbox mode")
    merchant_id: str = Field(..., description="PayFast merchant ID")
    payment_url: str = Field(..., description="PayFast payment URL")
    supported_currencies: list = Field(default=["ZAR", "USD", "EUR", "GBP"], description="Supported currencies")
    webhook_url: str = Field(..., description="Webhook notification URL")


# ===== PAYFAST ERROR SCHEMAS =====

class PayFastError(BaseModel):
    """PayFast error response"""
    
    error_code: str = Field(..., description="Error code")
    error_message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")


# ===== PAYFAST INTEGRATION HELPERS =====

class PayFastFormHelper(BaseModel):
    """Helper for generating PayFast payment forms in frontend"""
    
    action_url: str = Field(..., description="Form action URL")
    method: str = Field("POST", description="Form method")
    form_data: Dict[str, str] = Field(..., description="Form input data")
    
    def generate_html_form(self, form_id: str = "payfast-form", auto_submit: bool = False) -> str:
        """Generate HTML form for PayFast payment"""
        
        form_html = f'<form id="{form_id}" action="{self.action_url}" method="{self.method}">\n'
        
        for key, value in self.form_data.items():
            form_html += f'  <input type="hidden" name="{key}" value="{value}">\n'
        
        form_html += '  <input type="submit" value="Pay with PayFast">\n'
        form_html += '</form>\n'
        
        if auto_submit:
            form_html += f'''
<script>
document.getElementById('{form_id}').submit();
</script>
'''
        
        return form_html


# ===== PAYFAST TESTING SCHEMAS =====

class PayFastTestPayment(BaseModel):
    """Test payment data for PayFast sandbox"""
    
    test_card_number: str = Field("****************", description="Test card number")
    test_amount: Decimal = Field(Decimal("100.00"), description="Test amount")
    test_email: str = Field("<EMAIL>", description="Test email")
    test_name: str = Field("Test User", description="Test user name")
    
    class Config:
        schema_extra = {
            "example": {
                "test_card_number": "****************",
                "test_amount": 100.00,
                "test_email": "<EMAIL>",
                "test_name": "Test User"
            }
        }
