"""
Migration to fix EventCategoryEnum values in the database.

This migration:
1. Updates the enum values to match the Python enum definition
2. Updates any existing data to use the correct enum values
"""

from sqlalchemy import text
from config.session import engine


def fix_event_category_enum():
    """
    Execute the migration to fix EventCategoryEnum values.
    """
    
    with engine.connect() as connection:
        # Start transaction
        trans = connection.begin()
        
        try:
            print("Starting migration to fix EventCategoryEnum values...")
            
            # Check current enum values
            print("Checking current enum values...")
            current_values = connection.execute(text("""
                SELECT enumlabel 
                FROM pg_enum e
                JOIN pg_type t ON e.enumtypid = t.oid
                WHERE t.typname = 'eventcategoryenum'
                ORDER BY e.enumsortorder;
            """)).fetchall()
            
            current_enum_values = [row[0] for row in current_values]
            print(f"Current enum values: {current_enum_values}")
            
            # Expected values (lowercase to match Python enum values)
            expected_values = ['workshop', 'conference', 'webinar', 'competition']
            
            # Check if we need to update
            if set(current_enum_values) == set(expected_values):
                print("Enum values are already correct!")
                trans.commit()
                return
            
            # Drop and recreate the enum type with correct values
            print("Recreating enum type with correct values...")
            
            # First, check if there are any events using the enum
            events_count = connection.execute(text("""
                SELECT COUNT(*) FROM events WHERE category IS NOT NULL;
            """)).scalar()
            
            if events_count > 0:
                print(f"Found {events_count} events with category values. Updating them...")
                
                # Update existing data to use temporary string values
                connection.execute(text("""
                    ALTER TABLE events ALTER COLUMN category TYPE VARCHAR(20);
                """))
                
                # Update values to lowercase if they're uppercase
                for old_val, new_val in [
                    ('WORKSHOP', 'workshop'),
                    ('CONFERENCE', 'conference'), 
                    ('WEBINAR', 'webinar'),
                    ('COMPETITION', 'competition')
                ]:
                    connection.execute(text(f"""
                        UPDATE events SET category = '{new_val}' WHERE category = '{old_val}';
                    """))
            else:
                print("No events found, safe to recreate enum.")
                # Just alter the column to remove the enum constraint
                connection.execute(text("""
                    ALTER TABLE events ALTER COLUMN category TYPE VARCHAR(20);
                """))
            
            # Drop the old enum type
            print("Dropping old enum type...")
            connection.execute(text("""
                DROP TYPE IF EXISTS eventcategoryenum CASCADE;
            """))
            
            # Create new enum type with correct values
            print("Creating new enum type with correct values...")
            connection.execute(text("""
                CREATE TYPE eventcategoryenum AS ENUM ('workshop', 'conference', 'webinar', 'competition');
            """))
            
            # Update the events table to use the new enum
            print("Updating events table to use new enum...")
            connection.execute(text("""
                ALTER TABLE events 
                ALTER COLUMN category TYPE eventcategoryenum 
                USING category::eventcategoryenum;
            """))
            
            # Make sure the column is NOT NULL
            connection.execute(text("""
                ALTER TABLE events 
                ALTER COLUMN category SET NOT NULL;
            """))
            
            # Verify the fix
            print("Verifying the fix...")
            new_values = connection.execute(text("""
                SELECT enumlabel 
                FROM pg_enum e
                JOIN pg_type t ON e.enumtypid = t.oid
                WHERE t.typname = 'eventcategoryenum'
                ORDER BY e.enumsortorder;
            """)).fetchall()
            
            new_enum_values = [row[0] for row in new_values]
            print(f"New enum values: {new_enum_values}")
            
            # Commit transaction
            trans.commit()
            print("Migration completed successfully!")
            
        except Exception as e:
            # Rollback on error
            trans.rollback()
            print(f"Migration failed: {e}")
            raise


def rollback_migration():
    """
    Rollback the migration (restore uppercase enum values).
    """
    
    with engine.connect() as connection:
        trans = connection.begin()
        
        try:
            print("Starting rollback of EventCategoryEnum migration...")
            
            # Update existing data to use temporary string values
            connection.execute(text("""
                ALTER TABLE events ALTER COLUMN category TYPE VARCHAR(20);
            """))
            
            # Update values to uppercase
            for old_val, new_val in [
                ('workshop', 'WORKSHOP'),
                ('conference', 'CONFERENCE'), 
                ('webinar', 'WEBINAR'),
                ('competition', 'COMPETITION')
            ]:
                connection.execute(text(f"""
                    UPDATE events SET category = '{new_val}' WHERE category = '{old_val}';
                """))
            
            # Drop the enum type
            connection.execute(text("""
                DROP TYPE IF EXISTS eventcategoryenum CASCADE;
            """))
            
            # Create enum type with uppercase values
            connection.execute(text("""
                CREATE TYPE eventcategoryenum AS ENUM ('WORKSHOP', 'CONFERENCE', 'WEBINAR', 'COMPETITION');
            """))
            
            # Update the events table to use the enum
            connection.execute(text("""
                ALTER TABLE events 
                ALTER COLUMN category TYPE eventcategoryenum 
                USING category::eventcategoryenum;
            """))
            
            trans.commit()
            print("Rollback completed successfully!")
            
        except Exception as e:
            trans.rollback()
            print(f"Rollback failed: {e}")
            raise


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "rollback":
        rollback_migration()
    else:
        fix_event_category_enum()
