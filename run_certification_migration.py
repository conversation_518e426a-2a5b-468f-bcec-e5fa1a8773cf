#!/usr/bin/env python3
"""
Run certification system migration
"""
import sys
import os
from datetime import datetime, timezone

# Add the EduFair app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'EduFair', 'app'))

try:
    from sqlalchemy import text
    from config.session import engine, Base
    
    # Import certification models to register them
    from Models.Certifications import (
        CertificationTemplate, CompetitionCertification, CertificationCriteria,
        CertificationAuditLog, CertificationVerification
    )
    
    def run_certification_migration():
        """Run the certification system migration"""
        print("🚀 Running Certification System Migration")
        print("=" * 50)
        
        try:
            # Create all certification tables
            print("1. Creating certification database tables...")
            Base.metadata.create_all(bind=engine)
            print("   ✅ Certification tables created successfully")
            
            # Add any missing columns to existing tables
            print("2. Adding missing columns to existing tables...")

            with engine.connect() as connection:
                # Check if StudentExamAttempt has is_submitted column
                try:
                    connection.execute(text("SELECT is_submitted FROM student_exam_attempts LIMIT 1"))
                    print("   ✅ is_submitted column already exists")
                except Exception:
                    # Add is_submitted column if it doesn't exist
                    try:
                        connection.execute(text("ALTER TABLE student_exam_attempts ADD COLUMN is_submitted BOOLEAN DEFAULT FALSE"))
                        connection.commit()
                        print("   ✅ Added is_submitted column to student_exam_attempts")
                    except Exception as e:
                        print(f"   ⚠️  Could not add is_submitted column: {e}")

                # Update existing exam attempts to be submitted if they have scores
                try:
                    result = connection.execute(text("""
                        UPDATE student_exam_attempts
                        SET is_submitted = TRUE
                        WHERE total_score IS NOT NULL AND (is_submitted = FALSE OR is_submitted IS NULL)
                    """))
                    connection.commit()
                    print(f"   ✅ Updated {result.rowcount} exam attempts to submitted status")
                except Exception as e:
                    print(f"   ⚠️  Could not update exam attempts: {e}")

            # Insert default certification templates
            print("3. Creating default certification templates...")

            with engine.connect() as connection:
                # Find an institute to create default templates
                institute_result = connection.execute(text("""
                    SELECT id FROM users WHERE user_type = 'institute' LIMIT 1
                """)).fetchone()

                if institute_result:
                    institute_id = institute_result[0]

                    # Check if templates already exist
                    existing_templates = connection.execute(text("""
                        SELECT COUNT(*) FROM certification_templates WHERE institute_id = :institute_id
                    """), {"institute_id": institute_id}).scalar()

                    if existing_templates == 0:
                        default_templates = [
                            {
                                "name": "Winner Certificate",
                                "description": "Awarded to competition winners",
                                "certification_type": "WINNER",
                                "certificate_text": "This certifies that {participant_name} has achieved 1st place in {competition_name}",
                                "max_rank": 1,
                                "min_percentile": 95.0,
                                "requires_mentor_approval": True,
                                "auto_award": False
                            },
                            {
                                "name": "Excellence Certificate",
                                "description": "Awarded for excellent performance",
                                "certification_type": "EXCELLENCE",
                                "certificate_text": "This certifies that {participant_name} has demonstrated excellence in {competition_name}",
                                "min_percentile": 90.0,
                                "requires_mentor_approval": True,
                                "auto_award": False
                            },
                            {
                                "name": "Merit Certificate",
                                "description": "Awarded for meritorious performance",
                                "certification_type": "MERIT",
                                "certificate_text": "This certifies that {participant_name} has shown merit in {competition_name}",
                                "min_percentile": 75.0,
                                "requires_mentor_approval": False,
                                "auto_award": True
                            },
                            {
                                "name": "Participation Certificate",
                                "description": "Awarded for participation",
                                "certification_type": "PARTICIPATION",
                                "certificate_text": "This certifies that {participant_name} has participated in {competition_name}",
                                "min_percentile": 0.0,
                                "requires_mentor_approval": False,
                                "auto_award": True
                            }
                        ]
                        
                        try:
                            for template in default_templates:
                                connection.execute(text("""
                                    INSERT INTO certification_templates (
                                        id, name, description, certification_type, institute_id,
                                        certificate_text, min_percentile, requires_mentor_approval,
                                        auto_award, is_active, is_default, created_at, updated_at
                                    ) VALUES (
                                        gen_random_uuid(), :name, :description, :certification_type, :institute_id,
                                        :certificate_text, :min_percentile, :requires_mentor_approval,
                                        :auto_award, true, true, NOW(), NOW()
                                    )
                                """), {
                                    "name": template["name"],
                                    "description": template["description"],
                                    "certification_type": template["certification_type"],
                                    "institute_id": institute_id,
                                    "certificate_text": template["certificate_text"],
                                    "min_percentile": template["min_percentile"],
                                    "requires_mentor_approval": template["requires_mentor_approval"],
                                    "auto_award": template["auto_award"]
                                })
                                print(f"   ✅ Created template: {template['name']}")

                            connection.commit()
                        except Exception as e:
                            print(f"   ❌ Error creating templates: {e}")
                    else:
                        print(f"   ⚠️  Templates already exist ({existing_templates} found)")
                else:
                    print("   ⚠️  No institute found to create default templates")
            
            print("✅ Certification system migration completed successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Migration failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def verify_certification_migration():
        """Verify the certification migration was successful"""
        print("\n🔍 Verifying Certification Migration...")
        
        verification_queries = [
            ("Certification Templates Table", "SELECT COUNT(*) FROM certification_templates"),
            ("Competition Certifications Table", "SELECT COUNT(*) FROM competition_certifications"),
            ("Certification Criteria Table", "SELECT COUNT(*) FROM certification_criteria"),
            ("Certification Audit Logs Table", "SELECT COUNT(*) FROM certification_audit_logs"),
            ("Certification Verifications Table", "SELECT COUNT(*) FROM certification_verifications"),
            ("Student Exam Attempts with is_submitted", "SELECT COUNT(*) FROM student_exam_attempts WHERE is_submitted IS NOT NULL"),
        ]
        
        try:
            with engine.connect() as connection:
                for description, query in verification_queries:
                    try:
                        result = connection.execute(text(query)).scalar()
                        print(f"   ✅ {description}: {result} records")
                    except Exception as e:
                        print(f"   ❌ {description}: Error - {e}")
                        
        except Exception as e:
            print(f"❌ Verification failed: {e}")
            return False
        
        return True
    
    if __name__ == "__main__":
        success = run_certification_migration()
        if success:
            verify_certification_migration()
            print("\n🎉 Certification System Migration Complete!")
            print("\nNext steps:")
            print("1. Run: python test_certification_system.py")
            print("2. Test certification template creation")
            print("3. Test competition completion with certification generation")
            print("4. Test mentor evaluation workflow")
        else:
            print("\n❌ Migration failed. Please check the errors above.")
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running this from the correct directory")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
