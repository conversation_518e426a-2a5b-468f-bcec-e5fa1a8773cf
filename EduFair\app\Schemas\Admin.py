"""
Admin Schemas

This module contains Pydantic schemas for admin-level operations
including event management and registration oversight.
"""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from uuid import UUID
from datetime import datetime
from decimal import Decimal


# ==================== ADMIN EVENT SCHEMAS ====================

class AdminEventOut(BaseModel):
    """Enhanced event output for admin with statistics"""
    id: UUID
    title: str
    description: Optional[str] = None
    short_description: Optional[str] = None
    category: str
    status: str
    location: str
    start_datetime: datetime
    end_datetime: datetime
    organizer_id: UUID
    institute_id: Optional[UUID] = None
    created_at: datetime
    updated_at: datetime
    
    # Admin statistics
    total_registrations: int = 0
    confirmed_registrations: int = 0
    total_revenue: float = 0.0
    total_tickets: int = 0
    sold_tickets: int = 0
    available_tickets: int = 0
    organizer_email: Optional[str] = None
    organizer_name: Optional[str] = None
    
    class Config:
        from_attributes = True


class AdminEventListResponse(BaseModel):
    """Paginated response for admin event list"""
    events: List[AdminEventOut]
    total_count: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool


class AdminEventStatsOut(BaseModel):
    """Comprehensive event statistics for admin dashboard"""
    event_id: UUID
    registration_stats: Dict[str, int] = Field(
        description="Registration statistics by status"
    )
    revenue_stats: Dict[str, float] = Field(
        description="Revenue and payment statistics"
    )
    ticket_stats: Dict[str, int] = Field(
        description="Ticket availability and sales statistics"
    )
    generated_at: datetime


# ==================== ADMIN REGISTRATION SCHEMAS ====================

class AdminEventRegistrationOut(BaseModel):
    """Enhanced registration output for admin with user and event details"""
    id: UUID
    event_id: UUID
    ticket_id: Optional[UUID] = None
    user_id: UUID
    registration_number: str
    status: str
    quantity: int
    total_amount: Decimal
    currency: str
    attendee_info: Optional[Dict[str, Any]] = None
    special_requirements: Optional[str] = None
    emergency_contact: Optional[Dict[str, Any]] = None
    dietary_preferences: Optional[List[str]] = None
    accessibility_needs: Optional[str] = None
    registered_at: Optional[datetime] = None
    confirmed_at: Optional[datetime] = None
    cancelled_at: Optional[datetime] = None
    attended_at: Optional[datetime] = None
    payment_status: Optional[str] = None
    payment_reference: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    # Enhanced admin details
    user_email: Optional[str] = None
    user_name: Optional[str] = None
    user_phone: Optional[str] = None
    event_title: Optional[str] = None
    event_location: Optional[str] = None
    ticket_name: Optional[str] = None
    
    class Config:
        from_attributes = True


class AdminRegistrationOut(BaseModel):
    """Simplified registration output for admin lists"""
    id: UUID
    event_id: UUID
    user_id: UUID
    registration_number: str
    status: str
    quantity: int
    total_amount: Decimal
    currency: str
    registered_at: Optional[datetime] = None
    created_at: datetime
    
    # Enhanced admin details
    user_email: Optional[str] = None
    user_name: Optional[str] = None
    user_type: Optional[str] = None
    event_title: Optional[str] = None
    event_location: Optional[str] = None
    event_start_date: Optional[str] = None
    ticket_name: Optional[str] = None
    ticket_price: float = 0.0
    payment_status: Optional[str] = None
    payment_id: Optional[UUID] = None
    
    class Config:
        from_attributes = True


class AdminRegistrationListResponse(BaseModel):
    """Paginated response for admin registration list"""
    registrations: List[AdminRegistrationOut]
    total_count: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool


# ==================== ADMIN ACTION SCHEMAS ====================

class AdminActionLog(BaseModel):
    """Log entry for admin actions"""
    action: str = Field(description="Type of action performed")
    resource_type: str = Field(description="Type of resource (event, registration, etc.)")
    resource_id: UUID = Field(description="ID of the affected resource")
    admin_id: UUID = Field(description="ID of the admin who performed the action")
    admin_email: str = Field(description="Email of the admin")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional action details")
    timestamp: datetime = Field(description="When the action was performed")
    
    class Config:
        from_attributes = True


class AdminDashboardStats(BaseModel):
    """Overall statistics for admin dashboard"""
    total_events: int
    active_events: int
    total_registrations: int
    confirmed_registrations: int
    total_revenue: float
    pending_payments: int
    recent_registrations: int = Field(description="Registrations in last 24 hours")
    popular_events: List[Dict[str, Any]] = Field(description="Top 5 events by registration")
    
    class Config:
        from_attributes = True


# ==================== ADMIN BULK OPERATIONS ====================

class BulkRegistrationUpdate(BaseModel):
    """Schema for bulk updating registrations"""
    registration_ids: List[UUID] = Field(description="List of registration IDs to update")
    status: Optional[str] = Field(None, description="New status for all registrations")
    notes: Optional[str] = Field(None, description="Admin notes for the bulk update")
    
    class Config:
        from_attributes = True


class BulkRegistrationResponse(BaseModel):
    """Response for bulk registration operations"""
    updated_count: int
    failed_count: int
    failed_registrations: List[UUID] = Field(default_factory=list)
    message: str
    
    class Config:
        from_attributes = True


# ==================== ADMIN EXPORT SCHEMAS ====================

class EventExportRequest(BaseModel):
    """Request schema for exporting event data"""
    event_ids: Optional[List[UUID]] = Field(None, description="Specific events to export")
    date_from: Optional[datetime] = Field(None, description="Export events from this date")
    date_to: Optional[datetime] = Field(None, description="Export events until this date")
    include_registrations: bool = Field(True, description="Include registration data")
    include_payments: bool = Field(True, description="Include payment data")
    format: str = Field("csv", description="Export format (csv, xlsx, json)")
    
    class Config:
        from_attributes = True


class RegistrationExportRequest(BaseModel):
    """Request schema for exporting registration data"""
    event_id: Optional[UUID] = Field(None, description="Export registrations for specific event")
    status_filter: Optional[str] = Field(None, description="Filter by registration status")
    date_from: Optional[datetime] = Field(None, description="Export registrations from this date")
    date_to: Optional[datetime] = Field(None, description="Export registrations until this date")
    include_user_details: bool = Field(True, description="Include user information")
    include_payment_details: bool = Field(True, description="Include payment information")
    format: str = Field("csv", description="Export format (csv, xlsx, json)")
    
    class Config:
        from_attributes = True


# ==================== ADMIN NOTIFICATION SCHEMAS ====================

class AdminNotificationCreate(BaseModel):
    """Schema for creating admin notifications"""
    title: str = Field(description="Notification title")
    message: str = Field(description="Notification message")
    type: str = Field(description="Notification type (info, warning, error, success)")
    target_users: Optional[List[UUID]] = Field(None, description="Specific users to notify")
    target_events: Optional[List[UUID]] = Field(None, description="Users registered for these events")
    send_email: bool = Field(False, description="Send email notification")
    send_sms: bool = Field(False, description="Send SMS notification")
    
    class Config:
        from_attributes = True


class AdminNotificationResponse(BaseModel):
    """Response for admin notification operations"""
    notification_id: UUID
    sent_count: int
    failed_count: int
    message: str
    
    class Config:
        from_attributes = True
