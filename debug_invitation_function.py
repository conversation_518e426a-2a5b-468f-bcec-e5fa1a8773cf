#!/usr/bin/env python3
"""
Debug script for list_received_invitations function
"""
import sys
import os
import uuid

# Add the EduFair app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'EduFair', 'app'))

try:
    from sqlalchemy.orm import Session
    from config.session import get_db
    from Models.users import User, MentorProfile, UserTypeEnum, MentorInstituteInvite
    from Cruds.Institute.Mentor import _get_user_profile_details
    from Schemas.Mentors.MentorInstitutes import MentorInstituteInviteOut, InvitationListResponse
    from sqlalchemy import desc
    
    def debug_invitation_function():
        """Debug the invitation function step by step"""
        print("🔍 Debugging Invitation Function Step by Step")
        print("=" * 60)
        
        # Get database session
        db = next(get_db())
        
        try:
            # Find a mentor user who has received invitations
            mentor_user = db.query(User).filter(
                User.user_type == UserTypeEnum.mentor
            ).first()
            
            if not mentor_user:
                print("❌ No mentor users found in database")
                return
            
            print(f"✅ Found mentor user: {mentor_user.username} (ID: {mentor_user.id})")
            
            # Get invitations for this mentor
            receiver_id = mentor_user.id
            query = db.query(MentorInstituteInvite).filter(
                MentorInstituteInvite.receiver_id == receiver_id
            )
            
            invites = query.order_by(desc(MentorInstituteInvite.invited_at)).all()
            
            if not invites:
                print("❌ No invitations found for this mentor")
                return
            
            print(f"✅ Found {len(invites)} invitation(s)")
            
            # Process the first invitation manually
            invite = invites[0]
            print(f"\n📊 Processing invitation: {invite.id}")
            print(f"   - Mentor ID: {invite.mentor_id}")
            print(f"   - Institute ID: {invite.institute_id}")
            print(f"   - Receiver ID: {invite.receiver_id}")
            print(f"   - Received By: {invite.received_by}")
            
            # Create invite_out object
            invite_out = MentorInstituteInviteOut.model_validate(invite)
            print(f"✅ Created MentorInstituteInviteOut object")
            
            # Determine sender and receiver - handle both string and enum values
            received_by_value = invite.received_by.value if hasattr(invite.received_by, 'value') else str(invite.received_by)
            print(f"   - Received By Value: {received_by_value}")

            if received_by_value == "mentor":
                # Institute is the sender, Mentor is the receiver
                sender_id = invite.institute_id
                sender_type = "institute"
                invite_receiver_id = invite.mentor_id
                receiver_type = "mentor"
                print(f"✅ Determined: Institute ({sender_id}) -> Mentor ({invite_receiver_id})")
            elif received_by_value == "institute":
                # Mentor is the sender, Institute is the receiver
                sender_id = invite.mentor_id
                sender_type = "mentor"
                invite_receiver_id = invite.institute_id
                receiver_type = "institute"
                print(f"✅ Determined: Mentor ({sender_id}) -> Institute ({invite_receiver_id})")
            else:
                print(f"❌ Unknown received_by value: {received_by_value}")
                return
            
            # Test getting sender details
            print(f"\n🧪 Testing sender details...")
            print(f"   - Sender ID: {sender_id}")
            print(f"   - Sender Type: {sender_type}")
            
            sender_details = _get_user_profile_details(db, sender_id, sender_type)
            if sender_details:
                print("✅ Sender details retrieved successfully")
                print(f"   - Username: {sender_details.username}")
                print(f"   - Email: {sender_details.email}")
                if sender_type == "institute":
                    print(f"   - Institute Name: {sender_details.institute_name}")
                else:
                    print(f"   - Mentor Bio: {sender_details.mentor_bio}")
            else:
                print("❌ Sender details returned None")
            
            # Test getting receiver details
            print(f"\n🧪 Testing receiver details...")
            print(f"   - Receiver ID: {invite_receiver_id}")
            print(f"   - Receiver Type: {receiver_type}")
            
            receiver_details = _get_user_profile_details(db, invite_receiver_id, receiver_type)
            if receiver_details:
                print("✅ Receiver details retrieved successfully")
                print(f"   - Username: {receiver_details.username}")
                print(f"   - Email: {receiver_details.email}")
                if receiver_type == "mentor":
                    print(f"   - Mentor Bio: {receiver_details.mentor_bio}")
                    print(f"   - Mentor Full Name: {receiver_details.mentor_full_name}")
                    print(f"   - Mentor Position: {receiver_details.mentor_current_position}")
                    print(f"   - Mentor Organization: {receiver_details.mentor_current_organization}")
                    print(f"   - Mentor Phone: {receiver_details.mentor_phone}")
                    print(f"   - Mentor Languages: {receiver_details.mentor_languages}")
                    print(f"   - Mentor Availability: {receiver_details.mentor_availability_hours}")
                else:
                    print(f"   - Institute Name: {receiver_details.institute_name}")
            else:
                print("❌ Receiver details returned None")
            
            # Test assignment
            print(f"\n🧪 Testing assignment...")
            invite_out.sender = sender_details
            invite_out.receiver = receiver_details
            
            if invite_out.sender:
                print("✅ Sender assigned successfully")
            else:
                print("❌ Sender assignment failed")
                
            if invite_out.receiver:
                print("✅ Receiver assigned successfully")
                if receiver_type == "mentor":
                    print(f"   - Receiver mentor_bio: {invite_out.receiver.mentor_bio}")
                    print(f"   - Receiver mentor_full_name: {invite_out.receiver.mentor_full_name}")
            else:
                print("❌ Receiver assignment failed")
            
            # Test the complete function
            print(f"\n🧪 Testing complete list_received_invitations function...")
            from Cruds.Institute.Mentor import list_received_invitations
            
            invitation_response = list_received_invitations(
                db=db,
                receiver_id=receiver_id,
                page=1,
                size=20
            )
            
            if invitation_response and invitation_response.invitations:
                first_invitation = invitation_response.invitations[0]
                print(f"✅ Complete function returned {len(invitation_response.invitations)} invitation(s)")
                
                if first_invitation.receiver:
                    print(f"✅ Receiver details present in final result")
                    print(f"   - Mentor Bio: {first_invitation.receiver.mentor_bio}")
                    print(f"   - Mentor Full Name: {first_invitation.receiver.mentor_full_name}")
                    print(f"   - Mentor Position: {first_invitation.receiver.mentor_current_position}")
                else:
                    print(f"❌ Receiver details missing in final result")
                    
                if first_invitation.sender:
                    print(f"✅ Sender details present in final result")
                else:
                    print(f"❌ Sender details missing in final result")
            else:
                print("❌ Complete function returned no invitations")
                
        except Exception as e:
            print(f"❌ Error during debugging: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            db.close()
    
    if __name__ == "__main__":
        debug_invitation_function()
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running this from the correct directory and all dependencies are installed")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
