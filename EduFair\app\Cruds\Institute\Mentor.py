from typing import List, Optional
import uuid
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc
from fastapi import HTTPException
from datetime import datetime, timezone, timedelta
import bcrypt
import json

# Import Models
from Models.users import (
    User, UserTypeEnum, MentorProfile, InstituteProfile,
    MentorInstituteAssociation, MentorInstituteInvite as MentorInstituteInviteModel, Subject
)
from Models.Competitions import CompetitionMentorAssignment

# Import Schemas
from Schemas.Mentors.Mentor import (
    MentorProfileUpdate,
    MentorUserOut, MentorDetailedOut, MentorProfileOut, MentorListOut, MentorListResponse
)
from Schemas.Mentors.MentorInstitutes import (
    MentorInstituteInvite as MentorInstituteInviteSchema, MentorInstituteInviteOut, InvitationListResponse,
    CollaborationDetails, CollaborationCreate, CollaborationUpdate, InvitationSenderDetails
)
from Schemas.Institute.Institute import InstituteListOut

# Import Utilities
from utils.image_utils import get_profile_image_data


def _convert_subject_uuids_to_names(db: Session, subject_uuids: Optional[List[str]]) -> List[str]:
    """Convert subject UUIDs to subject names"""
    if not subject_uuids:
        return []

    try:
        # Convert string UUIDs to UUID objects and query subjects
        subject_names = []
        for uuid_str in subject_uuids:
            try:
                subject_uuid = uuid.UUID(uuid_str)
                subject = db.query(Subject).filter(Subject.id == subject_uuid).first()
                if subject:
                    subject_names.append(subject.name)
                else:
                    # If subject not found, keep the UUID as fallback
                    subject_names.append(f"Unknown Subject ({uuid_str[:8]}...)")
            except (ValueError, TypeError):
                # If UUID is invalid, keep the original string
                subject_names.append(str(uuid_str))

        return subject_names
    except Exception as e:
        # If anything goes wrong, return empty list
        print(f"Error converting subject UUIDs to names: {e}")
        return []


def _filter_null_fields(data_dict: dict) -> dict:
    """Remove null/None fields from dictionary to reduce response size"""
    return {k: v for k, v in data_dict.items() if v is not None and v != "" and v != []}


def _get_user_profile_details(db: Session, user_id: uuid.UUID, user_type: str) -> Optional[InvitationSenderDetails]:
    """Get complete user profile details for invitations"""

    # Get user data
    user_data = db.query(User).filter(User.id == user_id).first()
    if not user_data:
        return None

    # Parse profile picture - optimize to avoid redundant processing
    profile_pic = user_data.profile_picture
    profile_image_data = None

    if user_type == "mentor":
        # Get mentor profile with proper query and eager loading
        profile_data = db.query(MentorProfile).options(
            joinedload(MentorProfile.expertise_subjects),
            joinedload(MentorProfile.preferred_subjects)
        ).filter(MentorProfile.user_id == user_id).first()

        # If no profile exists, create a basic one
        if not profile_data:
            profile_data = MentorProfile(user_id=user_id)
            db.add(profile_data)
            db.commit()
            db.refresh(profile_data)

        # Determine primary image URL (prioritize mentor profile image over user profile pic)
        primary_image_url = None
        if profile_data and profile_data.profile_image_url:
            primary_image_url = profile_data.profile_image_url
        elif profile_pic:
            primary_image_url = profile_pic

        # Process image data only once
        if primary_image_url:
            try:
                profile_image_data = get_profile_image_data(primary_image_url)
            except Exception:
                profile_image_data = None

        # Parse mentor languages - handle both string and list formats with proper JSON parsing
        mentor_languages = None
        if profile_data and profile_data.languages:
            try:
                if isinstance(profile_data.languages, str):
                    # Clean up escaped quotes and parse as JSON
                    cleaned_languages = profile_data.languages.replace('\\"', '"')
                    mentor_languages = json.loads(cleaned_languages)
                elif isinstance(profile_data.languages, list):
                    # Already a list
                    mentor_languages = profile_data.languages
                else:
                    # Unknown format, convert to list
                    mentor_languages = [str(profile_data.languages)]
            except (json.JSONDecodeError, TypeError):
                # Fallback: try to extract from string
                if isinstance(profile_data.languages, str):
                    # Try to split by comma if it's a comma-separated string
                    if ',' in profile_data.languages:
                        mentor_languages = [lang.strip().strip('"\'') for lang in profile_data.languages.split(',')]
                    else:
                        mentor_languages = [profile_data.languages.strip('"\'')]
                else:
                    mentor_languages = []

        # Parse availability hours from JSON with proper handling of escaped quotes
        mentor_availability_hours = None
        if profile_data and profile_data.availability_hours:
            try:
                if isinstance(profile_data.availability_hours, str):
                    # Clean up escaped quotes and parse as JSON
                    cleaned_hours = profile_data.availability_hours.replace('\\"', '"')
                    mentor_availability_hours = json.loads(cleaned_hours)
                elif isinstance(profile_data.availability_hours, dict):
                    mentor_availability_hours = profile_data.availability_hours
            except (json.JSONDecodeError, TypeError):
                mentor_availability_hours = None

        # Create base data
        base_data = {
            "id": user_data.id,
            "username": user_data.username or "",
            "email": user_data.email or "",
            "profile_picture": profile_pic,
            "profile_image": profile_image_data,
        }

        # Add mentor-specific fields only if profile exists and has data
        mentor_data = {}
        if profile_data:
            mentor_data = {
                "mentor_bio": profile_data.bio,
                "mentor_experience_years": profile_data.experience_years,
                "mentor_hourly_rate": float(profile_data.hourly_rate) if profile_data.hourly_rate else None,
                "mentor_languages": mentor_languages,
                "mentor_full_name": profile_data.full_name,
                "mentor_current_position": profile_data.current_position,
                "mentor_current_organization": profile_data.current_organization,
                "mentor_education": profile_data.education,
                "mentor_certifications": profile_data.certifications,
                "mentor_is_verified": profile_data.is_verified,
                "mentor_rating": float(profile_data.rating) if profile_data.rating and profile_data.rating > 0 else None,
                "mentor_phone": profile_data.phone,
                "mentor_linkedin_url": profile_data.linkedin_url,
                "mentor_website": profile_data.website,
                "mentor_portfolio_url": profile_data.portfolio_url,
                "mentor_resume_url": profile_data.resume_url,
                "mentor_availability_hours": mentor_availability_hours,
                "mentor_verification_status": profile_data.verification_status,
                "mentor_total_reviews": profile_data.total_reviews,
            }
            # Filter out None/empty values
            mentor_data = {k: v for k, v in mentor_data.items() if v is not None and v != "" and v != []}

        # Combine base data with mentor data
        combined_data = {**base_data, **mentor_data}

        return InvitationSenderDetails(**combined_data)

    else:  # institute
        # Get institute profile
        profile_data = db.query(InstituteProfile).filter(InstituteProfile.user_id == user_id).first()

        # Determine primary image URL (prioritize institute logo over user profile pic)
        primary_image_url = None
        if profile_data and profile_data.logo_url:
            primary_image_url = profile_data.logo_url
        elif profile_pic:
            primary_image_url = profile_pic

        # Process image data only once
        institute_logo_data = None
        if primary_image_url:
            try:
                institute_logo_data = get_profile_image_data(primary_image_url)
            except Exception:
                institute_logo_data = None

        # Create base data
        base_data = {
            "id": user_data.id,
            "username": user_data.username or "",
            "email": user_data.email or "",
            "profile_picture": profile_pic,
            "profile_image": profile_image_data,
        }

        # Add institute-specific fields only if profile exists and has data
        institute_data = {}
        if profile_data:
            institute_data = {
                "institute_name": profile_data.institute_name,
                "institute_description": profile_data.description,
                "institute_website": profile_data.website,
                "institute_city": profile_data.city,
                "institute_state": profile_data.state,
                "institute_country": user_data.country,
                "institute_address": profile_data.address,
                "institute_postal_code": profile_data.postal_code,
                "institute_established_year": profile_data.established_year,
                "institute_type": profile_data.institute_type,
                "institute_accreditation": profile_data.accreditation,
                "institute_linkedin_url": profile_data.linkedin_url,
                "institute_facebook_url": profile_data.facebook_url,
                "institute_twitter_url": profile_data.twitter_url,
                "institute_is_verified": profile_data.is_verified,
                "institute_verification_status": profile_data.verification_status,
                "institute_logo": institute_logo_data,
            }
            # Filter out None/empty values
            institute_data = {k: v for k, v in institute_data.items() if v is not None and v != ""}

        # Combine base data with institute data
        combined_data = {**base_data, **institute_data}

        return InvitationSenderDetails(**combined_data)







def get_mentor_with_profile_by_id(db: Session, mentor_id: uuid.UUID) -> MentorDetailedOut:
    """Get mentor by ID with only profile details (no stats)."""

    # Fetch mentor user + profile
    user = db.query(User).options(
        joinedload(User.mentor_profile)
    ).filter(
        User.id == mentor_id,
        User.user_type == UserTypeEnum.mentor
    ).first()

    if not user:
        raise HTTPException(status_code=404, detail="Mentor not found")

    # Ensure mentor profile exists
    if not user.mentor_profile:
        mentor_profile = MentorProfile(user_id=user.id)
        db.add(mentor_profile)
        db.commit()
        db.refresh(mentor_profile)
        user.mentor_profile = mentor_profile

    profile = user.mentor_profile

    # Parse JSON fields
    languages = json.loads(profile.languages) if profile.languages else []
    availability_hours = json.loads(profile.availability_hours) if profile.availability_hours else {}

    # Image handling - Only process once to avoid repetition
    primary_image_url = user.profile_picture or profile.profile_image_url
    profile_image_data = get_profile_image_data(primary_image_url, None)

    # Mentor profile schema - Include image data here
    profile_out = MentorProfileOut(
        id=profile.id,
        user_id=profile.user_id,
        bio=profile.bio,
        experience_years=profile.experience_years,
        hourly_rate=profile.hourly_rate,
        profile_image_url=primary_image_url,
        profile_image=profile_image_data,

        # Extended profile information with defaults
        full_name=getattr(profile, 'full_name', None),
        phone=getattr(profile, 'phone', None),
        linkedin_url=getattr(profile, 'linkedin_url', None),
        website=getattr(profile, 'website', None),
        current_position=getattr(profile, 'current_position', None),
        current_organization=getattr(profile, 'current_organization', None),
        education=getattr(profile, 'education', None),
        certifications=getattr(profile, 'certifications', None),
        portfolio_url=getattr(profile, 'portfolio_url', None),
        resume_url=getattr(profile, 'resume_url', None),

        # JSON fields
        languages=languages,
        availability_hours=availability_hours,

        # Verification and rating with defaults
        is_verified=getattr(profile, 'is_verified', False),
        verification_status=getattr(profile, 'verification_status', 'pending'),
        rating=getattr(profile, 'rating', None),
        total_reviews=getattr(profile, 'total_reviews', 0),

        expertise_subjects=[
            {"id": str(s.id), "name": s.name} for s in getattr(profile, "expertise_subjects", []) if s
        ],
        preferred_subjects=[
            {"id": str(s.id), "name": s.name} for s in getattr(profile, "preferred_subjects", []) if s
        ],
        created_at=profile.created_at,
        updated_at=profile.updated_at,
    )

    # Mentor user schema - Don't duplicate image data, just include URL
    user_out = MentorUserOut(
        id=user.id,
        username=user.username,
        email=user.email,
        mobile=user.mobile,
        country=user.country,
        profile_picture=user.profile_picture,
        profile_image=None,  # Don't duplicate image data here
        user_type=str(user.user_type.value),
        is_email_verified=user.is_email_verified,
        is_mobile_verified=user.is_mobile_verified,
        created_at=user.created_at,
        mentor_profile=None  # Don't duplicate profile data here
    )

    # Final response — Return only the profile with image data to avoid duplication
    return MentorDetailedOut(
        user=user_out,
        profile=profile_out,
        total_competitions=0,
        active_institutes=0,
        average_rating=None,
        verification_status="pending"
    )



def get_mentors(
    db: Session,
    page: int = 1,
    size: int = 20,
    search: Optional[str] = None,
    subject_filter: Optional[str] = None,
    min_experience: Optional[int] = None,
    max_hourly_rate: Optional[float] = None
) -> MentorListResponse:
    """Get mentors with filtering and pagination"""
    # Calculate skip for pagination
    skip = (page - 1) * size

    query = db.query(User).options(
        joinedload(User.mentor_profile).joinedload(MentorProfile.expertise_subjects),
        joinedload(User.mentor_profile).joinedload(MentorProfile.preferred_subjects)
    ).join(MentorProfile, MentorProfile.user_id == User.id).filter(User.user_type == UserTypeEnum.mentor)

    # Add search functionality
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            or_(
                User.username.ilike(search_term),
                User.email.ilike(search_term),
                MentorProfile.bio.ilike(search_term)
            )
        )

    # Filter by subject expertise
    if subject_filter:
        query = query.join(MentorProfile.expertise_subjects).filter(
            Subject.name.ilike(f"%{subject_filter}%")
        )

    # Filter by minimum experience
    if min_experience is not None:
        query = query.filter(MentorProfile.experience_years >= min_experience)

    # Filter by maximum hourly rate
    if max_hourly_rate is not None:
        query = query.filter(MentorProfile.hourly_rate <= max_hourly_rate)

    # Get total count for pagination
    total = query.count()

    # Get mentors with pagination
    mentors = query.offset(skip).limit(size).all()

    # Convert to MentorListOut objects
    mentor_list = []
    for user in mentors:
        mentor_profile = user.mentor_profile

        # Get profile image URL - prioritize user.profile_picture over mentor_profile.profile_image_url
        user_profile_picture = user.profile_picture
        mentor_profile_image_url = getattr(mentor_profile, 'profile_image_url', None)
        primary_image_url = user_profile_picture or mentor_profile_image_url

        # Only process image data if there's an image URL to avoid unnecessary processing
        profile_image_data = None
        if primary_image_url:
            profile_image_data = get_profile_image_data(primary_image_url, None)

        # Get expertise areas from subjects
        expertise_areas = []
        if mentor_profile and hasattr(mentor_profile, 'expertise_subjects') and mentor_profile.expertise_subjects:
            expertise_areas = [subject.name for subject in mentor_profile.expertise_subjects if subject]

        # Parse languages from JSON
        languages = []
        if mentor_profile and mentor_profile.languages:
            try:
                languages = json.loads(mentor_profile.languages) if mentor_profile.languages else []
            except (json.JSONDecodeError, TypeError):
                languages = []

        # Create full name from username (could be enhanced with first_name/last_name if available)
        full_name = user.username

        mentor_list.append(MentorListOut(
            id=user.id,
            username=user.username,
            full_name=full_name,
            email=user.email,
            mobile=user.mobile,
            country=user.country,
            bio=mentor_profile.bio if mentor_profile else None,
            expertise_areas=expertise_areas,
            experience_years=mentor_profile.experience_years if mentor_profile else None,
            current_position=None,  # Not available in current schema
            hourly_rate=mentor_profile.hourly_rate if mentor_profile else None,
            languages=languages,
            rating=None,  # Not implemented yet
            is_verified=user.is_email_verified,
            verification_status="pending",  # Default status
            profile_image_url=primary_image_url,
            profile_image=profile_image_data,
            created_at=user.created_at
        ))

    # Calculate pagination info
    has_next = (skip + size) < total
    has_prev = page > 1

    return MentorListResponse(
        mentors=mentor_list,
        total=total,
        page=page,
        size=size,
        has_next=has_next,
        has_prev=has_prev
    )
   
def update_mentor_profile(
    db: Session,
    mentor_id: uuid.UUID,
    profile_update: MentorProfileUpdate
) -> MentorDetailedOut:
    """Update mentor profile"""
    
    user = db.query(User).options(
        joinedload(User.mentor_profile)
    ).filter(
        User.id == mentor_id,
        User.user_type == UserTypeEnum.mentor
    ).first()
    
    if not user:
        raise HTTPException(status_code=404, detail="Mentor not found")
    
    profile = user.mentor_profile
    if not profile:
        raise HTTPException(status_code=404, detail="Mentor profile not found")
    
    # Update profile fields
    update_data = profile_update.model_dump(exclude_unset=True)

    # Handle subject relationships separately
    expertise_subject_ids = update_data.pop('expertise_subject_ids', None)
    preferred_subject_ids = update_data.pop('preferred_subject_ids', None)

    for field, value in update_data.items():
        if hasattr(profile, field):
            # Handle JSON fields
            if field in ['languages'] and value:
                setattr(profile, field, json.dumps(value))
            elif field == 'availability_hours' and value:
                setattr(profile, field, json.dumps(value))
            # Handle text fields that might be empty strings
            elif field in ['bio', 'education', 'certifications'] and value == "":
                setattr(profile, field, None)
            # Handle URL fields validation
            elif field in ['linkedin_url', 'website', 'portfolio_url', 'resume_url'] and value:
                if not (value.startswith('http://') or value.startswith('https://')):
                    value = 'https://' + value
                setattr(profile, field, value)
            else:
                setattr(profile, field, value)

    # Update expertise subjects
    if expertise_subject_ids is not None:
        from Models.users import Subject
        profile.expertise_subjects.clear()
        if expertise_subject_ids:
            expertise_subjects = db.query(Subject).filter(Subject.id.in_(expertise_subject_ids)).all()
            profile.expertise_subjects.extend(expertise_subjects)

    # Update preferred subjects
    if preferred_subject_ids is not None:
        from Models.users import Subject
        profile.preferred_subjects.clear()
        if preferred_subject_ids:
            preferred_subjects = db.query(Subject).filter(Subject.id.in_(preferred_subject_ids)).all()
            profile.preferred_subjects.extend(preferred_subjects)

    db.commit()
    db.refresh(user)
    
    return get_mentor_with_profile_by_id(db, mentor_id)


def create_invite_to_institute(
    db: Session,
    mentor_id: uuid.UUID,
    invite_data: MentorInstituteInviteSchema
) -> MentorInstituteInviteOut:
    """Mentor sends invite to institute"""

    # Verify mentor exists
    mentor = db.query(User).filter(
        User.id == mentor_id,
        User.user_type == UserTypeEnum.mentor
    ).first()

    if not mentor:
        raise HTTPException(status_code=404, detail="Mentor not found")

    # Verify institute exists
    institute = db.query(User).filter(
        User.id == invite_data.receiver_id,
        User.user_type == UserTypeEnum.institute
    ).first()

    if not institute:
        raise HTTPException(status_code=404, detail="Institute not found")

    # Check if invite already exists
    existing_invite = db.query(MentorInstituteInviteModel).filter(
        MentorInstituteInviteModel.mentor_id == mentor_id,
        MentorInstituteInviteModel.institute_id == invite_data.receiver_id,
        MentorInstituteInviteModel.status == "pending"
    ).first()

    if existing_invite:
        raise HTTPException(status_code=400, detail="Pending invite already exists")

    # Validate required fields
    if not invite_data.invitation_message or len(invite_data.invitation_message.strip()) < 10:
        raise HTTPException(status_code=400, detail="Invitation message must be at least 10 characters long")
    if not invite_data.proposed_hourly_rate or invite_data.proposed_hourly_rate <= 0:
        raise HTTPException(status_code=400, detail="Valid hourly rate is required (must be greater than 0)")
    if not invite_data.proposed_hours_per_week or invite_data.proposed_hours_per_week <= 0 or invite_data.proposed_hours_per_week > 168:
        raise HTTPException(status_code=400, detail="Valid hours per week is required (1-168 hours)")

    # Validate mentor has complete profile
    mentor_profile = db.query(MentorProfile).filter(MentorProfile.user_id == mentor_id).first()
    if not mentor_profile:
        raise HTTPException(status_code=400, detail="Mentor must complete profile before sending invitations")
    if not mentor_profile.bio or not mentor_profile.hourly_rate:
        raise HTTPException(status_code=400, detail="Mentor must have bio and hourly rate set before sending invitations")

    # Create invite
    invite = MentorInstituteInviteModel(
        mentor_id=mentor_id,
        institute_id=invite_data.receiver_id,
        receiver_id=invite_data.receiver_id,  # Who receives the invite (institute in this case)
        invitation_message=invite_data.invitation_message,
        proposed_hourly_rate=invite_data.proposed_hourly_rate,
        proposed_hours_per_week=invite_data.proposed_hours_per_week,
        expertise_areas_needed=invite_data.expertise_areas_needed,
        contract_terms=invite_data.contract_terms,
        expires_at=invite_data.expires_at,
        # Legacy fields for backward compatibility
        hourly_rate=invite_data.hourly_rate or invite_data.proposed_hourly_rate,
        hours_per_week=invite_data.hours_per_week or invite_data.proposed_hours_per_week,
        received_by="institute"  # Institute receives this invite
    )
    db.add(invite)
    db.commit()
    db.refresh(invite)

    return MentorInstituteInviteOut.model_validate(invite)


def create_invite_to_mentor(
    db: Session,
    institute_id: uuid.UUID,
    invite_data: MentorInstituteInviteSchema
) -> MentorInstituteInviteOut:
    """Institute sends invite to mentor"""

    # Verify institute exists
    institute = db.query(User).filter(
        User.id == institute_id,
        User.user_type == UserTypeEnum.institute
    ).first()

    if not institute:
        raise HTTPException(status_code=404, detail="Institute not found")

    # Verify mentor exists
    mentor = db.query(User).filter(
        User.id == invite_data.receiver_id,
        User.user_type == UserTypeEnum.mentor
    ).first()

    if not mentor:
        raise HTTPException(status_code=404, detail="Mentor not found")

    # Check if invite already exists
    existing_invite = db.query(MentorInstituteInviteModel).filter(
        MentorInstituteInviteModel.mentor_id == invite_data.receiver_id,
        MentorInstituteInviteModel.institute_id == institute_id,
        MentorInstituteInviteModel.status == "pending"
    ).first()

    if existing_invite:
        raise HTTPException(status_code=400, detail="Pending invite already exists")

    # Validate required fields
    if not invite_data.invitation_message or len(invite_data.invitation_message.strip()) < 10:
        raise HTTPException(status_code=400, detail="Invitation message must be at least 10 characters long")
    if not invite_data.proposed_hourly_rate or invite_data.proposed_hourly_rate <= 0:
        raise HTTPException(status_code=400, detail="Valid hourly rate is required (must be greater than 0)")
    if not invite_data.proposed_hours_per_week or invite_data.proposed_hours_per_week <= 0 or invite_data.proposed_hours_per_week > 168:
        raise HTTPException(status_code=400, detail="Valid hours per week is required (1-168 hours)")

    # Validate institute has complete profile
    institute_profile = db.query(InstituteProfile).filter(InstituteProfile.user_id == institute_id).first()
    if not institute_profile:
        raise HTTPException(status_code=400, detail="Institute must complete profile before sending invitations")
    if not institute_profile.institute_name or not institute_profile.description:
        raise HTTPException(status_code=400, detail="Institute must have name and description set before sending invitations")

    # Create invite
    invite = MentorInstituteInviteModel(
        mentor_id=invite_data.receiver_id,
        institute_id=institute_id,
        receiver_id=invite_data.receiver_id,  # Who receives the invite (mentor in this case)
        invitation_message=invite_data.invitation_message,
        proposed_hourly_rate=invite_data.proposed_hourly_rate,
        proposed_hours_per_week=invite_data.proposed_hours_per_week,
        expertise_areas_needed=invite_data.expertise_areas_needed,
        contract_terms=invite_data.contract_terms,
        expires_at=invite_data.expires_at,
        # Legacy fields for backward compatibility
        hourly_rate=invite_data.hourly_rate or invite_data.proposed_hourly_rate,
        hours_per_week=invite_data.hours_per_week or invite_data.proposed_hours_per_week,
        received_by="mentor"  # Mentor receives this invite
    )
    db.add(invite)
    db.commit()
    db.refresh(invite)

    return MentorInstituteInviteOut.model_validate(invite)

def list_sent_invitations(
    db: Session,
    sender_id: uuid.UUID,
    sender_type: str,
    page: int = 1,
    size: int = 20,
    status_filter: Optional[str] = None
) -> InvitationListResponse:
    """List invitations sent by a user (mentor or institute)"""

    skip = (page - 1) * size

    # Build query and fetch sender data based on sender type
    if sender_type == "mentor":
        query = db.query(MentorInstituteInviteModel).filter(
            MentorInstituteInviteModel.mentor_id == sender_id,
            MentorInstituteInviteModel.received_by == "institute"
        )
        # Fetch mentor user and profile data
        user_data = db.query(User).filter(User.id == sender_id).first()
        profile_data = db.query(MentorProfile).filter(MentorProfile.user_id == sender_id).first()

        # Optimize image processing - determine primary image URL first
        profile_pic = user_data.profile_picture if user_data else None
        primary_image_url = None
        if profile_data and profile_data.profile_image_url:
            primary_image_url = profile_data.profile_image_url
        elif profile_pic:
            primary_image_url = profile_pic

        # Process image data only once
        profile_image_data = None
        if primary_image_url:
            try:
                profile_image_data = get_profile_image_data(primary_image_url)
            except Exception:
                profile_image_data = None

    elif sender_type == "institute":
        from Models.users import InviteReceivedByEnum
        query = db.query(MentorInstituteInviteModel).filter(
            MentorInstituteInviteModel.institute_id == sender_id,
            MentorInstituteInviteModel.received_by == InviteReceivedByEnum.mentor
        )
        # Fetch institute user and profile data
        user_data = db.query(User).filter(User.id == sender_id).first()
        profile_data = db.query(InstituteProfile).filter(InstituteProfile.user_id == sender_id).first()

        # Optimize image processing - determine primary image URL first
        profile_pic = user_data.profile_picture if user_data else None
        primary_image_url = None
        if profile_data and profile_data.logo_url:
            primary_image_url = profile_data.logo_url
        elif profile_pic:
            primary_image_url = profile_pic

        # Process image data only once
        profile_image_data = None
        institute_logo_data = None
        if primary_image_url:
            try:
                image_data = get_profile_image_data(primary_image_url)
                if profile_data and profile_data.logo_url:
                    institute_logo_data = image_data
                else:
                    profile_image_data = image_data
            except Exception:
                pass
    else:
        raise HTTPException(status_code=400, detail="Invalid sender type")

    if status_filter:
        query = query.filter(MentorInstituteInviteModel.status == status_filter)

    total = query.count()
    invites = query.order_by(desc(MentorInstituteInviteModel.invited_at)).offset(skip).limit(size).all()

    # Convert to response format with sender details
    invite_details = []
    for invite in invites:
        invite_out = MentorInstituteInviteOut.model_validate(invite)

        # Convert expertise area UUIDs to subject names
        if invite.expertise_areas_needed:
            invite_out.expertise_areas_needed_uuids = invite.expertise_areas_needed  # Keep original UUIDs
            invite_out.expertise_areas_needed = _convert_subject_uuids_to_names(db, invite.expertise_areas_needed)
        else:
            invite_out.expertise_areas_needed = []
            invite_out.expertise_areas_needed_uuids = []

        # Build sender details from fetched data
        if sender_type == "mentor":
            # Parse mentor languages with proper JSON handling
            mentor_languages = None
            if profile_data and profile_data.languages:
                try:
                    if isinstance(profile_data.languages, str):
                        # Clean up escaped quotes and parse as JSON
                        cleaned_languages = profile_data.languages.replace('\\"', '"')
                        mentor_languages = json.loads(cleaned_languages)
                    else:
                        mentor_languages = profile_data.languages
                except (json.JSONDecodeError, TypeError):
                    # Fallback parsing
                    if isinstance(profile_data.languages, str):
                        if ',' in profile_data.languages:
                            mentor_languages = [lang.strip().strip('"\'') for lang in profile_data.languages.split(',')]
                        else:
                            mentor_languages = [profile_data.languages.strip('"\'')]
                    else:
                        mentor_languages = []

            # Parse availability hours from JSON with proper handling
            mentor_availability_hours = None
            if profile_data and profile_data.availability_hours:
                try:
                    if isinstance(profile_data.availability_hours, str):
                        # Clean up escaped quotes and parse as JSON
                        cleaned_hours = profile_data.availability_hours.replace('\\"', '"')
                        mentor_availability_hours = json.loads(cleaned_hours)
                    elif isinstance(profile_data.availability_hours, dict):
                        mentor_availability_hours = profile_data.availability_hours
                except (json.JSONDecodeError, TypeError):
                    mentor_availability_hours = None

            sender_details = InvitationSenderDetails(
                id=user_data.id,
                username=user_data.username or "",
                email=user_data.email or "",
                profile_picture=profile_pic,
                profile_image=profile_image_data,
                # Mentor fields - Complete details
                mentor_bio=profile_data.bio if profile_data else None,
                mentor_experience_years=profile_data.experience_years if profile_data else None,
                mentor_hourly_rate=float(profile_data.hourly_rate) if profile_data and profile_data.hourly_rate else None,
                mentor_languages=mentor_languages,
                mentor_full_name=profile_data.full_name if profile_data else None,
                mentor_current_position=profile_data.current_position if profile_data else None,
                mentor_current_organization=profile_data.current_organization if profile_data else None,
                mentor_education=profile_data.education if profile_data else None,
                mentor_certifications=profile_data.certifications if profile_data else None,
                mentor_is_verified=profile_data.is_verified if profile_data else False,
                mentor_rating=float(profile_data.rating) if profile_data and profile_data.rating and profile_data.rating > 0 else None,
                mentor_phone=profile_data.phone if profile_data else None,
                mentor_linkedin_url=profile_data.linkedin_url if profile_data else None,
                mentor_website=profile_data.website if profile_data else None,
                mentor_portfolio_url=profile_data.portfolio_url if profile_data else None,
                mentor_resume_url=profile_data.resume_url if profile_data else None,
                mentor_availability_hours=mentor_availability_hours,
                mentor_verification_status=profile_data.verification_status if profile_data else "pending",
                mentor_total_reviews=profile_data.total_reviews if profile_data else 0,
                # Institute fields as None
                institute_name=None,
                institute_description=None,
                institute_website=None,
                institute_city=None,
                institute_country=None,
                institute_is_verified=None,
                institute_logo=None
            )
        else:  # institute
            sender_details = InvitationSenderDetails(
                id=user_data.id,
                username=user_data.username or "",
                email=user_data.email or "",
                profile_picture=profile_pic,
                profile_image=profile_image_data,
                # Institute fields
                institute_name=profile_data.institute_name if profile_data else "Unknown Institute",
                institute_description=profile_data.description if profile_data else None,
                institute_website=profile_data.website if profile_data else None,
                institute_city=profile_data.city if profile_data else None,
                institute_country=user_data.country if user_data else None,
                institute_is_verified=profile_data.is_verified if profile_data else False,
                institute_logo=institute_logo_data,
                # Mentor fields as None
                mentor_bio=None,
                mentor_experience_years=None,
                mentor_hourly_rate=None,
                mentor_languages=None
            )

        # Get receiver details
        receiver_details = _get_user_profile_details(db, invite.receiver_id, "institute" if invite.received_by == "institute" else "mentor")

        invite_out.sender = sender_details
        invite_out.receiver = receiver_details
        invite_details.append(invite_out)

    return InvitationListResponse(
        invitations=invite_details,
        total=total,
        page=page,
        size=size,
        has_next=(skip + size) < total,
        has_prev=page > 1
    )




def list_received_invitations(
    db: Session,
    receiver_id: uuid.UUID,
    page: int = 1,
    size: int = 20,
    status_filter: Optional[str] = None
) -> InvitationListResponse:
    """List invitations received by a user (mentor or institute)"""

    skip = (page - 1) * size

    query = db.query(MentorInstituteInviteModel).filter(
        MentorInstituteInviteModel.receiver_id == receiver_id
    )

    if status_filter:
        query = query.filter(MentorInstituteInviteModel.status == status_filter)

    total = query.count()
    invites = query.order_by(desc(MentorInstituteInviteModel.invited_at)).offset(skip).limit(size).all()

    # Convert to response format with sender and receiver details
    invite_details = []
    for invite in invites:
        invite_out = MentorInstituteInviteOut.model_validate(invite)

        # Convert expertise area UUIDs to subject names
        if invite.expertise_areas_needed:
            invite_out.expertise_areas_needed_uuids = invite.expertise_areas_needed  # Keep original UUIDs
            invite_out.expertise_areas_needed = _convert_subject_uuids_to_names(db, invite.expertise_areas_needed)
        else:
            invite_out.expertise_areas_needed = []
            invite_out.expertise_areas_needed_uuids = []

        # Determine sender and receiver - handle both string and enum values
        received_by_value = invite.received_by.value if hasattr(invite.received_by, 'value') else str(invite.received_by)

        if received_by_value == "mentor":
            # Institute is the sender, Mentor is the receiver
            sender_id = invite.institute_id
            sender_type = "institute"
            invite_receiver_id = invite.mentor_id
            receiver_type = "mentor"
        elif received_by_value == "institute":
            # Mentor is the sender, Institute is the receiver
            sender_id = invite.mentor_id
            sender_type = "mentor"
            invite_receiver_id = invite.institute_id
            receiver_type = "institute"
        else:
            invite_details.append(invite_out)
            continue

        # Get sender details
        sender_details = _get_user_profile_details(db, sender_id, sender_type)

        # Get receiver details
        receiver_details = _get_user_profile_details(db, invite_receiver_id, receiver_type)

        # Populate both sender and receiver
        invite_out.sender = sender_details
        invite_out.receiver = receiver_details
        invite_details.append(invite_out)

    return InvitationListResponse(
        invitations=invite_details,
        total=total,
        page=page,
        size=size,
        has_next=(skip + size) < total,
        has_prev=page > 1
    )



def respond_to_received_invite(
    db: Session,
    user_id: uuid.UUID,
    invite_id: uuid.UUID,
    response: dict
) -> CollaborationDetails:
    """User (mentor or institute) responds to an invite they received"""

    # Get the invite - check both mentor and institute scenarios
    from Models.users import InviteReceivedByEnum
    invite = db.query(MentorInstituteInviteModel).filter(
        MentorInstituteInviteModel.id == invite_id,
        or_(
            and_(
                MentorInstituteInviteModel.receiver_id == user_id,
                MentorInstituteInviteModel.received_by == InviteReceivedByEnum.mentor
            ),
            and_(
                MentorInstituteInviteModel.receiver_id == user_id,
                MentorInstituteInviteModel.received_by == InviteReceivedByEnum.institute
            )
        )
    ).first()

    if not invite:
        raise HTTPException(status_code=404, detail="Invite not found")

    if invite.status != "pending":
        raise HTTPException(status_code=400, detail="Invite already responded to")

    # Update invite status based on response
    action = response.get("action", "").lower()

    if action == "accept":
        invite.status = "accepted"
        invite.responded_at = datetime.now(timezone.utc)

        # Create collaboration
        collaboration = MentorInstituteAssociation(
            mentor_id=invite.mentor_id,
            institute_id=invite.institute_id,
            status="active",
            hourly_rate=response.get("hourly_rate") or invite.proposed_hourly_rate or invite.hourly_rate,
            hours_per_week=response.get("hours_per_week") or invite.proposed_hours_per_week or invite.hours_per_week,
            start_date=datetime.now(timezone.utc),
            created_from_invite_id=invite.id
        )

        db.add(collaboration)
        db.commit()
        db.refresh(collaboration)

        # Import the helper function from our new CRUD
        from Cruds.Mentors.CollaborationCrud import _format_collaboration_details
        return _format_collaboration_details(db, collaboration)

    elif action == "reject":
        invite.status = "declined"
        invite.responded_at = datetime.now(timezone.utc)
        invite.response_message = response.get("message", "Invitation declined")
        db.commit()
        return None  # No collaboration created for rejection

    else:
        raise HTTPException(status_code=400, detail="Invalid action. Use 'accept' or 'reject'")





