-- PostgreSQL script to add missing fields to mentor_profiles table
-- Run this script to ensure all mentor profile fields exist

-- Add missing columns if they don't exist
DO $$
BEGIN
    -- Check and add full_name column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'mentor_profiles' AND column_name = 'full_name') THEN
        ALTER TABLE mentor_profiles ADD COLUMN full_name VARCHAR(255);
        RAISE NOTICE 'Added full_name column to mentor_profiles';
    END IF;

    -- Check and add phone column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'mentor_profiles' AND column_name = 'phone') THEN
        ALTER TABLE mentor_profiles ADD COLUMN phone VARCHAR(20);
        RAISE NOTICE 'Added phone column to mentor_profiles';
    END IF;

    -- Check and add linkedin_url column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'mentor_profiles' AND column_name = 'linkedin_url') THEN
        ALTER TABLE mentor_profiles ADD COLUMN linkedin_url VARCHAR(500);
        RAISE NOTICE 'Added linkedin_url column to mentor_profiles';
    END IF;

    -- Check and add website column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'mentor_profiles' AND column_name = 'website') THEN
        ALTER TABLE mentor_profiles ADD COLUMN website VARCHAR(500);
        RAISE NOTICE 'Added website column to mentor_profiles';
    END IF;

    -- Check and add current_position column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'mentor_profiles' AND column_name = 'current_position') THEN
        ALTER TABLE mentor_profiles ADD COLUMN current_position VARCHAR(255);
        RAISE NOTICE 'Added current_position column to mentor_profiles';
    END IF;

    -- Check and add current_organization column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'mentor_profiles' AND column_name = 'current_organization') THEN
        ALTER TABLE mentor_profiles ADD COLUMN current_organization VARCHAR(255);
        RAISE NOTICE 'Added current_organization column to mentor_profiles';
    END IF;

    -- Check and add education column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'mentor_profiles' AND column_name = 'education') THEN
        ALTER TABLE mentor_profiles ADD COLUMN education TEXT;
        RAISE NOTICE 'Added education column to mentor_profiles';
    END IF;

    -- Check and add certifications column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'mentor_profiles' AND column_name = 'certifications') THEN
        ALTER TABLE mentor_profiles ADD COLUMN certifications TEXT;
        RAISE NOTICE 'Added certifications column to mentor_profiles';
    END IF;

    -- Check and add portfolio_url column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'mentor_profiles' AND column_name = 'portfolio_url') THEN
        ALTER TABLE mentor_profiles ADD COLUMN portfolio_url VARCHAR(500);
        RAISE NOTICE 'Added portfolio_url column to mentor_profiles';
    END IF;

    -- Check and add resume_url column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'mentor_profiles' AND column_name = 'resume_url') THEN
        ALTER TABLE mentor_profiles ADD COLUMN resume_url VARCHAR(500);
        RAISE NOTICE 'Added resume_url column to mentor_profiles';
    END IF;

    -- Check and add languages column (JSON)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'mentor_profiles' AND column_name = 'languages') THEN
        ALTER TABLE mentor_profiles ADD COLUMN languages JSON;
        RAISE NOTICE 'Added languages column to mentor_profiles';
    END IF;

    -- Check and add availability_hours column (JSON)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'mentor_profiles' AND column_name = 'availability_hours') THEN
        ALTER TABLE mentor_profiles ADD COLUMN availability_hours JSON;
        RAISE NOTICE 'Added availability_hours column to mentor_profiles';
    END IF;

    -- Check and add is_verified column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'mentor_profiles' AND column_name = 'is_verified') THEN
        ALTER TABLE mentor_profiles ADD COLUMN is_verified BOOLEAN DEFAULT FALSE NOT NULL;
        RAISE NOTICE 'Added is_verified column to mentor_profiles';
    END IF;

    -- Check and add verification_status column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'mentor_profiles' AND column_name = 'verification_status') THEN
        ALTER TABLE mentor_profiles ADD COLUMN verification_status VARCHAR(50) DEFAULT 'pending' NOT NULL;
        RAISE NOTICE 'Added verification_status column to mentor_profiles';
    END IF;

    -- Check and add rating column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'mentor_profiles' AND column_name = 'rating') THEN
        ALTER TABLE mentor_profiles ADD COLUMN rating NUMERIC(3,2) DEFAULT 0.0 NOT NULL;
        RAISE NOTICE 'Added rating column to mentor_profiles';
    END IF;

    -- Check and add total_reviews column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'mentor_profiles' AND column_name = 'total_reviews') THEN
        ALTER TABLE mentor_profiles ADD COLUMN total_reviews INTEGER DEFAULT 0 NOT NULL;
        RAISE NOTICE 'Added total_reviews column to mentor_profiles';
    END IF;

    RAISE NOTICE 'Mentor profile fields check completed successfully!';
END $$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_mentor_profiles_full_name ON mentor_profiles(full_name);
CREATE INDEX IF NOT EXISTS idx_mentor_profiles_current_position ON mentor_profiles(current_position);
CREATE INDEX IF NOT EXISTS idx_mentor_profiles_current_organization ON mentor_profiles(current_organization);
CREATE INDEX IF NOT EXISTS idx_mentor_profiles_is_verified ON mentor_profiles(is_verified);
CREATE INDEX IF NOT EXISTS idx_mentor_profiles_verification_status ON mentor_profiles(verification_status);
CREATE INDEX IF NOT EXISTS idx_mentor_profiles_rating ON mentor_profiles(rating);

-- Update any existing records with default values if needed
UPDATE mentor_profiles 
SET 
    is_verified = COALESCE(is_verified, FALSE),
    verification_status = COALESCE(verification_status, 'pending'),
    rating = COALESCE(rating, 0.0),
    total_reviews = COALESCE(total_reviews, 0)
WHERE 
    is_verified IS NULL 
    OR verification_status IS NULL 
    OR rating IS NULL 
    OR total_reviews IS NULL;

-- Display current mentor_profiles table structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'mentor_profiles' 
ORDER BY ordinal_position;
