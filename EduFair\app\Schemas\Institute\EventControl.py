"""
Institute Event Control Schemas for EduFair Platform

This module contains Pydantic schemas for institute event management including:
- Registration management and control
- Refund and cancellation handling
- Ticket revocation and transfers
- Event analytics and reporting
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator
from datetime import datetime
from uuid import UUID
from decimal import Decimal
from enum import Enum

# Import Enums
from Models.Events import (
    RegistrationStatusEnum, PaymentStatusEnum, EventStatusEnum,
    EventCategoryEnum, TicketStatusEnum
)


class RefundReason(str, Enum):
    """Reasons for refunding a registration"""
    USER_REQUEST = "user_request"
    EVENT_CANCELLED = "event_cancelled"
    EVENT_POSTPONED = "event_postponed"
    TECHNICAL_ISSUE = "technical_issue"
    DUPLICATE_BOOKING = "duplicate_booking"
    POLICY_VIOLATION = "policy_violation"
    INSTITUTE_DECISION = "institute_decision"
    OTHER = "other"


class RefundType(str, Enum):
    """Types of refunds"""
    FULL = "full"
    PARTIAL = "partial"
    NONE = "none"


class RegistrationAction(str, Enum):
    """Actions that can be performed on registrations"""
    APPROVE = "approve"
    REJECT = "reject"
    CANCEL = "cancel"
    REFUND = "refund"
    MARK_ATTENDED = "mark_attended"
    TRANSFER_TICKET = "transfer_ticket"
    UPDATE_INFO = "update_info"
    SEND_REMINDER = "send_reminder"


class InstituteRegistrationControl(BaseModel):
    """Registration control request from institute"""
    registration_ids: List[UUID] = Field(..., description="List of registration IDs to process")
    action: RegistrationAction = Field(..., description="Action to perform")
    reason: Optional[str] = Field(None, description="Reason for the action")
    
    # Refund-specific fields
    refund_type: Optional[RefundType] = Field(None, description="Type of refund")
    refund_amount: Optional[Decimal] = Field(None, description="Custom refund amount for partial refunds")
    refund_reason: Optional[RefundReason] = Field(None, description="Reason for refund")
    
    # Transfer-specific fields
    new_user_email: Optional[str] = Field(None, description="Email of user to transfer ticket to")
    
    # Update-specific fields
    updated_info: Optional[Dict[str, Any]] = Field(None, description="Updated registration information")
    
    # Notification fields
    send_notification: bool = Field(True, description="Whether to send notification to user")
    custom_message: Optional[str] = Field(None, description="Custom message to include in notification")


class RegistrationControlResult(BaseModel):
    """Result of registration control action"""
    registration_id: UUID
    action_performed: RegistrationAction
    success: bool
    message: str
    
    # Action-specific results
    refund_amount: Optional[Decimal] = None
    refund_reference: Optional[str] = None
    new_status: Optional[RegistrationStatusEnum] = None
    notification_sent: bool = False
    
    # Error details
    error_code: Optional[str] = None
    error_details: Optional[str] = None


class BulkRegistrationControlResponse(BaseModel):
    """Response for bulk registration control operations"""
    total_processed: int
    successful_actions: int
    failed_actions: int
    results: List[RegistrationControlResult]
    
    # Summary
    total_refund_amount: Decimal = Field(default=Decimal('0.00'))
    notifications_sent: int = 0
    
    # Processing metadata
    processed_at: datetime = Field(default_factory=lambda: datetime.now())
    processed_by: str


class InstituteEventAnalytics(BaseModel):
    """Comprehensive event analytics for institutes"""
    event_id: UUID
    event_title: str
    event_status: EventStatusEnum
    
    # Registration metrics
    total_registrations: int = 0
    confirmed_registrations: int = 0
    pending_registrations: int = 0
    cancelled_registrations: int = 0
    attended_registrations: int = 0
    
    # Financial metrics
    total_revenue: Decimal = Field(default=Decimal('0.00'))
    pending_revenue: Decimal = Field(default=Decimal('0.00'))
    refunded_amount: Decimal = Field(default=Decimal('0.00'))
    net_revenue: Decimal = Field(default=Decimal('0.00'))
    
    # Ticket metrics
    total_tickets_available: int = 0
    tickets_sold: int = 0
    tickets_remaining: int = 0
    
    # Attendance metrics
    attendance_rate: float = 0.0  # Percentage
    no_show_rate: float = 0.0     # Percentage
    
    # Time-based metrics
    registrations_by_day: Dict[str, int] = Field(default_factory=dict)
    revenue_by_day: Dict[str, Decimal] = Field(default_factory=dict)
    
    # User demographics
    registrations_by_user_type: Dict[str, int] = Field(default_factory=dict)
    
    # Generated metadata
    generated_at: datetime = Field(default_factory=lambda: datetime.now())
    last_updated: Optional[datetime] = None


class EventRegistrationSummary(BaseModel):
    """Summary of a registration for institute view"""
    registration_id: UUID
    registration_number: str
    user_id: UUID
    user_name: str
    user_email: str
    user_type: str
    
    # Registration details
    status: RegistrationStatusEnum
    quantity: int
    total_amount: Decimal
    currency: str
    registered_at: datetime
    confirmed_at: Optional[datetime] = None
    attended_at: Optional[datetime] = None
    cancelled_at: Optional[datetime] = None
    
    # Payment details
    payment_status: PaymentStatusEnum
    payment_reference: Optional[str] = None
    payment_method: Optional[str] = None
    
    # Ticket details
    ticket_id: Optional[UUID] = None
    ticket_name: Optional[str] = None
    ticket_price: Decimal = Field(default=Decimal('0.00'))
    
    # Additional info
    attendee_info: Optional[Dict[str, Any]] = None
    special_requirements: Optional[str] = None
    emergency_contact: Optional[Dict[str, str]] = None
    
    # Control flags
    can_refund: bool = False
    can_transfer: bool = False
    can_modify: bool = False
    
    # QR codes
    qr_code: Optional[str] = None
    check_in_code: Optional[str] = None

    class Config:
        from_attributes = True


class InstituteEventRegistrationsResponse(BaseModel):
    """Paginated response for institute event registrations"""
    event_id: UUID
    event_title: str
    registrations: List[EventRegistrationSummary]
    
    # Pagination
    total_count: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool
    
    # Quick stats
    confirmed_count: int = 0
    pending_count: int = 0
    cancelled_count: int = 0
    attended_count: int = 0
    
    # Financial summary
    total_revenue: Decimal = Field(default=Decimal('0.00'))
    pending_revenue: Decimal = Field(default=Decimal('0.00'))


class EventCancellationRequest(BaseModel):
    """Request to cancel an event"""
    cancellation_reason: str = Field(..., description="Reason for cancelling the event")
    refund_policy: RefundType = Field(RefundType.FULL, description="Refund policy for attendees")
    custom_refund_percentage: Optional[float] = Field(None, ge=0, le=100, description="Custom refund percentage for partial refunds")
    
    # Notification settings
    notify_attendees: bool = Field(True, description="Send cancellation notifications to attendees")
    notification_message: str = Field(..., description="Message to send to attendees")
    
    # Rescheduling options
    is_rescheduled: bool = Field(False, description="Whether event is being rescheduled")
    new_start_datetime: Optional[datetime] = Field(None, description="New event start time if rescheduled")
    new_end_datetime: Optional[datetime] = Field(None, description="New event end time if rescheduled")
    new_location: Optional[str] = Field(None, description="New event location if changed")


class EventCancellationResponse(BaseModel):
    """Response for event cancellation"""
    event_id: UUID
    cancellation_successful: bool
    message: str
    
    # Refund processing
    total_registrations_affected: int = 0
    refunds_processed: int = 0
    total_refund_amount: Decimal = Field(default=Decimal('0.00'))
    
    # Notifications
    notifications_sent: int = 0
    notification_failures: int = 0
    
    # Processing details
    cancelled_at: datetime = Field(default_factory=lambda: datetime.now())
    cancelled_by: str
    
    # Rescheduling info
    rescheduled: bool = False
    new_event_details: Optional[Dict[str, Any]] = None


class TicketTransferRequest(BaseModel):
    """Request to transfer a ticket to another user"""
    registration_id: UUID = Field(..., description="Registration ID to transfer")
    new_user_email: str = Field(..., description="Email of the new ticket holder")
    transfer_reason: str = Field(..., description="Reason for the transfer")
    
    # Transfer settings
    notify_original_user: bool = Field(True, description="Notify original user about transfer")
    notify_new_user: bool = Field(True, description="Notify new user about transfer")
    custom_message: Optional[str] = Field(None, description="Custom message for notifications")
    
    # Validation
    require_new_user_confirmation: bool = Field(True, description="Require new user to confirm transfer")


class TicketTransferResponse(BaseModel):
    """Response for ticket transfer"""
    transfer_successful: bool
    message: str
    
    # Transfer details
    original_user_email: str
    new_user_email: str
    registration_id: UUID
    new_registration_id: Optional[UUID] = None
    
    # Notifications
    original_user_notified: bool = False
    new_user_notified: bool = False
    
    # Processing details
    transferred_at: datetime = Field(default_factory=lambda: datetime.now())
    transferred_by: str
    
    # Confirmation details
    confirmation_required: bool = False
    confirmation_token: Optional[str] = None
    confirmation_expires_at: Optional[datetime] = None
