"""
Event Tickets Routes for EduFair Platform

This module contains all API routes for Event Ticket management including:
- Ticket CRUD operations
- Ticket availability checking
- Ticket sales analytics
"""

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from typing import List
from uuid import UUID

# Import dependencies
from config.session import get_db
from config.deps import oauth2_scheme, get_current_user
from config.permission import require_type

# Import CRUD operations
from Cruds.Events.EventTickets import (
    create_event_ticket, get_event_ticket_by_id, get_tickets_by_event,
    update_event_ticket, delete_event_ticket, check_ticket_availability,
    get_ticket_sales_summary
)

# Import schemas
from Schemas.Events.Events import (
    EventTicketCreate, EventTicketUpdate, EventTicketOut
)

router = APIRouter()


# ==================== EVENT TICKET ROUTES ====================

@router.post("/events/{event_id}/tickets", response_model=EventTicketOut)
def create_event_ticket_endpoint(
    event_id: UUID,
    ticket: EventTicketCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Create a new ticket for an event.
    
    Only event organizers (institutes) can create tickets.
    """
    current_user = get_current_user(token, db)
    # Set event_id in ticket data
    ticket.event_id = event_id
    return create_event_ticket(db, ticket, current_user.id)


@router.get("/events/{event_id}/tickets", response_model=List[EventTicketOut])
def get_tickets_by_event_endpoint(
    event_id: UUID,
    db: Session = Depends(get_db)
):
    """
    Get all tickets for a specific event.
    
    Public endpoint - no authentication required.
    """
    return get_tickets_by_event(db, event_id)


@router.get("/tickets/{ticket_id}", response_model=EventTicketOut)
def get_event_ticket_by_id_endpoint(
    ticket_id: UUID,
    db: Session = Depends(get_db)
):
    """
    Get event ticket by ID.
    
    Public endpoint - no authentication required.
    """
    return get_event_ticket_by_id(db, ticket_id)


@router.put("/tickets/{ticket_id}", response_model=EventTicketOut)
def update_event_ticket_endpoint(
    ticket_id: UUID,
    ticket_update: EventTicketUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Update an existing event ticket.
    
    Only event organizers (institutes) can update tickets.
    """
    current_user = get_current_user(token, db)
    return update_event_ticket(db, ticket_id, ticket_update, current_user.id)


@router.delete("/tickets/{ticket_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_event_ticket_endpoint(
    ticket_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Delete an event ticket.
    
    Only event organizers (institutes) can delete tickets.
    Cannot delete tickets with existing registrations.
    """
    current_user = get_current_user(token, db)
    delete_event_ticket(db, ticket_id, current_user.id)
    return None


@router.get("/tickets/{ticket_id}/availability")
def check_ticket_availability_endpoint(
    ticket_id: UUID,
    quantity: int = Query(1, ge=1, le=100, description="Number of tickets to check"),
    db: Session = Depends(get_db)
):
    """
    Check if ticket is available for purchase.
    
    Public endpoint - no authentication required.
    """
    is_available = check_ticket_availability(db, ticket_id, quantity)
    return {
        "ticket_id": ticket_id,
        "quantity": quantity,
        "available": is_available
    }


@router.get("/events/{event_id}/tickets/sales-summary")
def get_ticket_sales_summary_endpoint(
    event_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Get ticket sales summary for an event.
    
    Only event organizers (institutes) can view sales data.
    """
    current_user = get_current_user(token, db)
    return get_ticket_sales_summary(db, event_id, current_user.id)
