"""
Universal Event Access Routes for EduFair Platform

This module provides unified API access for all user types (students, teachers, sponsors, institutes)
to access their event-related data with appropriate permissions and filtering.
"""

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from typing import List, Optional, Union
from uuid import UUID

# Import dependencies
from config.session import get_db
from config.deps import oauth2_scheme, get_current_user

# Import CRUD operations
from Cruds.Users.EventDashboard import (
    get_user_event_dashboard, get_user_events, get_user_event_details,
    get_user_event_stats
)

# Import schemas
from Schemas.Users.EventDashboard import (
    UserEventDashboard, UserEventListResponse, UserEventDetails,
    EventFilterOptions, UserEventStats, EventParticipationStatus
)
from Models.Events import RegistrationStatusEnum, PaymentStatusEnum, EventCategoryEnum
from Models.users import UserTypeEnum

router = APIRouter()


# ==================== UNIVERSAL EVENT ACCESS ====================

@router.get("/my/events/dashboard", response_model=UserEventDashboard)
def get_my_universal_dashboard(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get comprehensive event dashboard for any user type.
    
    Automatically adapts to user type:
    - Students: Personal event registrations and participation
    - Teachers: Events they've registered for or organized
    - Sponsors: Events they've sponsored or participated in
    - Institutes: Events they've organized (redirects to institute dashboard)
    """
    current_user = get_current_user(token, db)
    
    # For institutes, provide a note about using institute-specific endpoints
    if current_user.user_type == UserTypeEnum.institute:
        dashboard = get_user_event_dashboard(db, current_user.id)
        # Add a note for institutes
        dashboard.stats.total_registrations = 0  # Institutes don't "register" for events
        return dashboard
    
    return get_user_event_dashboard(db, current_user.id)


@router.get("/my/events", response_model=UserEventListResponse)
def get_my_universal_events(
    skip: int = Query(0, ge=0, description="Number of events to skip"),
    limit: int = Query(20, ge=1, le=100, description="Number of events to return"),
    
    # Universal filter parameters
    status: Optional[List[RegistrationStatusEnum]] = Query(None, description="Filter by registration status"),
    category: Optional[List[EventCategoryEnum]] = Query(None, description="Filter by event category"),
    participation_status: Optional[List[EventParticipationStatus]] = Query(None, description="Filter by participation status"),
    payment_status: Optional[List[PaymentStatusEnum]] = Query(None, description="Filter by payment status"),
    search: Optional[str] = Query(None, description="Search in event title, description, or location"),
    
    # Time-based filters
    upcoming_only: bool = Query(False, description="Show only upcoming events"),
    past_only: bool = Query(False, description="Show only past events"),
    
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get paginated list of events for any user type with universal filtering.
    
    Adapts behavior based on user type:
    - Students/Teachers/Sponsors: Events they've registered for
    - Institutes: Events they've organized (with note to use institute endpoints)
    """
    current_user = get_current_user(token, db)
    
    # Build filters
    filters = EventFilterOptions(
        status=status,
        category=category,
        participation_status=participation_status,
        payment_status=payment_status,
        search_query=search
    )
    
    # Add time-based filters
    if upcoming_only or past_only:
        from datetime import datetime, timezone
        now = datetime.now(timezone.utc)
        
        if upcoming_only:
            filters.date_from = now
        elif past_only:
            filters.date_to = now
    
    result = get_user_events(db, current_user.id, filters, skip, limit)
    
    # Add user type context to response
    if current_user.user_type == UserTypeEnum.institute:
        # Add note for institutes
        result.filters_applied = filters
    
    return result


@router.get("/my/events/stats", response_model=UserEventStats)
def get_my_universal_stats(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get event participation statistics for any user type.
    
    Provides relevant metrics based on user type:
    - Students: Registration and attendance stats
    - Teachers: Professional development and teaching events
    - Sponsors: Sponsored events and participation
    - Institutes: Basic stats (recommend using institute analytics)
    """
    current_user = get_current_user(token, db)
    return get_user_event_stats(db, current_user.id)


@router.get("/my/events/{registration_id}", response_model=UserEventDetails)
def get_my_universal_event_details(
    registration_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get detailed information about a specific event registration for any user type.
    
    Includes appropriate actions and information based on user type and event status.
    """
    current_user = get_current_user(token, db)
    return get_user_event_details(db, current_user.id, registration_id)


# ==================== USER TYPE SPECIFIC QUICK ACCESS ====================

@router.get("/my/events/quick/upcoming")
def get_my_upcoming_quick(
    limit: int = Query(5, ge=1, le=10, description="Number of upcoming events to return"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Quick access to upcoming events for any user type.
    """
    current_user = get_current_user(token, db)
    
    from datetime import datetime, timezone, timedelta
    now = datetime.now(timezone.utc)
    future_date = now + timedelta(days=30)
    
    filters = EventFilterOptions(
        status=[RegistrationStatusEnum.PENDING, RegistrationStatusEnum.CONFIRMED],
        date_from=now,
        date_to=future_date
    )
    
    result = get_user_events(db, current_user.id, filters, 0, limit)
    
    return {
        "user_type": current_user.user_type.value if hasattr(current_user.user_type, 'value') else str(current_user.user_type),
        "upcoming_events": result.events,
        "total_upcoming": result.total_count
    }


@router.get("/my/events/quick/recent")
def get_my_recent_activity_quick(
    limit: int = Query(5, ge=1, le=10, description="Number of recent activities to return"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Quick access to recent event activity for any user type.
    """
    current_user = get_current_user(token, db)
    
    from datetime import datetime, timezone, timedelta
    now = datetime.now(timezone.utc)
    recent_date = now - timedelta(days=30)
    
    filters = EventFilterOptions(
        date_from=recent_date,
        date_to=now
    )
    
    result = get_user_events(db, current_user.id, filters, 0, limit)
    
    return {
        "user_type": current_user.user_type.value if hasattr(current_user.user_type, 'value') else str(current_user.user_type),
        "recent_activity": result.events,
        "total_recent": result.total_count
    }


# ==================== USER TYPE GUIDANCE ====================

@router.get("/my/events/guidance")
def get_user_type_guidance(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get guidance on available event features based on user type.
    """
    current_user = get_current_user(token, db)
    
    user_type = current_user.user_type.value if hasattr(current_user.user_type, 'value') else str(current_user.user_type)
    
    guidance = {
        "user_type": user_type,
        "available_features": [],
        "recommended_endpoints": [],
        "limitations": []
    }
    
    if user_type == "student":
        guidance.update({
            "available_features": [
                "Event registration and management",
                "Ticket downloads and QR codes",
                "Event history and statistics",
                "Registration cancellation with refunds",
                "Event search and filtering"
            ],
            "recommended_endpoints": [
                "GET /api/universal/my/events/dashboard - Complete dashboard",
                "GET /api/universal/my/events - All registered events",
                "GET /api/users/registrations/{id}/cancel - Cancel registration",
                "GET /api/users/registrations/{id}/ticket - Download ticket"
            ],
            "limitations": [
                "Cannot organize events",
                "Cannot manage other users' registrations"
            ]
        })
    
    elif user_type == "teacher":
        guidance.update({
            "available_features": [
                "Event registration and management",
                "Professional development tracking",
                "Event organization (if authorized)",
                "Student event supervision",
                "Ticket downloads and QR codes"
            ],
            "recommended_endpoints": [
                "GET /api/universal/my/events/dashboard - Complete dashboard",
                "GET /api/universal/my/events - All events",
                "GET /api/events - Browse available events",
                "POST /api/events/{id}/register - Register for events"
            ],
            "limitations": [
                "Limited event organization capabilities",
                "Cannot access institute-level analytics"
            ]
        })
    
    elif user_type == "sponsor":
        guidance.update({
            "available_features": [
                "Event sponsorship tracking",
                "Sponsored event management",
                "Event participation",
                "Sponsorship analytics",
                "Brand visibility tracking"
            ],
            "recommended_endpoints": [
                "GET /api/universal/my/events/dashboard - Sponsorship dashboard",
                "GET /api/universal/my/events - Sponsored/participated events",
                "GET /api/sponsors/events - Sponsorship opportunities"
            ],
            "limitations": [
                "Cannot organize non-sponsored events",
                "Limited access to attendee data"
            ]
        })
    
    elif user_type == "institute":
        guidance.update({
            "available_features": [
                "Complete event management",
                "Registration control and analytics",
                "Refund and cancellation processing",
                "Bulk operations on registrations",
                "Comprehensive event analytics"
            ],
            "recommended_endpoints": [
                "GET /api/institute/events/{id}/registrations - Manage registrations",
                "GET /api/institute/events/{id}/analytics - Event analytics",
                "POST /api/institute/events/{id}/registrations/control - Bulk actions",
                "POST /api/institute/events/{id}/cancel - Cancel events"
            ],
            "limitations": [
                "Universal endpoints show limited data",
                "Should use institute-specific endpoints for full functionality"
            ]
        })
    
    return guidance


@router.get("/my/events/summary")
def get_my_events_summary(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get a quick summary of user's event-related data.
    """
    current_user = get_current_user(token, db)
    
    stats = get_user_event_stats(db, current_user.id)
    dashboard = get_user_event_dashboard(db, current_user.id)
    
    return {
        "user_id": current_user.id,
        "user_type": current_user.user_type.value if hasattr(current_user.user_type, 'value') else str(current_user.user_type),
        "user_name": current_user.username,
        "quick_stats": {
            "total_events": stats.total_registrations,
            "upcoming_events": stats.upcoming_events,
            "attended_events": stats.attended_events,
            "total_spent": float(stats.total_spent)
        },
        "next_event": dashboard.next_event.event_title if dashboard.next_event else None,
        "next_event_date": dashboard.next_event.event_start_datetime.isoformat() if dashboard.next_event else None,
        "recent_activity_count": len(dashboard.recent_registrations),
        "last_activity": stats.last_registration_date.isoformat() if stats.last_registration_date else None
    }
