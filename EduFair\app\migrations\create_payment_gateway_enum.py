"""
Migration to create PaymentGatewayEnum type in the database.

This migration creates the paymentgatewayenum type that is missing from the database
and updates the event_payments table to use it.
"""

from sqlalchemy import text
from config.session import engine


def create_payment_gateway_enum():
    """
    Execute the migration to create PaymentGatewayEnum type.
    """
    
    with engine.connect() as connection:
        # Start transaction
        trans = connection.begin()
        
        try:
            print("Starting migration to create PaymentGatewayEnum...")
            
            # Check if enum already exists
            enum_exists = connection.execute(text("""
                SELECT EXISTS (
                    SELECT FROM pg_type 
                    WHERE typname = 'paymentgatewayenum'
                );
            """)).scalar()
            
            if enum_exists:
                print("PaymentGatewayEnum already exists!")
                trans.commit()
                return
            
            # Create the enum type
            print("Creating PaymentGatewayEnum type...")
            connection.execute(text("""
                CREATE TYPE paymentgatewayenum AS ENUM ('STRIPE', 'PAYPAL', 'RAZORPAY', 'PAYFAST', 'BANK_TRANSFER', 'CASH');
            """))
            
            # Check if event_payments table exists
            payments_table_exists = connection.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'event_payments'
                );
            """)).scalar()
            
            if payments_table_exists:
                # Check current payment_method column type
                column_info = connection.execute(text("""
                    SELECT data_type, udt_name
                    FROM information_schema.columns 
                    WHERE table_name = 'event_payments' AND column_name = 'payment_method';
                """)).fetchone()
                
                if column_info:
                    current_type = column_info[0]
                    print(f"Current payment_method column type: {current_type}")
                    
                    if current_type != 'USER-DEFINED':
                        print("Converting payment_method column to use PaymentGatewayEnum...")
                        
                        # First, let's see what values are currently in the database
                        current_values = connection.execute(text("""
                            SELECT DISTINCT payment_method, COUNT(*) as count
                            FROM event_payments 
                            WHERE payment_method IS NOT NULL
                            GROUP BY payment_method;
                        """)).fetchall()
                        
                        print("Current payment_method values in database:")
                        for row in current_values:
                            print(f"  '{row[0]}': {row[1]} records")
                        
                        # Update existing data to valid enum values
                        print("Updating existing payment_method values...")
                        connection.execute(text("""
                            UPDATE event_payments SET payment_method = 'CASH' 
                            WHERE payment_method IS NULL OR payment_method = '' OR payment_method = 'pending';
                        """))
                        connection.execute(text("""
                            UPDATE event_payments SET payment_method = 'STRIPE' 
                            WHERE payment_method = 'stripe' OR payment_method = 'card';
                        """))
                        connection.execute(text("""
                            UPDATE event_payments SET payment_method = 'PAYPAL' 
                            WHERE payment_method = 'paypal';
                        """))
                        connection.execute(text("""
                            UPDATE event_payments SET payment_method = 'PAYFAST' 
                            WHERE payment_method = 'payfast';
                        """))
                        connection.execute(text("""
                            UPDATE event_payments SET payment_method = 'BANK_TRANSFER' 
                            WHERE payment_method = 'bank_transfer' OR payment_method = 'bank';
                        """))
                        connection.execute(text("""
                            UPDATE event_payments SET payment_method = 'CASH' 
                            WHERE payment_method = 'cash' OR payment_method = 'manual';
                        """))
                        
                        # Convert column to use enum
                        connection.execute(text("""
                            ALTER TABLE event_payments 
                            ALTER COLUMN payment_method TYPE paymentgatewayenum 
                            USING payment_method::paymentgatewayenum;
                        """))
                        
                        print("payment_method column converted to PaymentGatewayEnum")
                else:
                    print("Adding payment_method column to event_payments table...")
                    connection.execute(text("""
                        ALTER TABLE event_payments 
                        ADD COLUMN payment_method paymentgatewayenum DEFAULT 'CASH';
                    """))
            
            # Verify the enum was created
            print("Verifying PaymentGatewayEnum creation...")
            enum_values = connection.execute(text("""
                SELECT enumlabel 
                FROM pg_enum e
                JOIN pg_type t ON e.enumtypid = t.oid
                WHERE t.typname = 'paymentgatewayenum'
                ORDER BY e.enumsortorder;
            """)).fetchall()
            
            enum_value_list = [row[0] for row in enum_values]
            print(f"PaymentGatewayEnum values: {enum_value_list}")
            
            # Commit transaction
            trans.commit()
            print("Migration completed successfully!")
            
        except Exception as e:
            # Rollback on error
            trans.rollback()
            print(f"Migration failed: {e}")
            raise


def rollback_migration():
    """
    Rollback the migration (convert payment_method back to text and drop enum).
    """
    
    with engine.connect() as connection:
        trans = connection.begin()
        
        try:
            print("Starting rollback of PaymentGatewayEnum migration...")
            
            # Convert payment_method column back to text if it exists
            payments_table_exists = connection.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'event_payments'
                );
            """)).scalar()
            
            if payments_table_exists:
                payment_method_column_exists = connection.execute(text("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.columns 
                        WHERE table_name = 'event_payments' AND column_name = 'payment_method'
                    );
                """)).scalar()
                
                if payment_method_column_exists:
                    print("Converting payment_method column back to text...")
                    connection.execute(text("""
                        ALTER TABLE event_payments ALTER COLUMN payment_method TYPE TEXT;
                    """))
            
            # Drop the enum type
            print("Dropping PaymentGatewayEnum type...")
            connection.execute(text("""
                DROP TYPE IF EXISTS paymentgatewayenum CASCADE;
            """))
            
            trans.commit()
            print("Rollback completed successfully!")
            
        except Exception as e:
            trans.rollback()
            print(f"Rollback failed: {e}")
            raise


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "rollback":
        rollback_migration()
    else:
        create_payment_gateway_enum()
