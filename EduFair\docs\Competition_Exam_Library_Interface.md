# Competition Exam Library Interface Documentation
## Simplified Competition Creation with Reusable Exams

---

## 🎯 **Overview**

The new simplified approach allows mentors and teachers to create reusable exams that can be assigned to multiple competitions. This eliminates the need to create exams during competition setup and provides better exam management.

---

## 🔄 **New Workflow**

### **Step 1: Exam Creation (Teachers/Mentors)**
```typescript
// Mentors can now create exams just like teachers
POST /api/exams/create-with-assignment
// OR
POST /api/exams/

// Permissions updated to include mentors
require_type(["teacher", "mentor"])
```

### **Step 2: Competition Creation (Institutes)**
```typescript
interface CompetitionEventCreate {
  title: string;
  description?: string;
  start_datetime: string;
  end_datetime: string;
  category_id: string;
  
  // Simplified exam assignment
  exam_id: string;  // Select from existing exams
  copy_exam: boolean;  // Whether to create a copy
  
  // Competition settings
  competition_rules?: string;
  prize_details?: object;
  judging_type: 'ai_only' | 'mentor_review' | 'hybrid';
  mentor_assignment_strategy: 'auto' | 'manual' | 'volunteer';
  min_mentors_required: number;
}
```

---

## 🏗️ **Frontend Components**

### **1. Exam Library Browser**
```typescript
interface ExamLibraryProps {
  onExamSelect: (exam: ExamInfo) => void;
  userType: 'teacher' | 'mentor' | 'institute';
  filters?: ExamFilters;
}

interface ExamInfo {
  id: string;
  title: string;
  description: string;
  total_marks: number;
  total_duration: number;
  difficulty_level: string;
  question_count: number;
  created_by: string;
  created_at: string;
  usage_info: {
    is_used_in_competitions: boolean;
    competition_count: number;
    student_assignments: number;
    requires_copy_for_update: boolean;
  };
}
```

**Component Structure:**
```jsx
<ExamLibrary>
  <ExamFilters />
  <ExamGrid>
    <ExamCard 
      exam={exam}
      showUsageInfo={true}
      onSelect={handleExamSelect}
      onCopy={handleExamCopy}
      onPreview={handleExamPreview}
    />
  </ExamGrid>
  <ExamPreviewModal />
</ExamLibrary>
```

### **2. Exam Selection Card**
```typescript
interface ExamCardProps {
  exam: ExamInfo;
  isSelected?: boolean;
  showUsageInfo?: boolean;
  onSelect: (examId: string) => void;
  onCopy?: (examId: string) => void;
  onPreview?: (examId: string) => void;
}
```

**Visual Indicators:**
- 🟢 **Available**: Can be used directly
- 🟡 **In Use**: Recommend copying for modifications
- 🔴 **Restricted**: Permission issues
- 📋 **Copy Available**: Shows copy option

### **3. Competition Creation Form (Updated)**
```jsx
<CompetitionCreateForm>
  <BasicInfoSection />
  
  <ExamSelectionSection>
    <ExamLibraryBrowser 
      onExamSelect={setSelectedExam}
    />
    <ExamCopyOption 
      exam={selectedExam}
      copyExam={copyExam}
      onToggle={setCopyExam}
    />
    <ExamUsageWarning 
      exam={selectedExam}
      show={selectedExam?.usage_info.requires_copy_for_update}
    />
  </ExamSelectionSection>
  
  <CompetitionSettingsSection />
  <MentorAssignmentSection />
</CompetitionCreateForm>
```

---

## 🔌 **API Endpoints**

### **Exam Management (Updated Permissions)**
```typescript
// Now available to mentors too
GET    /api/exams/my-exams                    // Get user's exams
POST   /api/exams/                           // Create exam
PUT    /api/exams/{id}                       // Update exam
GET    /api/exams/{id}                       // Get exam details

// New exam copy functionality
POST   /api/exams/{id}/copy                  // Copy exam
GET    /api/exams/{id}/usage-info            // Get usage information
PUT    /api/exams/{id}/update-with-copy      // Smart update (copy if needed)
```

### **Competition-Specific Exam Endpoints**
```typescript
// Get exams available for competitions
GET    /api/competitions/available-exams     // Browse exam library

// Competition creation with exam assignment
POST   /api/competitions/                    // Create with exam_id + copy_exam flag
```

---

## 📊 **Exam Usage Information**

### **Usage Info Response**
```typescript
interface ExamUsageInfo {
  exam_id: string;
  is_used_in_competitions: boolean;
  competition_count: number;
  competition_titles: string[];
  student_assignments: number;
  student_attempts: number;
  is_reusable: boolean;
  requires_copy_for_update: boolean;
}
```

### **Usage Indicators in UI**
```jsx
<ExamUsageIndicator usage={exam.usage_info}>
  {usage.is_used_in_competitions && (
    <Badge color="blue">
      Used in {usage.competition_count} competitions
    </Badge>
  )}
  
  {usage.student_assignments > 0 && (
    <Badge color="green">
      {usage.student_assignments} student assignments
    </Badge>
  )}
  
  {usage.requires_copy_for_update && (
    <Warning>
      Recommend copying before modifications
    </Warning>
  )}
</ExamUsageIndicator>
```

---

## 🎨 **UI/UX Flow**

### **Competition Creation Flow**
1. **Basic Info**: Title, description, dates, category
2. **Exam Selection**: 
   - Browse exam library
   - Filter by subject, difficulty, duration
   - Preview exam questions
   - Select exam
   - Choose copy option if needed
3. **Competition Settings**: Rules, prizes, judging type
4. **Mentor Assignment**: Auto or manual assignment
5. **Review & Create**: Final review before creation

### **Exam Copy Decision Helper**
```jsx
<ExamCopyDecisionHelper exam={selectedExam}>
  <CopyRecommendation>
    {exam.usage_info.requires_copy_for_update ? (
      <Alert type="warning">
        This exam is being used in other competitions. 
        We recommend creating a copy to avoid affecting existing competitions.
      </Alert>
    ) : (
      <Alert type="info">
        This exam is not currently in use. 
        You can use it directly or create a copy for modifications.
      </Alert>
    )}
  </CopyRecommendation>
  
  <CopyOptions>
    <RadioGroup value={copyExam} onChange={setCopyExam}>
      <Radio value={false}>Use original exam</Radio>
      <Radio value={true}>Create a copy for this competition</Radio>
    </RadioGroup>
  </CopyOptions>
</ExamCopyDecisionHelper>
```

---

## 🔄 **State Management**

### **Exam Library State**
```typescript
interface ExamLibraryState {
  exams: ExamInfo[];
  filters: ExamFilters;
  selectedExam: ExamInfo | null;
  copyExam: boolean;
  loading: boolean;
  error: string | null;
}

interface ExamFilters {
  subject?: string;
  difficulty?: string;
  duration_min?: number;
  duration_max?: number;
  created_by?: string;
  search?: string;
}
```

### **Actions**
```typescript
// Exam library actions
fetchAvailableExams()
filterExams(filters: ExamFilters)
selectExam(examId: string)
toggleCopyExam(copy: boolean)
previewExam(examId: string)

// Exam management actions
copyExam(examId: string, newTitle?: string)
getExamUsageInfo(examId: string)
updateExamWithCopy(examId: string, updates: ExamUpdate)
```

---

## 📱 **Mobile Considerations**

### **Responsive Exam Cards**
- **Desktop**: Grid layout with detailed info
- **Tablet**: 2-column grid with condensed info
- **Mobile**: Single column list with essential info only

### **Mobile-Optimized Filters**
- Collapsible filter panel
- Quick filter chips
- Search-first approach

---

## 🚀 **Implementation Benefits**

### **For Users**
1. **Reusable Exams**: Create once, use multiple times
2. **Better Organization**: Centralized exam library
3. **Safer Updates**: Automatic copy creation when needed
4. **Faster Setup**: No need to create exams during competition setup

### **For System**
1. **Data Integrity**: Original exams preserved
2. **Better Performance**: Reduced duplicate exam creation
3. **Cleaner Architecture**: Separation of exam and competition concerns
4. **Easier Maintenance**: Centralized exam management

---

## 🔧 **Technical Implementation Notes**

### **Exam Copy Strategy**
- **Automatic Copy**: When exam is in use and updates are needed
- **Manual Copy**: User chooses to copy during competition creation
- **Smart Updates**: System decides whether to copy or update in place

### **Permission Model**
- **Teachers**: Can create and manage their own exams
- **Mentors**: Can create and manage their own exams (NEW)
- **Institutes**: Can browse and use exams from their teachers/mentors
- **Students**: Can only take assigned exams

### **Data Relationships**
```
Exam (reusable)
├── Questions (copied with exam)
├── Competitions (many-to-many via competition_exam_id)
├── Assignments (many-to-many via StudentExamAssignment)
└── Usage Tracking (for copy decisions)
```

This simplified approach makes the competition system much more user-friendly while maintaining data integrity and providing better exam management capabilities.
