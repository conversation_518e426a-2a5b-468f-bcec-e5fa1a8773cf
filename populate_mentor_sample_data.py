#!/usr/bin/env python3
"""
Script to populate sample mentor profile data for testing
"""
import sys
import os

# Add the EduFair app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'EduFair', 'app'))

try:
    from sqlalchemy.orm import Session
    from config.session import get_db
    from Models.users import User, MentorProfile, UserTypeEnum
    
    def populate_mentor_sample_data():
        """Populate sample data for mentor profiles"""
        print("🔧 Populating Sample Mentor Profile Data")
        print("=" * 50)
        
        # Get database session
        db = next(get_db())
        
        try:
            # Find mentor users
            mentor_users = db.query(User).filter(
                User.user_type == UserTypeEnum.mentor
            ).all()
            
            if not mentor_users:
                print("❌ No mentor users found in database")
                return
            
            print(f"✅ Found {len(mentor_users)} mentor user(s)")
            
            for i, mentor_user in enumerate(mentor_users):
                print(f"\n🔧 Processing mentor: {mentor_user.username} (ID: {mentor_user.id})")
                
                # Get or create mentor profile
                mentor_profile = db.query(MentorProfile).filter(
                    MentorProfile.user_id == mentor_user.id
                ).first()
                
                if not mentor_profile:
                    print("   Creating new mentor profile...")
                    mentor_profile = MentorProfile(user_id=mentor_user.id)
                    db.add(mentor_profile)
                    db.commit()
                    db.refresh(mentor_profile)
                
                # Sample data based on mentor index
                sample_data = [
                    {
                        "full_name": "<PERSON>",
                        "current_position": "Senior Software Engineer",
                        "current_organization": "Tech Innovations Inc",
                        "phone": "******-0123",
                        "linkedin_url": "https://linkedin.com/in/johndoe",
                        "website": "https://johndoe.dev",
                        "education": "MS Computer Science, Stanford University",
                        "certifications": "AWS Certified Solutions Architect, Google Cloud Professional",
                        "portfolio_url": "https://portfolio.johndoe.dev",
                        "resume_url": "https://resume.johndoe.dev"
                    },
                    {
                        "full_name": "Jane Smith",
                        "current_position": "Lead Data Scientist",
                        "current_organization": "AI Solutions Corp",
                        "phone": "******-0456",
                        "linkedin_url": "https://linkedin.com/in/janesmith",
                        "website": "https://janesmith.ai",
                        "education": "PhD Machine Learning, MIT",
                        "certifications": "TensorFlow Developer Certificate, Azure AI Engineer",
                        "portfolio_url": "https://portfolio.janesmith.ai",
                        "resume_url": "https://resume.janesmith.ai"
                    },
                    {
                        "full_name": "Ahmed Khan",
                        "current_position": "Product Manager",
                        "current_organization": "Global Tech Solutions",
                        "phone": "+92-300-1234567",
                        "linkedin_url": "https://linkedin.com/in/ahmedkhan",
                        "website": "https://ahmedkhan.com",
                        "education": "MBA Technology Management, LUMS",
                        "certifications": "PMP Certified, Scrum Master",
                        "portfolio_url": "https://portfolio.ahmedkhan.com",
                        "resume_url": "https://resume.ahmedkhan.com"
                    }
                ]
                
                # Use sample data based on index, cycle if more mentors than samples
                data = sample_data[i % len(sample_data)]
                
                # Update mentor profile with sample data only if fields are empty
                updated_fields = []
                
                if not mentor_profile.full_name:
                    mentor_profile.full_name = data["full_name"]
                    updated_fields.append("full_name")
                
                if not mentor_profile.current_position:
                    mentor_profile.current_position = data["current_position"]
                    updated_fields.append("current_position")
                
                if not mentor_profile.current_organization:
                    mentor_profile.current_organization = data["current_organization"]
                    updated_fields.append("current_organization")
                
                if not mentor_profile.phone:
                    mentor_profile.phone = data["phone"]
                    updated_fields.append("phone")
                
                if not mentor_profile.linkedin_url:
                    mentor_profile.linkedin_url = data["linkedin_url"]
                    updated_fields.append("linkedin_url")
                
                if not mentor_profile.website:
                    mentor_profile.website = data["website"]
                    updated_fields.append("website")
                
                if not mentor_profile.education:
                    mentor_profile.education = data["education"]
                    updated_fields.append("education")
                
                if not mentor_profile.certifications:
                    mentor_profile.certifications = data["certifications"]
                    updated_fields.append("certifications")
                
                if not mentor_profile.portfolio_url:
                    mentor_profile.portfolio_url = data["portfolio_url"]
                    updated_fields.append("portfolio_url")
                
                if not mentor_profile.resume_url:
                    mentor_profile.resume_url = data["resume_url"]
                    updated_fields.append("resume_url")
                
                # Set a sample rating if it's 0
                if mentor_profile.rating == 0:
                    mentor_profile.rating = 4.5 + (i * 0.1)  # Vary ratings between 4.5-4.8
                    mentor_profile.total_reviews = 15 + (i * 5)  # Vary review counts
                    updated_fields.extend(["rating", "total_reviews"])
                
                if updated_fields:
                    db.commit()
                    db.refresh(mentor_profile)
                    print(f"   ✅ Updated fields: {', '.join(updated_fields)}")
                else:
                    print("   ℹ️  All fields already have values")
                
                # Display current profile status
                print(f"   📊 Profile Summary:")
                print(f"      - Full Name: {mentor_profile.full_name}")
                print(f"      - Position: {mentor_profile.current_position}")
                print(f"      - Organization: {mentor_profile.current_organization}")
                print(f"      - Phone: {mentor_profile.phone}")
                print(f"      - Rating: {mentor_profile.rating} ({mentor_profile.total_reviews} reviews)")
            
            print(f"\n✅ Sample data population completed!")
            print(f"📝 Note: Only empty fields were populated. Existing data was preserved.")
            
        except Exception as e:
            print(f"❌ Error during data population: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            db.close()
    
    if __name__ == "__main__":
        populate_mentor_sample_data()
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running this from the correct directory and all dependencies are installed")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
