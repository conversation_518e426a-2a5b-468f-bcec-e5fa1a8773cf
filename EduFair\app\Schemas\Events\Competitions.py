"""
Competition System Schemas for Enhanced Event System
"""
from pydantic import BaseModel, Field, field_validator
from typing import Optional, Dict, Any, List
from uuid import UUID
from datetime import datetime
from enum import Enum

# Import enums from models
# from Models.Competitions import MentorAssignmentStatusEnum  # Not used directly in schemas

# Define missing enums locally since they don't exist in Events model
class JudgingTypeEnum(str, Enum):
    AI_ONLY = "ai_only"
    MENTOR_REVIEW = "mentor_review"
    HYBRID = "hybrid"

class MentorAssignmentStrategyEnum(str, Enum):
    AUTO = "auto"
    MANUAL = "manual"
    VOLUNTEER = "volunteer"


# ==================== SIMPLIFIED COMPETITION CREATION ====================

class CompetitionEventCreate(BaseModel):
    """Simplified schema for creating competition events with exam assignment"""
    title: str = Field(..., max_length=300, description="Competition title")
    description: Optional[str] = Field(None, description="Competition description")
    short_description: Optional[str] = Field(None, max_length=500, description="Brief competition summary")
    start_datetime: datetime = Field(..., description="Competition start time")
    end_datetime: datetime = Field(..., description="Competition end time")
    category_id: UUID = Field(..., description="Event category ID")
    location_id: Optional[UUID] = Field(None, description="Event location ID")
    max_attendees: Optional[int] = Field(None, ge=1, description="Maximum number of participants")

    # Exam assignment (simplified approach)
    exam_id: UUID = Field(..., description="ID of existing exam to use for competition")
    copy_exam: bool = Field(False, description="Whether to create a copy of the exam for this competition")

    # Competition specific settings
    competition_rules: Optional[str] = Field(None, description="Competition rules and guidelines")
    prize_details: Optional[Dict[str, Any]] = Field(None, description="Prize information")
    judging_type: JudgingTypeEnum = Field(JudgingTypeEnum.MENTOR_REVIEW, description="Type of judging")
    mentor_assignment_strategy: MentorAssignmentStrategyEnum = Field(MentorAssignmentStrategyEnum.AUTO, description="How mentors are assigned")
    min_mentors_required: int = Field(1, ge=1, description="Minimum mentors required")
    judging_deadline: Optional[datetime] = Field(None, description="Deadline for mentor evaluations")
    result_publication_date: Optional[datetime] = Field(None, description="When results will be published")
    evaluation_criteria: Optional[Dict[str, Any]] = Field(None, description="Evaluation criteria and weights")

    @field_validator('end_datetime')
    @classmethod
    def validate_end_after_start(cls, v, info):
        if info.data.get('start_datetime') and v <= info.data['start_datetime']:
            raise ValueError('End datetime must be after start datetime')
        return v

    @field_validator('judging_deadline')
    @classmethod
    def validate_judging_deadline(cls, v, info):
        if v and info.data.get('end_datetime') and v <= info.data['end_datetime']:
            raise ValueError('Judging deadline must be after competition end time')
        return v


class CompetitionEventOut(BaseModel):
    """Schema for competition event output"""
    id: UUID
    title: str
    description: Optional[str] = None
    start_datetime: datetime
    end_datetime: datetime
    max_attendees: Optional[int] = None
    competition_exam_id: Optional[UUID] = None
    competition_rules: Optional[str] = None
    prize_details: Optional[Dict[str, Any]] = None
    status: str
    total_participants: Optional[int] = 0
    total_submissions: Optional[int] = 0
    competition_status: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class CompetitionDetailsBase(BaseModel):
    """Base schema for competition details"""
    event_id: UUID = Field(..., description="Competition event ID")
    exam_id: UUID = Field(..., description="Competition exam ID")
    judging_type: JudgingTypeEnum = Field(JudgingTypeEnum.MENTOR_REVIEW, description="Type of judging")
    mentor_assignment_strategy: MentorAssignmentStrategyEnum = Field(
        MentorAssignmentStrategyEnum.AUTO, 
        description="Strategy for assigning mentors"
    )
    min_mentors_required: int = Field(1, ge=1, description="Minimum mentors required")
    max_mentors_per_submission: int = Field(3, ge=1, description="Maximum mentors per submission")
    judging_deadline: Optional[datetime] = Field(None, description="Deadline for judging")
    result_publication_date: Optional[datetime] = Field(None, description="When results will be published")
    prize_distribution: Optional[Dict[str, Any]] = Field(None, description="Prize distribution details")
    evaluation_criteria: Optional[Dict[str, Any]] = Field(None, description="Evaluation criteria")


class CompetitionDetailsCreate(CompetitionDetailsBase):
    """Schema for creating competition details"""
    pass


class CompetitionDetailsUpdate(BaseModel):
    """Schema for updating competition details"""
    judging_type: Optional[JudgingTypeEnum] = None
    mentor_assignment_strategy: Optional[MentorAssignmentStrategyEnum] = None
    min_mentors_required: Optional[int] = Field(None, ge=1)
    max_mentors_per_submission: Optional[int] = Field(None, ge=1)
    judging_deadline: Optional[datetime] = None
    result_publication_date: Optional[datetime] = None
    prize_distribution: Optional[Dict[str, Any]] = None
    evaluation_criteria: Optional[Dict[str, Any]] = None


class CompetitionDetailsOut(CompetitionDetailsBase):
    """Schema for competition details output"""
    id: UUID
    created_at: datetime
    updated_at: datetime
    
    # Related data
    event: Optional[Dict[str, Any]] = None
    exam: Optional[Dict[str, Any]] = None

    class Config:
        from_attributes = True


class CompetitionMentorAssignmentBase(BaseModel):
    """Base schema for competition mentor assignments"""
    competition_id: UUID = Field(..., description="Competition ID")
    mentor_id: UUID = Field(..., description="Mentor user ID")
    institute_id: UUID = Field(..., description="Institute ID")
    assignment_type: str = Field("auto", description="Type of assignment (auto, manual, volunteer)")
    collaboration_verified: bool = Field(False, description="Whether collaboration is verified")
    workload_capacity: int = Field(10, ge=1, description="Maximum submissions mentor can judge")
    current_workload: int = Field(0, ge=0, description="Current number of submissions assigned")
    specialization_match_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="Match score")
    
    # Assignment details
    estimated_hours: Optional[float] = Field(None, ge=0, description="Estimated hours for assignment")
    hourly_rate: Optional[float] = Field(None, ge=0, description="Hourly rate for compensation")
    total_compensation: Optional[float] = Field(None, ge=0, description="Total compensation")
    assignment_notes: Optional[str] = Field(None, description="Notes about the assignment")
    special_instructions: Optional[str] = Field(None, description="Special instructions for mentor")


class CompetitionMentorAssignmentCreate(CompetitionMentorAssignmentBase):
    """Schema for creating mentor assignments"""
    assigned_by: UUID = Field(..., description="User who made the assignment")


class CompetitionMentorAssignmentUpdate(BaseModel):
    """Schema for updating mentor assignments"""
    assignment_type: Optional[str] = None
    collaboration_verified: Optional[bool] = None
    workload_capacity: Optional[int] = Field(None, ge=1)
    specialization_match_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    estimated_hours: Optional[float] = Field(None, ge=0)
    hourly_rate: Optional[float] = Field(None, ge=0)
    total_compensation: Optional[float] = Field(None, ge=0)
    assignment_notes: Optional[str] = None
    special_instructions: Optional[str] = None


class CompetitionMentorAssignmentOut(CompetitionMentorAssignmentBase):
    """Schema for mentor assignment output"""
    id: UUID
    assigned_by: UUID
    status: str  # MentorAssignmentStatusEnum
    assigned_at: datetime
    accepted_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    questions_checked: int
    total_questions: int
    progress_percentage: float
    created_at: datetime
    updated_at: datetime
    
    # Related data
    mentor: Optional[Dict[str, Any]] = None
    institute: Optional[Dict[str, Any]] = None
    competition: Optional[Dict[str, Any]] = None
    assigner: Optional[Dict[str, Any]] = None

    class Config:
        from_attributes = True


class CompetitionSubmissionBase(BaseModel):
    """Base schema for competition submissions"""
    competition_id: UUID = Field(..., description="Competition ID")
    participant_id: UUID = Field(..., description="Participant (student) ID")
    exam_attempt_id: UUID = Field(..., description="Exam attempt ID")


class CompetitionSubmissionCreate(CompetitionSubmissionBase):
    """Schema for creating competition submissions"""
    pass


class CompetitionSubmissionOut(CompetitionSubmissionBase):
    """Schema for competition submission output"""
    id: UUID
    submission_status: str  # SubmissionStatusEnum
    submitted_at: datetime
    judging_started_at: Optional[datetime] = None
    judging_completed_at: Optional[datetime] = None
    final_score: Optional[float] = None
    final_rank: Optional[int] = None
    is_winner: bool
    prize_category: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    # Related data
    participant: Optional[Dict[str, Any]] = None
    competition: Optional[Dict[str, Any]] = None
    exam_attempt: Optional[Dict[str, Any]] = None

    class Config:
        from_attributes = True


class CompetitionJudgmentBase(BaseModel):
    """Base schema for competition judgments"""
    submission_id: UUID = Field(..., description="Submission ID")
    mentor_id: UUID = Field(..., description="Mentor ID")
    score: Optional[float] = Field(None, ge=0, description="Score given by mentor")
    feedback: Optional[str] = Field(None, description="Feedback from mentor")
    evaluation_criteria_scores: Optional[Dict[str, Any]] = Field(None, description="Breakdown by criteria")
    time_spent_minutes: Optional[int] = Field(None, ge=0, description="Time spent on judgment")


class CompetitionJudgmentCreate(CompetitionJudgmentBase):
    """Schema for creating competition judgments"""
    pass


class CompetitionJudgmentUpdate(BaseModel):
    """Schema for updating competition judgments"""
    score: Optional[float] = Field(None, ge=0)
    feedback: Optional[str] = None
    evaluation_criteria_scores: Optional[Dict[str, Any]] = None
    time_spent_minutes: Optional[int] = Field(None, ge=0)


class CompetitionJudgmentOut(CompetitionJudgmentBase):
    """Schema for competition judgment output"""
    id: UUID
    judgment_status: str  # JudgmentStatusEnum
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    
    # Related data
    submission: Optional[Dict[str, Any]] = None
    mentor: Optional[Dict[str, Any]] = None

    class Config:
        from_attributes = True


# Enhanced Competition Event Schema
class CompetitionEventCreate(BaseModel):
    """Schema for creating competition events with all details"""
    # Basic event info
    title: str = Field(..., max_length=300)
    description: Optional[str] = None
    short_description: Optional[str] = Field(None, max_length=500)
    start_datetime: datetime
    end_datetime: datetime
    category_id: UUID
    location_id: Optional[UUID] = None
    max_attendees: Optional[int] = Field(None, ge=1)
    
    # Competition specific
    exam_id: UUID = Field(..., description="Competition exam ID")
    competition_rules: Optional[str] = None
    prize_details: Optional[Dict[str, Any]] = None
    
    # Competition details
    judging_type: JudgingTypeEnum = Field(JudgingTypeEnum.MENTOR_REVIEW)
    mentor_assignment_strategy: MentorAssignmentStrategyEnum = Field(MentorAssignmentStrategyEnum.AUTO)
    min_mentors_required: int = Field(1, ge=1)
    max_mentors_per_submission: int = Field(3, ge=1)
    judging_deadline: Optional[datetime] = None
    result_publication_date: Optional[datetime] = None
    evaluation_criteria: Optional[Dict[str, Any]] = None


class CompetitionEventOut(BaseModel):
    """Schema for complete competition event output"""
    # Event details
    id: UUID
    title: str
    description: Optional[str]
    start_datetime: datetime
    end_datetime: datetime
    status: str
    max_attendees: Optional[int]
    
    # Competition specific
    exam_id: UUID
    competition_rules: Optional[str]
    prize_details: Optional[Dict[str, Any]]
    
    # Competition details
    competition_details: Optional[CompetitionDetailsOut] = None
    mentor_assignments: List[CompetitionMentorAssignmentOut] = []
    submissions_count: int = 0
    
    # Related data
    exam: Optional[Dict[str, Any]] = None
    category: Optional[Dict[str, Any]] = None
    location: Optional[Dict[str, Any]] = None
    institute: Optional[Dict[str, Any]] = None

    class Config:
        from_attributes = True


# Response schemas
class CompetitionListResponse(BaseModel):
    """Response schema for competition lists"""
    competitions: List[CompetitionEventOut]
    total: int
    page: int
    per_page: int


class MentorAssignmentResponse(BaseModel):
    """Response schema for mentor assignments"""
    assignments: List[CompetitionMentorAssignmentOut]
    total: int
    pending: int
    accepted: int
    completed: int


class CompetitionAnalytics(BaseModel):
    """Analytics schema for competitions"""
    total_competitions: int
    active_competitions: int
    completed_competitions: int
    total_participants: int
    total_mentors: int
    average_score: Optional[float] = None
    completion_rate: Optional[float] = None


# Additional missing schemas for CRUD operations
class CompetitionEventUpdate(BaseModel):
    """Schema for updating competition events"""
    title: Optional[str] = Field(None, max_length=300)
    description: Optional[str] = None
    short_description: Optional[str] = Field(None, max_length=500)
    start_datetime: Optional[datetime] = None
    end_datetime: Optional[datetime] = None
    max_attendees: Optional[int] = Field(None, ge=1)
    competition_rules: Optional[str] = None
    prize_details: Optional[Dict[str, Any]] = None
    judging_deadline: Optional[datetime] = None
    result_publication_date: Optional[datetime] = None
    evaluation_criteria: Optional[Dict[str, Any]] = None


class CompetitionResultOut(BaseModel):
    """Schema for competition result output"""
    id: UUID
    competition_id: UUID
    participant_id: UUID
    participant_name: str
    final_score: float
    max_possible_score: float
    percentage_score: float
    rank: Optional[int] = None
    time_taken: Optional[int] = None
    submission_time: Optional[datetime] = None
    is_published: bool = False
    published_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class CompetitionLeaderboardOut(BaseModel):
    """Schema for competition leaderboard output"""
    competition_id: UUID
    competition_title: str
    total_participants: int
    results: List[CompetitionResultOut]

    class Config:
        from_attributes = True


class CompetitionStatisticsOut(BaseModel):
    """Schema for competition statistics output"""
    competition_id: UUID
    total_participants: int
    total_submissions: int
    total_mentors: int
    completion_rate: float
    average_score: Optional[float] = None
    highest_score: Optional[float] = None
    score_distribution: Optional[Dict[str, int]] = None
    evaluation_status: Optional[Dict[str, int]] = None

    class Config:
        from_attributes = True
