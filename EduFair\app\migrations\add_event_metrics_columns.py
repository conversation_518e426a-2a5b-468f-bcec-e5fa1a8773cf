"""
Database migration script to add missing event metrics columns
Adds total_registrations, total_attendees, average_rating, total_reviews columns to events table
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from config.session import SQLALCHEMY_DATABASE_URL

def run_migration():
    """Run the migration to add missing event metrics columns"""
    
    engine = create_engine(SQLALCHEMY_DATABASE_URL)
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        print("Starting event metrics columns migration...")
        
        # Check if columns already exist
        result = session.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'events' 
            AND column_name IN ('total_registrations', 'total_attendees', 'average_rating', 'total_reviews')
        """))
        existing_columns = [row[0] for row in result.fetchall()]
        
        columns_to_add = []
        if 'total_registrations' not in existing_columns:
            columns_to_add.append("ADD COLUMN total_registrations INTEGER DEFAULT 0")
        if 'total_attendees' not in existing_columns:
            columns_to_add.append("ADD COLUMN total_attendees INTEGER DEFAULT 0")
        if 'average_rating' not in existing_columns:
            columns_to_add.append("ADD COLUMN average_rating NUMERIC(3,2)")
        if 'total_reviews' not in existing_columns:
            columns_to_add.append("ADD COLUMN total_reviews INTEGER DEFAULT 0")
        
        if columns_to_add:
            print(f"Adding columns: {', '.join([col.split(' ')[2] for col in columns_to_add])}")
            
            # Add columns one by one
            for column_def in columns_to_add:
                session.execute(text(f"ALTER TABLE events {column_def}"))
                print(f"Added column: {column_def.split(' ')[2]}")
            
            session.commit()
            print("Event metrics columns migration completed successfully!")
        else:
            print("All event metrics columns already exist. No migration needed.")
        
    except Exception as e:
        session.rollback()
        print(f"Migration failed: {str(e)}")
        raise
    finally:
        session.close()

if __name__ == "__main__":
    run_migration()
