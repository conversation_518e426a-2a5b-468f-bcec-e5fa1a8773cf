#!/usr/bin/env python3
"""
Run enhanced event system migration - work with existing models
"""
import sys
import os

# Add the EduFair app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'EduFair', 'app'))

try:
    from sqlalchemy import text
    from config.session import engine, Base
    
    def run_enhanced_migration():
        """Run the enhanced event system migration"""
        print("🚀 Running Enhanced Event System Migration")
        print("=" * 50)
        
        try:
            # Create all tables (this will create new ones and skip existing)
            print("1. Creating/updating database tables...")
            Base.metadata.create_all(bind=engine)
            print("   ✅ Tables created/updated successfully")
            
            # Add new columns to existing tables
            print("2. Adding new columns to existing tables...")
            
            with engine.connect() as connection:
                # Add columns to events table
                alter_statements = [
                    # Events table enhancements
                    "ALTER TABLE events ADD COLUMN IF NOT EXISTS event_type VARCHAR(20) DEFAULT 'workshop'",
                    "ALTER TABLE events ADD COLUMN IF NOT EXISTS requires_collaboration BOOLEAN DEFAULT FALSE",
                    "ALTER TABLE events ADD COLUMN IF NOT EXISTS collaboration_required_level VARCHAR(20) DEFAULT 'basic'",
                    
                    # Users table enhancements for mentor capabilities
                    "ALTER TABLE users ADD COLUMN IF NOT EXISTS can_act_as_mentor BOOLEAN DEFAULT FALSE",
                    "ALTER TABLE users ADD COLUMN IF NOT EXISTS mentor_specializations JSON",
                    "ALTER TABLE users ADD COLUMN IF NOT EXISTS judging_experience_years INTEGER DEFAULT 0",
                    
                    # Exams table enhancements for competitions
                    "ALTER TABLE exams ADD COLUMN IF NOT EXISTS is_competition_exam BOOLEAN DEFAULT FALSE",
                    "ALTER TABLE exams ADD COLUMN IF NOT EXISTS auto_grading_enabled BOOLEAN DEFAULT TRUE",
                    "ALTER TABLE exams ADD COLUMN IF NOT EXISTS manual_review_required BOOLEAN DEFAULT FALSE",
                    "ALTER TABLE exams ADD COLUMN IF NOT EXISTS competition_specific_settings JSON",
                    
                    # Competition mentor assignments enhancements
                    "ALTER TABLE competition_mentor_assignments ADD COLUMN IF NOT EXISTS institute_id UUID",
                    "ALTER TABLE competition_mentor_assignments ADD COLUMN IF NOT EXISTS assignment_type VARCHAR(20) DEFAULT 'auto'",
                    "ALTER TABLE competition_mentor_assignments ADD COLUMN IF NOT EXISTS collaboration_verified BOOLEAN DEFAULT FALSE",
                    "ALTER TABLE competition_mentor_assignments ADD COLUMN IF NOT EXISTS workload_capacity INTEGER DEFAULT 10",
                    "ALTER TABLE competition_mentor_assignments ADD COLUMN IF NOT EXISTS current_workload INTEGER DEFAULT 0",
                    "ALTER TABLE competition_mentor_assignments ADD COLUMN IF NOT EXISTS specialization_match_score DECIMAL(3,2)",
                ]
                
                for statement in alter_statements:
                    try:
                        connection.execute(text(statement))
                        connection.commit()
                        print(f"   ✅ {statement[:60]}...")
                    except Exception as e:
                        if "already exists" in str(e).lower() or "duplicate" in str(e).lower():
                            print(f"   ⚠️  Column already exists: {statement[:60]}...")
                        else:
                            print(f"   ❌ Error: {e}")
            
            # Insert default event types
            print("3. Creating event_types table and inserting defaults...")
            
            with engine.connect() as connection:
                # Create event_types table
                create_table_sql = """
                CREATE TABLE IF NOT EXISTS event_types (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    name VARCHAR(100) NOT NULL UNIQUE,
                    display_name VARCHAR(100) NOT NULL,
                    description TEXT,
                    default_settings JSON,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                )
                """
                
                try:
                    connection.execute(text(create_table_sql))
                    connection.commit()
                    print("   ✅ event_types table created")
                except Exception as e:
                    if "already exists" in str(e).lower():
                        print("   ⚠️  event_types table already exists")
                    else:
                        print(f"   ❌ Error creating event_types table: {e}")
                
                # Check if event types already exist
                try:
                    result = connection.execute(text("SELECT COUNT(*) FROM event_types")).scalar()
                    
                    if result == 0:
                        insert_statements = [
                            """INSERT INTO event_types (name, display_name, description, default_settings, is_active) VALUES 
                            ('workshop', 'Workshop', 'Hands-on skill-building sessions', '{"max_attendees": 50}', true)""",
                            
                            """INSERT INTO event_types (name, display_name, description, default_settings, is_active) VALUES 
                            ('conference', 'Conference', 'Large-scale knowledge sharing events', '{"max_attendees": 500}', true)""",
                            
                            """INSERT INTO event_types (name, display_name, description, default_settings, is_active) VALUES 
                            ('webinar', 'Webinar', 'Online educational sessions', '{"max_attendees": 1000, "is_virtual": true}', true)""",
                            
                            """INSERT INTO event_types (name, display_name, description, default_settings, is_active) VALUES 
                            ('competition', 'Competition', 'Exam-based contests with mentor judging', '{"requires_collaboration": true}', true)""",
                        ]
                        
                        for statement in insert_statements:
                            try:
                                connection.execute(text(statement))
                                connection.commit()
                                print(f"   ✅ Inserted event type")
                            except Exception as e:
                                print(f"   ❌ Error inserting event type: {e}")
                    else:
                        print(f"   ⚠️  Event types already exist ({result} records)")
                except Exception as e:
                    print(f"   ❌ Error checking event types: {e}")
            
            # Update existing data
            print("4. Updating existing data...")
            
            with engine.connect() as connection:
                update_statements = [
                    "UPDATE events SET event_type = 'competition' WHERE is_competition = true",
                    "UPDATE events SET event_type = 'workshop' WHERE (event_type IS NULL OR event_type = '') AND (is_competition = false OR is_competition IS NULL)",
                    "UPDATE exams SET is_competition_exam = true WHERE id IN (SELECT DISTINCT competition_exam_id FROM events WHERE competition_exam_id IS NOT NULL)",
                    "UPDATE users SET can_act_as_mentor = true WHERE user_type = 'teacher'",  # Allow teachers to act as mentors
                ]
                
                for statement in update_statements:
                    try:
                        result = connection.execute(text(statement))
                        connection.commit()
                        print(f"   ✅ Updated {result.rowcount} records: {statement[:50]}...")
                    except Exception as e:
                        print(f"   ❌ Error updating: {e}")
            
            # Add foreign key constraints
            print("5. Adding foreign key constraints...")
            
            with engine.connect() as connection:
                fk_statements = [
                    "ALTER TABLE competition_mentor_assignments ADD CONSTRAINT fk_mentor_assignment_institute FOREIGN KEY (institute_id) REFERENCES users(id) ON DELETE SET NULL",
                ]
                
                for statement in fk_statements:
                    try:
                        connection.execute(text(statement))
                        connection.commit()
                        print(f"   ✅ Added constraint")
                    except Exception as e:
                        if "already exists" in str(e).lower() or "duplicate" in str(e).lower():
                            print(f"   ⚠️  Constraint already exists")
                        else:
                            print(f"   ❌ Error adding constraint: {e}")
            
            print("✅ Enhanced migration completed successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Migration failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def verify_migration():
        """Verify the migration was successful"""
        print("\n🔍 Verifying Migration...")
        
        verification_queries = [
            ("Event Types Table", "SELECT COUNT(*) FROM event_types"),
            ("Events with Event Type", "SELECT COUNT(*) FROM events WHERE event_type IS NOT NULL"),
            ("Users with Mentor Capabilities", "SELECT COUNT(*) FROM users WHERE can_act_as_mentor = true"),
            ("Competition Exams", "SELECT COUNT(*) FROM exams WHERE is_competition_exam = true"),
            ("Competition Mentor Assignments", "SELECT COUNT(*) FROM competition_mentor_assignments"),
            ("Enhanced Mentor Assignments", "SELECT COUNT(*) FROM competition_mentor_assignments WHERE workload_capacity IS NOT NULL"),
        ]
        
        try:
            with engine.connect() as connection:
                for description, query in verification_queries:
                    try:
                        result = connection.execute(text(query)).scalar()
                        print(f"   ✅ {description}: {result} records")
                    except Exception as e:
                        print(f"   ❌ {description}: Error - {e}")
                        
        except Exception as e:
            print(f"❌ Verification failed: {e}")
            return False
        
        return True
    
    if __name__ == "__main__":
        success = run_enhanced_migration()
        if success:
            verify_migration()
            print("\n🎉 Enhanced Event System Migration Complete!")
            print("\nNext steps:")
            print("1. Run: python test_event_system_migration.py")
            print("2. Test event creation with new types")
            print("3. Test competition functionality")
            print("4. Test teacher-as-mentor functionality")
        else:
            print("\n❌ Migration failed. Please check the errors above.")
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running this from the correct directory")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
