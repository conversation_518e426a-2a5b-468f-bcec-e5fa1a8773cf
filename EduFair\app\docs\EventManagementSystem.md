# Event Management System - Updated Models and Schemas

## Overview

The Event Management system has been completely updated to match the exact requirements specified. All routes and CRUD operations have been removed, leaving only the SQLAlchemy models and Pydantic schemas for request/response handling.

## Core Features Implemented

### 1. List of Upcoming Events (with banners, slider)
- **Models**: `Event`, `EventCategory`, `EventAnalytics`
- **Schemas**: `EventOut`, `UpcomingEventsResponse`, `CalendarEventOut`
- **Features**:
  - Banner images for events (`banner_image_url`)
  - Gallery images for sliders (`gallery_images`)
  - Featured events flag (`is_featured`)
  - Event categories with banners (`EventCategory.banner_image_url`)
  - Event analytics for tracking views and engagement

### 2. Event Locations
- **Models**: `EventLocation`
- **Schemas**: `EventLocationOut`, `EventLocationCreate`, `EventLocationUpdate`
- **Features**:
  - Physical locations with full address details
  - Virtual locations with platform integration (Zoom, Teams, etc.)
  - GPS coordinates for mapping
  - Venue capacity and facilities
  - Location images and contact information

### 3. Guest/Speaker List with Profiles
- **Models**: `EventSpeaker`
- **Schemas**: `EventSpeakerOut`, `EventSpeakerCreate`, `EventSpeakerUpdate`
- **Features**:
  - Comprehensive speaker profiles with bio, achievements, certifications
  - Social media links (LinkedIn, Twitter, Instagram, Facebook)
  - Speaking topics and expertise areas
  - Speaker ratings and event history
  - Keynote speaker designation
  - Speaker fees and travel requirements

### 4. Purchase Ticket (with Payment Gateway Integration)
- **Models**: `EventTicket`, `EventRegistration`, `EventPayment`
- **Schemas**: `EventTicketOut`, `EventRegistrationOut`, `EventPaymentOut`
- **Features**:
  - Multiple ticket types with different pricing
  - Payment gateway integration (Stripe, PayPal, Razorpay, Bank Transfer, Cash)
  - Ticket benefits and inclusions
  - Registration with attendee information
  - QR codes for check-in
  - Payment tracking and refund management

### 5. Calendar View of Events
- **Models**: `EventCalendar`, `Event`
- **Schemas**: `CalendarEventOut`, `CalendarResponse`
- **Features**:
  - Calendar entries with date-based filtering
  - Color-coded events by category
  - Registration deadlines tracking
  - Monthly/yearly calendar views

### 6. Event Categories (Workshops, Conferences, Webinars, Competitions)
- **Models**: `EventCategory`
- **Schemas**: `EventCategoryOut`, `EventCategoryCreate`, `EventCategoryUpdate`
- **Features**:
  - Pre-defined categories: Workshop, Conference, Webinar, Competition
  - Category-specific banners and colors
  - Category icons for UI
  - Sort ordering for display

## Database Models

### Core Event Models

1. **EventCategory**
   - Category management with banners for sliders
   - Color coding and icons for UI
   - Sort ordering for display

2. **EventLocation**
   - Physical and virtual location support
   - GPS coordinates and mapping
   - Virtual platform integration (Zoom, Teams, etc.)
   - Capacity and facilities management

3. **EventSpeaker**
   - Comprehensive speaker profiles
   - Social media integration
   - Rating and review system
   - Speaking history tracking

4. **Event**
   - Main event entity with all details
   - Banner and gallery images for sliders
   - Featured events support
   - Competition integration
   - Analytics tracking

### Ticketing and Registration Models

5. **EventTicket**
   - Multiple ticket types per event
   - Pricing and availability management
   - Benefits and inclusions
   - Sale period management

6. **EventRegistration**
   - User registration with attendee details
   - QR code generation for check-in
   - Special requirements tracking
   - Payment status integration

7. **EventPayment**
   - Payment gateway integration
   - Transaction tracking
   - Refund management
   - Multiple payment methods

### Analytics and Feedback Models

8. **EventFeedback**
   - Comprehensive feedback system
   - Multiple rating categories
   - Public/private feedback options
   - Improvement suggestions

9. **EventAnalytics**
   - Event performance metrics
   - Revenue tracking
   - Attendance analytics
   - Engagement metrics

10. **EventCalendar**
    - Calendar view support
    - Date-based event organization
    - Color coding by category

## Pydantic Schemas

### Request Schemas (for API bodies)
- `EventCategoryCreate`, `EventCategoryUpdate`
- `EventLocationCreate`, `EventLocationUpdate`
- `EventSpeakerCreate`, `EventSpeakerUpdate`
- `EventCreate`, `EventUpdate`
- `EventTicketCreate`, `EventTicketUpdate`
- `EventRegistrationCreate`, `EventRegistrationUpdate`
- `EventPaymentCreate`, `EventPaymentUpdate`
- `EventFeedbackCreate`, `EventFeedbackUpdate`

### Response Schemas (for API responses)
- `EventCategoryOut`
- `EventLocationOut`
- `EventSpeakerOut`
- `EventOut`
- `EventTicketOut`
- `EventRegistrationOut`
- `EventPaymentOut`
- `EventFeedbackOut`
- `EventAnalyticsOut`

### List and Filter Schemas
- `EventListResponse` - Paginated event lists
- `UpcomingEventsResponse` - Featured and upcoming events
- `CalendarResponse` - Calendar view data
- `EventListFilter` - Event filtering options

## Key Features

### Payment Gateway Integration
- Support for multiple payment gateways (Stripe, PayPal, Razorpay)
- Secure transaction handling
- Refund management
- Payment status tracking

### Event Sliders and Banners
- Featured events for homepage sliders
- Category banners for navigation
- Gallery images for event details
- Responsive image handling

### Calendar Integration
- Monthly/yearly calendar views
- Event color coding by category
- Registration deadline tracking
- Multi-event day handling

### Speaker Management
- Detailed speaker profiles
- Social media integration
- Speaking history and ratings
- Keynote speaker designation

### Analytics and Reporting
- Event performance metrics
- Revenue and attendance tracking
- Feedback analysis
- Registration trends

## File Structure

```
app/
├── Models/
│   └── Events.py                    # All SQLAlchemy models
├── Schemas/
│   └── Events/
│       └── EventManagement.py      # All Pydantic schemas
└── docs/
    └── EventManagementSystem.md    # This documentation
```

## Competition Integration with Exam System

### Key Integration Points

**1. Exam Reuse**
- Competitions use the existing `Exam` model via `competition_exam_id`
- All exam functionality (questions, attempts, grading) is reused
- AI question generation uses the existing system from the Questions module

**2. Process Flow**
```
Competition Creation → Create Event → Link to Exam → Reuse Exam Process
Registration → Event Registration → Exam Assignment → Same as Student Exam
Attempt → StudentExamAttempt → Same attempt process as regular exams
Submission → StudentExamAnswer → Same grading process (AI + Manual)
Results → StudentExamAttempt results → Competition leaderboard
```

**3. Models Used**
- **Event Creation**: `Event` model with `is_competition=True`
- **Questions**: Existing `Question` model (no duplication)
- **Attempts**: Existing `StudentExamAttempt` model
- **Answers**: Existing `StudentExamAnswer` model
- **AI Grading**: Existing `StudentExamAIResult` model
- **Manual Review**: Existing `StudentExamTeacherResult` model
- **Competition-Specific**: `CompetitionMentorAssignment`, `CompetitionResult` for additional features

**4. AI Question Generation**
- Uses the existing AI question generation from the Questions module
- Same prompts, same models, same process
- Competition-specific prompts can be added without changing the core system

**5. Grading Process**
- Automatic grading uses the existing exam auto-grading system
- Manual review uses the existing teacher/mentor review system
- Competition results aggregate the exam results with competition-specific features

### Benefits of This Approach

1. **No Duplication**: Reuses all existing exam functionality
2. **Consistency**: Same user experience for exams and competitions
3. **Maintainability**: Single codebase for question management, grading, AI integration
4. **Scalability**: All exam optimizations automatically benefit competitions
5. **Feature Parity**: Competitions get all exam features (proctoring, analytics, etc.)

## Next Steps

To implement the full Event Management system:

1. **Create API Routes** - Implement FastAPI routes using the schemas
2. **Create CRUD Operations** - Implement database operations
3. **Frontend Integration** - Build React components using the schemas
4. **Payment Gateway Setup** - Configure payment providers
5. **Competition Integration** - Implement competition-specific endpoints that wrap exam functionality
6. **Testing** - Write comprehensive tests for all functionality

All models and schemas are ready for immediate use in building the complete Event Management system with full competition integration.
