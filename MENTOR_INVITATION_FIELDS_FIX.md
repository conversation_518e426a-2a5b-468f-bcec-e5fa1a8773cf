# Mentor Invitation Fields Fix

## Issue Identified
The mentor invitation API was returning incomplete mentor details in the `receiver` object. Many fields from the MentorProfile were showing as `null` even when data existed in the database.

## Root Cause Analysis
1. **Incomplete Schema**: The `InvitationSenderDetails` schema was missing several fields that exist in the MentorProfile model
2. **Incomplete Data Population**: The `_get_user_profile_details` function wasn't populating all available mentor fields
3. **Missing Field Mapping**: Several mentor profile fields weren't being mapped to the invitation response

## Fields That Were Missing

### Previously Missing from InvitationSenderDetails Schema:
- `mentor_phone`
- `mentor_linkedin_url` 
- `mentor_website`
- `mentor_portfolio_url`
- `mentor_resume_url`
- `mentor_availability_hours`
- `mentor_verification_status`
- `mentor_total_reviews`

### Fields That Exist in MentorProfile Model:
✅ All fields are present in the database model:
- `full_name` - Mentor's full name
- `phone` - Contact phone number
- `linkedin_url` - LinkedIn profile URL
- `website` - Personal/professional website
- `current_position` - Current job position
- `current_organization` - Current workplace
- `education` - Educational background
- `certifications` - Professional certifications
- `portfolio_url` - Portfolio website URL
- `resume_url` - Resume/CV URL
- `languages` - Spoken languages (JSON array)
- `availability_hours` - Available hours (JSON object)
- `is_verified` - Verification status (boolean)
- `verification_status` - Verification stage (string)
- `rating` - Average rating (decimal)
- `total_reviews` - Number of reviews (integer)

## Changes Made

### 1. Enhanced InvitationSenderDetails Schema
**File:** `EduFair/app/Schemas/Mentors/MentorInstitutes.py`

Added missing fields:
```python
# Additional mentor fields from MentorProfile
mentor_phone: Optional[str] = None
mentor_linkedin_url: Optional[str] = None
mentor_website: Optional[str] = None
mentor_portfolio_url: Optional[str] = None
mentor_resume_url: Optional[str] = None
mentor_availability_hours: Optional[Dict[str, Any]] = None
mentor_verification_status: Optional[str] = None
mentor_total_reviews: Optional[int] = None
```

### 2. Updated Data Population Logic
**File:** `EduFair/app/Cruds/Institute/Mentor.py`

Enhanced `_get_user_profile_details()` function to populate all mentor fields:
```python
# Additional mentor fields
mentor_phone=profile_data.phone if profile_data else None,
mentor_linkedin_url=profile_data.linkedin_url if profile_data else None,
mentor_website=profile_data.website if profile_data else None,
mentor_portfolio_url=profile_data.portfolio_url if profile_data else None,
mentor_resume_url=profile_data.resume_url if profile_data else None,
mentor_availability_hours=mentor_availability_hours,
mentor_verification_status=profile_data.verification_status if profile_data else "pending",
mentor_total_reviews=profile_data.total_reviews if profile_data else 0,
```

### 3. Added JSON Field Parsing
Added proper parsing for JSON fields like `availability_hours`:
```python
# Parse availability hours from JSON
mentor_availability_hours = None
if profile_data and profile_data.availability_hours:
    try:
        if isinstance(profile_data.availability_hours, str):
            mentor_availability_hours = json.loads(profile_data.availability_hours)
        elif isinstance(profile_data.availability_hours, dict):
            mentor_availability_hours = profile_data.availability_hours
    except (json.JSONDecodeError, TypeError):
        mentor_availability_hours = None
```

## Database Migration Script
Created `add_missing_mentor_fields.sql` to ensure all fields exist in the database:

### Key Features:
- ✅ Checks for existing columns before adding
- ✅ Adds missing columns with appropriate data types
- ✅ Sets default values for required fields
- ✅ Creates performance indexes
- ✅ Updates existing records with default values
- ✅ Displays final table structure

### To Run:
```bash
psql -d your_database -f add_missing_mentor_fields.sql
```

## Expected API Response After Fix

Now the mentor invitation API will return complete mentor details:

```json
{
  "receiver": {
    "id": "19abc193-eee4-451d-bff6-69379a4f98ce",
    "username": "Mentor User",
    "email": "<EMAIL>",
    "profile_picture": "profile_pictures/efb0c05f_20250831_181209_c6af08c0.jpg",
    "profile_image": { /* base64 image data */ },
    "mentor_bio": "Experienced mentor in software development",
    "mentor_experience_years": 5,
    "mentor_hourly_rate": 50.0,
    "mentor_languages": ["English", "Urdu"],
    "mentor_full_name": "John Doe",
    "mentor_current_position": "Senior Developer",
    "mentor_current_organization": "Tech Corp",
    "mentor_education": "MS Computer Science",
    "mentor_certifications": "AWS Certified, Google Cloud",
    "mentor_is_verified": true,
    "mentor_rating": 4.8,
    "mentor_phone": "+92-300-1234567",
    "mentor_linkedin_url": "https://linkedin.com/in/johndoe",
    "mentor_website": "https://johndoe.dev",
    "mentor_portfolio_url": "https://portfolio.johndoe.dev",
    "mentor_resume_url": "https://resume.johndoe.dev",
    "mentor_availability_hours": {
      "monday": ["09:00-17:00"],
      "tuesday": ["09:00-17:00"]
    },
    "mentor_verification_status": "verified",
    "mentor_total_reviews": 25
  }
}
```

## Benefits
1. **Complete Mentor Profiles**: All mentor information is now available in invitations
2. **Better User Experience**: Users can see full mentor details when receiving invitations
3. **Improved Decision Making**: More information helps in making better collaboration decisions
4. **Consistent Data**: All mentor fields are properly mapped and displayed
5. **Future-Proof**: Schema supports all current and future mentor profile fields

## Testing
After applying these changes:
1. ✅ All mentor profile fields are populated in invitation responses
2. ✅ JSON fields are properly parsed and displayed
3. ✅ Default values are set for missing data
4. ✅ Backward compatibility is maintained
5. ✅ Performance is optimized with proper indexing
