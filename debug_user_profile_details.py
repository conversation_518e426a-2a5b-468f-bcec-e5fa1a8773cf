#!/usr/bin/env python3
"""
Debug script for _get_user_profile_details function
"""
import sys
import os
import uuid

# Add the EduFair app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'EduFair', 'app'))

try:
    from sqlalchemy.orm import Session
    from config.session import get_db
    from Models.users import User, MentorProfile, UserTypeEnum, InstituteProfile
    from Cruds.Institute.Mentor import _get_user_profile_details
    
    def debug_user_profile_details():
        """Debug the _get_user_profile_details function"""
        print("🔍 Debugging _get_user_profile_details Function")
        print("=" * 60)
        
        # Get database session
        db = next(get_db())
        
        try:
            # Find a mentor user
            mentor_user = db.query(User).filter(
                User.user_type == UserTypeEnum.mentor
            ).first()
            
            if not mentor_user:
                print("❌ No mentor users found in database")
                return
            
            print(f"✅ Found mentor user: {mentor_user.username} (ID: {mentor_user.id})")
            
            # Test the function directly
            print(f"\n🧪 Testing _get_user_profile_details with mentor...")
            try:
                result = _get_user_profile_details(db, mentor_user.id, "mentor")
                if result:
                    print("✅ Function returned data successfully")
                    print(f"   - ID: {result.id}")
                    print(f"   - Username: {result.username}")
                    print(f"   - Email: {result.email}")
                    print(f"   - Mentor Bio: {result.mentor_bio}")
                    print(f"   - Mentor Full Name: {result.mentor_full_name}")
                    print(f"   - Mentor Position: {result.mentor_current_position}")
                    print(f"   - Mentor Organization: {result.mentor_current_organization}")
                    print(f"   - Mentor Phone: {result.mentor_phone}")
                    print(f"   - Mentor Languages: {result.mentor_languages}")
                    print(f"   - Mentor Availability: {result.mentor_availability_hours}")
                else:
                    print("❌ Function returned None")
            except Exception as e:
                print(f"❌ Error calling function: {e}")
                import traceback
                traceback.print_exc()
            
            # Find an institute user
            institute_user = db.query(User).filter(
                User.user_type == UserTypeEnum.institute
            ).first()
            
            if institute_user:
                print(f"\n✅ Found institute user: {institute_user.username} (ID: {institute_user.id})")
                
                # Test the function with institute
                print(f"\n🧪 Testing _get_user_profile_details with institute...")
                try:
                    result = _get_user_profile_details(db, institute_user.id, "institute")
                    if result:
                        print("✅ Function returned data successfully")
                        print(f"   - ID: {result.id}")
                        print(f"   - Username: {result.username}")
                        print(f"   - Email: {result.email}")
                        print(f"   - Institute Name: {result.institute_name}")
                        print(f"   - Institute Description: {result.institute_description}")
                        print(f"   - Institute Website: {result.institute_website}")
                        print(f"   - Institute City: {result.institute_city}")
                    else:
                        print("❌ Function returned None")
                except Exception as e:
                    print(f"❌ Error calling function: {e}")
                    import traceback
                    traceback.print_exc()
            else:
                print("❌ No institute users found")
            
            # Test with invalid user ID
            print(f"\n🧪 Testing with invalid user ID...")
            try:
                fake_id = uuid.uuid4()
                result = _get_user_profile_details(db, fake_id, "mentor")
                if result:
                    print("⚠️  Function returned data for invalid ID")
                else:
                    print("✅ Function correctly returned None for invalid ID")
            except Exception as e:
                print(f"❌ Error with invalid ID: {e}")
            
            # Check database directly
            print(f"\n📊 Direct Database Checks:")
            mentor_profile = db.query(MentorProfile).filter(MentorProfile.user_id == mentor_user.id).first()
            if mentor_profile:
                print("✅ Mentor profile exists in database")
                print(f"   - Bio: {mentor_profile.bio}")
                print(f"   - Full Name: {mentor_profile.full_name}")
                print(f"   - Experience: {mentor_profile.experience_years}")
                print(f"   - Phone: {mentor_profile.phone}")
                print(f"   - Languages (raw): {mentor_profile.languages}")
                print(f"   - Availability (raw): {mentor_profile.availability_hours}")
            else:
                print("❌ No mentor profile found in database")
                
        except Exception as e:
            print(f"❌ Error during debugging: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            db.close()
    
    if __name__ == "__main__":
        debug_user_profile_details()
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running this from the correct directory and all dependencies are installed")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
