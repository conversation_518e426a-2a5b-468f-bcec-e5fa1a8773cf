"""
Mentor Capabilities Schemas for Enhanced Event System
"""
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from uuid import UUID
from datetime import datetime

# Import user type enum
from Models.users import UserTypeEnum


class MentorCapabilitiesBase(BaseModel):
    """Base schema for mentor capabilities"""
    can_act_as_mentor: bool = Field(False, description="Whether user can act as mentor")
    mentor_specializations: Optional[List[str]] = Field(None, description="Areas of expertise")
    judging_experience_years: int = Field(0, ge=0, description="Years of judging experience")


class MentorCapabilitiesUpdate(BaseModel):
    """Schema for updating mentor capabilities"""
    can_act_as_mentor: Optional[bool] = None
    mentor_specializations: Optional[List[str]] = None
    judging_experience_years: Optional[int] = Field(None, ge=0)


class EnhancedUserBase(BaseModel):
    """Enhanced user schema with mentor capabilities"""
    id: UUID
    username: str
    email: str
    mobile: str
    user_type: str
    country: Optional[str] = None
    profile_picture: Optional[str] = None
    is_email_verified: bool
    is_mobile_verified: bool
    
    # Mentor capabilities
    can_act_as_mentor: bool
    mentor_specializations: Optional[List[str]] = None
    judging_experience_years: int
    
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class MentorUserOut(BaseModel):
    """Schema for mentor user output"""
    id: UUID
    username: str
    email: str
    user_type: str
    profile_picture: Optional[str] = None
    
    # Mentor specific fields
    can_act_as_mentor: bool
    mentor_specializations: Optional[List[str]] = None
    judging_experience_years: int
    
    # Performance metrics
    total_competitions_judged: int = 0
    average_rating: Optional[float] = None
    completion_rate: Optional[float] = None
    current_workload: int = 0
    max_workload_capacity: int = 10
    
    # Availability
    is_available: bool = True
    last_active: Optional[datetime] = None

    class Config:
        from_attributes = True


class TeacherMentorOut(BaseModel):
    """Schema for teacher acting as mentor"""
    id: UUID
    username: str
    email: str
    user_type: str  # Will be 'teacher'
    profile_picture: Optional[str] = None
    
    # Teacher specific
    subjects_taught: Optional[List[str]] = None
    institution: Optional[str] = None
    
    # Mentor capabilities
    can_act_as_mentor: bool
    mentor_specializations: Optional[List[str]] = None
    judging_experience_years: int
    
    # Performance as mentor
    competitions_judged_as_mentor: int = 0
    mentor_rating: Optional[float] = None

    class Config:
        from_attributes = True


class MentorAvailabilityBase(BaseModel):
    """Base schema for mentor availability"""
    mentor_id: UUID = Field(..., description="Mentor user ID")
    is_available: bool = Field(True, description="Whether mentor is available")
    max_concurrent_assignments: int = Field(5, ge=1, description="Max concurrent assignments")
    preferred_competition_types: Optional[List[str]] = Field(None, description="Preferred competition types")
    availability_schedule: Optional[Dict[str, Any]] = Field(None, description="Weekly availability schedule")
    time_zone: Optional[str] = Field(None, description="Mentor's time zone")


class MentorAvailabilityUpdate(BaseModel):
    """Schema for updating mentor availability"""
    is_available: Optional[bool] = None
    max_concurrent_assignments: Optional[int] = Field(None, ge=1)
    preferred_competition_types: Optional[List[str]] = None
    availability_schedule: Optional[Dict[str, Any]] = None
    time_zone: Optional[str] = None


class MentorPerformanceMetrics(BaseModel):
    """Schema for mentor performance metrics"""
    mentor_id: UUID
    total_assignments: int = 0
    completed_assignments: int = 0
    pending_assignments: int = 0
    average_completion_time_hours: Optional[float] = None
    average_rating: Optional[float] = None
    total_feedback_given: int = 0
    specialization_match_average: Optional[float] = None
    
    # Recent performance
    assignments_this_month: int = 0
    completion_rate_this_month: Optional[float] = None
    
    # Rankings
    overall_rank: Optional[int] = None
    specialization_rank: Optional[int] = None

    class Config:
        from_attributes = True


class MentorSearchFilters(BaseModel):
    """Schema for mentor search filters"""
    specializations: Optional[List[str]] = None
    min_experience_years: Optional[int] = Field(None, ge=0)
    max_experience_years: Optional[int] = Field(None, ge=0)
    user_types: Optional[List[str]] = Field(None, description="mentor, teacher")
    is_available: Optional[bool] = None
    min_rating: Optional[float] = Field(None, ge=0.0, le=5.0)
    max_current_workload: Optional[int] = Field(None, ge=0)
    collaboration_verified: Optional[bool] = None
    institute_id: Optional[UUID] = None


class MentorSearchResult(BaseModel):
    """Schema for mentor search results"""
    mentor: MentorUserOut
    match_score: float = Field(..., ge=0.0, le=1.0, description="How well mentor matches search criteria")
    availability_score: float = Field(..., ge=0.0, le=1.0, description="Availability score")
    performance_score: float = Field(..., ge=0.0, le=1.0, description="Performance score")
    overall_score: float = Field(..., ge=0.0, le=1.0, description="Overall matching score")


class MentorListResponse(BaseModel):
    """Response schema for mentor lists"""
    mentors: List[MentorUserOut]
    total: int
    available: int
    busy: int
    page: int = 1
    per_page: int = 20


class MentorSearchResponse(BaseModel):
    """Response schema for mentor search"""
    results: List[MentorSearchResult]
    total: int
    filters_applied: MentorSearchFilters
    search_metadata: Optional[Dict[str, Any]] = None


class MentorAssignmentRequest(BaseModel):
    """Schema for requesting mentor assignment"""
    competition_id: UUID = Field(..., description="Competition ID")
    mentor_ids: Optional[List[UUID]] = Field(None, description="Specific mentors to assign (optional)")
    assignment_strategy: str = Field("auto", description="Assignment strategy")
    max_mentors: int = Field(3, ge=1, le=10, description="Maximum mentors to assign")
    specialization_requirements: Optional[List[str]] = None
    priority_level: str = Field("normal", description="Priority level (low, normal, high)")
    assignment_notes: Optional[str] = None


class MentorAssignmentResult(BaseModel):
    """Schema for mentor assignment results"""
    competition_id: UUID
    assigned_mentors: List[UUID]
    failed_assignments: List[Dict[str, Any]] = []
    assignment_strategy_used: str
    total_assigned: int
    assignment_timestamp: datetime
    estimated_completion_time: Optional[datetime] = None


class MentorCollaborationStatus(BaseModel):
    """Schema for mentor-institute collaboration status"""
    mentor_id: UUID
    institute_id: UUID
    collaboration_level: str  # basic, verified, premium
    is_verified: bool
    verification_date: Optional[datetime] = None
    collaboration_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    total_collaborations: int = 0
    successful_collaborations: int = 0
    
    # Permissions
    can_judge_competitions: bool = True
    max_concurrent_judgments: int = 5
    specialization_areas: Optional[List[str]] = None

    class Config:
        from_attributes = True


class MentorDashboardSummary(BaseModel):
    """Schema for mentor dashboard summary"""
    mentor_id: UUID
    current_assignments: int
    pending_judgments: int
    completed_this_month: int
    average_rating: Optional[float] = None
    total_earnings: Optional[float] = None
    
    # Upcoming deadlines
    upcoming_deadlines: List[Dict[str, Any]] = []
    
    # Recent activity
    recent_competitions: List[Dict[str, Any]] = []
    
    # Performance trends
    performance_trend: Optional[Dict[str, Any]] = None

    class Config:
        from_attributes = True
