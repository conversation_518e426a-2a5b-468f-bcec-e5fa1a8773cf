import enum
from sqlalchemy import Column, String, Integer, DateTime, Foreign<PERSON><PERSON>, Table, Boolean, JSON, Text, Numeric, Enum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from .baseModel import BaseModel
from datetime import datetime, timezone
import uuid


class EventCategoryEnum(enum.Enum):
    WORKSHOP = "WORKSHOP"
    CONFERENCE = "CONFERENCE"
    WEBINAR = "WEBINAR"
    COMPETITION = "COMPETITION"


class EventStatusEnum(enum.Enum):
    DRAFT = "DRAFT"
    PUBLISHED = "PUBLISHED"
    CANCELLED = "CANCELLED"
    COMPLETED = "COMPLETED"


class TicketStatusEnum(enum.Enum):
    ACTIVE = "ACTIVE"
    SOLD_OUT = "SOLD_OUT"
    INACTIVE = "INACTIVE"


class RegistrationStatusEnum(enum.Enum):
    PENDING = "PENDING"
    CONFIRMED = "CONFIRMED"
    CANCELLED = "CANCELLED"
    ATTENDED = "ATTENDED"


class PaymentStatusEnum(enum.Enum):
    PENDING = "PENDING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    REFUNDED = "REFUNDED"


class PaymentGatewayEnum(enum.Enum):
    STRIPE = "STRIPE"
    PAYPAL = "PAYPAL"
    RAZORPAY = "RAZORPAY"
    PAYFAST = "PAYFAST"
    BANK_TRANSFER = "BANK_TRANSFER"
    CASH = "CASH"


class PayoutStatusEnum(enum.Enum):
    PENDING = "PENDING"
    PROCESSING = "PROCESSING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    CANCELLED = "CANCELLED"


class PayoutMethodEnum(enum.Enum):
    BANK_TRANSFER = "BANK_TRANSFER"
    JAZZCASH = "JAZZCASH"
    EASYPAISA = "EASYPAISA"
    PAYPAL = "PAYPAL"
    MANUAL_CASH = "MANUAL_CASH"


# Association table for Event <-> Speaker many-to-many
event_speaker_association = Table(
    'event_speaker_association', BaseModel.metadata,
    Column('event_id', UUID(as_uuid=True), ForeignKey('events.id'), primary_key=True),
    Column('speaker_id', UUID(as_uuid=True), ForeignKey('event_speakers.id'), primary_key=True)
)





# EventLocation model removed - location is now a simple string field in Event


class EventSpeaker(BaseModel):
    __tablename__ = 'event_speakers'

    name = Column(String(200), nullable=False)
    title = Column(String(200), nullable=True)
    bio = Column(Text, nullable=True)
    profile_image_url = Column(String(500), nullable=True)
    company = Column(String(200), nullable=True)
    position = Column(String(200), nullable=True)
    website = Column(String(500), nullable=True)
    linkedin_url = Column(String(500), nullable=True)
    twitter_url = Column(String(500), nullable=True)
    instagram_url = Column(String(500), nullable=True)
    facebook_url = Column(String(500), nullable=True)
    email = Column(String(255), nullable=True)
    phone = Column(String(20), nullable=True)
    expertise_areas = Column(JSON, nullable=True)  # List of expertise areas
    achievements = Column(JSON, nullable=True)  # List of achievements
    certifications = Column(JSON, nullable=True)  # List of certifications
    speaking_topics = Column(JSON, nullable=True)  # Topics they speak about
    languages = Column(JSON, nullable=True)  # Languages they speak
    is_featured = Column(Boolean, default=False)
    is_keynote_speaker = Column(Boolean, default=False)
    speaker_fee = Column(Numeric(10, 2), nullable=True)
    travel_required = Column(Boolean, default=False)
    rating = Column(Numeric(3, 2), nullable=True)  # Average rating
    total_events = Column(Integer, default=0)  # Total events spoken at

    # Relationships
    events = relationship(
        'Event',
        secondary=event_speaker_association,
        back_populates='speakers'
    )


class Event(BaseModel):
    __tablename__ = 'events'

    title = Column(String(300), nullable=False)
    description = Column(Text, nullable=True)
    short_description = Column(String(500), nullable=True)
    banner_image_url = Column(String(500), nullable=True)
    gallery_images = Column(JSON, nullable=True)  # List of image URLs for slider

    # Timing
    start_datetime = Column(DateTime(timezone=True), nullable=False)
    end_datetime = Column(DateTime(timezone=True), nullable=False)
    registration_start = Column(DateTime(timezone=True), nullable=True)
    registration_end = Column(DateTime(timezone=True), nullable=True)

    # Event details
    category = Column(Enum(EventCategoryEnum, name='eventcategoryenum'), nullable=False)
    location = Column(String(500), nullable=True)  # Event location (address, venue name, or 'Online')
    organizer_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    institute_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)  # Only institutes can create events

    # Status and settings
    status = Column(Enum(EventStatusEnum), default=EventStatusEnum.DRAFT)
    is_featured = Column(Boolean, default=False)  # For featured events slider
    is_public = Column(Boolean, default=True)
    requires_approval = Column(Boolean, default=False)
    max_attendees = Column(Integer, nullable=True)
    min_attendees = Column(Integer, nullable=True)

    # Additional info
    agenda = Column(JSON, nullable=True)  # Event agenda/schedule
    requirements = Column(Text, nullable=True)  # Prerequisites or requirements
    tags = Column(JSON, nullable=True)  # List of tags
    external_links = Column(JSON, nullable=True)  # Additional links

    # Competition specific fields (for competition events)
    is_competition = Column(Boolean, default=False)
    competition_exam_id = Column(UUID(as_uuid=True), ForeignKey('exams.id'), nullable=True)
    competition_rules = Column(Text, nullable=True)
    prize_details = Column(JSON, nullable=True)  # Prize information

    # Competition settings (these columns don't exist in database yet)
    # auto_grading_enabled = Column(Boolean, default=True)
    # manual_review_required = Column(Boolean, default=False)
    # proctoring_enabled = Column(Boolean, default=False)
    # randomize_questions = Column(Boolean, default=True)

    # Event metrics
    total_registrations = Column(Integer, default=0)
    total_attendees = Column(Integer, default=0)
    average_rating = Column(Numeric(3, 2), nullable=True)
    total_reviews = Column(Integer, default=0)

    # Relationships
    organizer = relationship('User', foreign_keys=[organizer_id], backref='organized_events')
    institute = relationship('User', foreign_keys=[institute_id], overlaps="competitions")
    speakers = relationship(
        'EventSpeaker',
        secondary=event_speaker_association,
        back_populates='events'
    )
    tickets = relationship('EventTicket', back_populates='event', cascade='all, delete-orphan')
    registrations = relationship('EventRegistration', back_populates='event', cascade='all, delete-orphan')
    payments = relationship('EventPayment', back_populates='event', cascade='all, delete-orphan')
    feedback = relationship('EventFeedback', back_populates='event', cascade='all, delete-orphan')
    competition_exam = relationship('Exam', backref='competition_events')

    # Competition-specific relationships (reuse existing exam models)
    # Note: Competitions use the existing Exam, StudentExamAttempt, StudentExamAnswer models
    # Additional competition-specific models for mentor assignments and results
    mentor_assignments = relationship('CompetitionMentorAssignment', foreign_keys='CompetitionMentorAssignment.competition_id', cascade='all, delete-orphan', overlaps="competition,mentor")


class EventTicket(BaseModel):
    __tablename__ = 'event_tickets'

    event_id = Column(UUID(as_uuid=True), ForeignKey('events.id'), nullable=False)
    name = Column(String(200), nullable=False)  # e.g., "Early Bird", "VIP", "Student"
    description = Column(Text, nullable=True)
    price = Column(Numeric(10, 2), nullable=False, default=0.00)
    currency = Column(String(3), nullable=False, default='PKR')

    # Availability
    total_quantity = Column(Integer, nullable=True)  # null = unlimited
    sold_quantity = Column(Integer, default=0)
    available_quantity = Column(Integer, nullable=True)  # Computed field
    status = Column(Enum(TicketStatusEnum), default=TicketStatusEnum.ACTIVE)

    # Timing
    sale_start = Column(DateTime(timezone=True), nullable=True)
    sale_end = Column(DateTime(timezone=True), nullable=True)

    # Restrictions
    min_quantity_per_order = Column(Integer, default=1)
    max_quantity_per_order = Column(Integer, nullable=True)
    requires_approval = Column(Boolean, default=False)

    # Additional settings
    is_transferable = Column(Boolean, default=True)
    is_refundable = Column(Boolean, default=False)
    refund_policy = Column(Text, nullable=True)
    terms_and_conditions = Column(Text, nullable=True)

    # Ticket benefits/features
    benefits = Column(JSON, nullable=True)  # List of ticket benefits
    includes = Column(JSON, nullable=True)  # What's included with ticket

    # Relationships
    event = relationship('Event', back_populates='tickets')
    registrations = relationship('EventRegistration', back_populates='ticket')


class EventRegistration(BaseModel):
    __tablename__ = 'event_registrations'

    event_id = Column(UUID(as_uuid=True), ForeignKey('events.id'), nullable=False)
    ticket_id = Column(UUID(as_uuid=True), ForeignKey('event_tickets.id'), nullable=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)

    # Registration details
    registration_number = Column(String(50), nullable=False, unique=True, default=lambda: f"REG-{uuid.uuid4().hex[:12].upper()}")
    status = Column(Enum(RegistrationStatusEnum), default=RegistrationStatusEnum.PENDING)
    quantity = Column(Integer, default=1)
    total_amount = Column(Numeric(10, 2), nullable=False, default=0.00)
    currency = Column(String(3), nullable=False, default='PKR')

    # Attendee information
    attendee_info = Column(JSON, nullable=True)  # Additional attendee details
    special_requirements = Column(Text, nullable=True)
    emergency_contact = Column(JSON, nullable=True)
    dietary_preferences = Column(JSON, nullable=True)  # For catering
    accessibility_needs = Column(Text, nullable=True)

    # Timestamps
    registered_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    confirmed_at = Column(DateTime(timezone=True), nullable=True)
    cancelled_at = Column(DateTime(timezone=True), nullable=True)
    attended_at = Column(DateTime(timezone=True), nullable=True)

    # Payment tracking
    payment_status = Column(Enum(PaymentStatusEnum), default=PaymentStatusEnum.PENDING)
    payment_reference = Column(String(255), nullable=True)
    payment_method = Column(String(50), nullable=True)

    # QR Code for check-in
    qr_code = Column(String(500), nullable=True)  # QR code for event check-in
    check_in_code = Column(String(20), nullable=True)  # Alternative check-in code

    # Relationships
    event = relationship('Event', back_populates='registrations')
    ticket = relationship('EventTicket', back_populates='registrations')
    user = relationship('User', backref='event_registrations')
    payments = relationship('EventPayment', back_populates='registration', cascade='all, delete-orphan')


class EventPayment(BaseModel):
    __tablename__ = 'event_payments'

    event_id = Column(UUID(as_uuid=True), ForeignKey('events.id'), nullable=False)
    registration_id = Column(UUID(as_uuid=True), ForeignKey('event_registrations.id'), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    amount = Column(Numeric(10, 2), nullable=False)
    currency = Column(String(3), nullable=False, default='PKR')

    # Payment details
    payment_method = Column(Enum(PaymentGatewayEnum), nullable=True)
    payment_gateway = Column(String(50), nullable=True)
    gateway_transaction_id = Column(String(255), nullable=True)
    gateway_payment_intent_id = Column(String(255), nullable=True)
    gateway_session_id = Column(String(255), nullable=True)

    # Status and timing
    status = Column(Enum(PaymentStatusEnum), default=PaymentStatusEnum.PENDING)
    initiated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    processed_at = Column(DateTime(timezone=True), nullable=True)
    failed_at = Column(DateTime(timezone=True), nullable=True)
    refunded_at = Column(DateTime(timezone=True), nullable=True)

    # Additional info
    failure_reason = Column(Text, nullable=True)
    refund_reason = Column(Text, nullable=True)
    refund_amount = Column(Numeric(10, 2), nullable=True)
    gateway_response = Column(JSON, nullable=True)  # Store gateway response

    # Payment metadata
    payment_description = Column(String(500), nullable=True)
    customer_email = Column(String(255), nullable=True)
    customer_phone = Column(String(20), nullable=True)

    # Relationships
    event = relationship('Event', back_populates='payments')
    registration = relationship('EventRegistration', back_populates='payments')
    user = relationship('User', backref='event_payments')


class EventAttendance(BaseModel):
    __tablename__ = 'event_attendance'

    event_id = Column(UUID(as_uuid=True), ForeignKey('events.id'), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    registration_id = Column(UUID(as_uuid=True), ForeignKey('event_registrations.id'), nullable=True)

    # Attendance details
    checked_in_at = Column(DateTime(timezone=True), nullable=True)
    checked_out_at = Column(DateTime(timezone=True), nullable=True)
    attendance_method = Column(String(50), nullable=True)  # qr_code, manual, etc.
    attendance_duration_minutes = Column(Integer, nullable=True)
    notes = Column(Text, nullable=True)

    # Check-in location (for verification)
    check_in_location = Column(JSON, nullable=True)  # GPS coordinates
    check_in_device = Column(String(200), nullable=True)  # Device info

    # Relationships
    event = relationship('Event', backref='attendance_records')
    user = relationship('User', backref='event_attendance')
    registration = relationship('EventRegistration', backref='attendance_record')


class EventFeedback(BaseModel):
    __tablename__ = 'event_feedback'

    event_id = Column(UUID(as_uuid=True), ForeignKey('events.id'), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)

    # Feedback details
    overall_rating = Column(Integer, nullable=True)  # 1-5 scale
    review = Column(Text, nullable=True)
    would_recommend = Column(Boolean, nullable=True)

    # Specific ratings
    content_rating = Column(Integer, nullable=True)
    speaker_rating = Column(Integer, nullable=True)
    venue_rating = Column(Integer, nullable=True)
    organization_rating = Column(Integer, nullable=True)
    networking_rating = Column(Integer, nullable=True)
    value_for_money_rating = Column(Integer, nullable=True)

    # Additional feedback
    best_aspects = Column(JSON, nullable=True)  # What they liked most
    improvement_suggestions = Column(Text, nullable=True)
    future_topics_interest = Column(JSON, nullable=True)  # Topics they'd like to see
    is_public = Column(Boolean, default=True)
    is_approved = Column(Boolean, default=False)

    # Feedback metadata
    submitted_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    approved_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    event = relationship('Event', back_populates='feedback')
    user = relationship('User', backref='event_feedback')


# Calendar and Event Analytics Models

class EventCalendar(BaseModel):
    __tablename__ = 'event_calendar'

    event_id = Column(UUID(as_uuid=True), ForeignKey('events.id'), nullable=False)
    calendar_date = Column(DateTime(timezone=True), nullable=False)
    is_start_date = Column(Boolean, default=True)
    is_end_date = Column(Boolean, default=False)
    is_registration_deadline = Column(Boolean, default=False)

    # Calendar display settings
    display_color = Column(String(7), nullable=True)  # Hex color
    display_priority = Column(Integer, default=0)  # For overlapping events

    # Relationships
    event = relationship('Event', backref='calendar_entries')


class EventAnalytics(BaseModel):
    __tablename__ = 'event_analytics'

    event_id = Column(UUID(as_uuid=True), ForeignKey('events.id'), nullable=False)

    # Registration metrics
    total_views = Column(Integer, default=0)
    total_registrations = Column(Integer, default=0)
    total_cancellations = Column(Integer, default=0)
    total_attendees = Column(Integer, default=0)
    total_no_shows = Column(Integer, default=0)

    # Revenue metrics
    total_revenue = Column(Numeric(12, 2), default=0.00)
    total_refunds = Column(Numeric(12, 2), default=0.00)
    net_revenue = Column(Numeric(12, 2), default=0.00)

    # Engagement metrics
    average_rating = Column(Numeric(3, 2), nullable=True)
    total_feedback_count = Column(Integer, default=0)
    recommendation_rate = Column(Numeric(5, 2), nullable=True)  # Percentage

    # Time-based metrics
    peak_registration_date = Column(DateTime(timezone=True), nullable=True)
    last_updated = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))

    # Relationships
    event = relationship('Event', backref='analytics')


class Payout(BaseModel):
    __tablename__ = 'payouts'

    # Core payout information
    institute_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    event_id = Column(UUID(as_uuid=True), ForeignKey('events.id'), nullable=False)

    # Financial details
    amount = Column(Numeric(12, 2), nullable=False)
    currency = Column(String(3), nullable=False, default='PKR')
    commission_rate = Column(Numeric(5, 2), nullable=True)  # Percentage taken by platform
    commission_amount = Column(Numeric(12, 2), nullable=True)  # Actual commission amount
    gross_revenue = Column(Numeric(12, 2), nullable=True)  # Total event revenue before commission

    # Payout details
    payout_date = Column(DateTime(timezone=True), nullable=True)
    method = Column(Enum(PayoutMethodEnum), nullable=False)
    status = Column(Enum(PayoutStatusEnum), default=PayoutStatusEnum.PENDING)

    # Bank/Payment details
    bank_account_number = Column(String(50), nullable=True)
    bank_name = Column(String(100), nullable=True)
    account_holder_name = Column(String(100), nullable=True)
    mobile_wallet_number = Column(String(20), nullable=True)  # For JazzCash/Easypaisa

    # Transaction tracking
    transaction_reference = Column(String(100), nullable=True)
    payment_proof_url = Column(String(500), nullable=True)  # Receipt/proof of transfer

    # Administrative details
    created_by = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)  # Admin who created payout
    processed_by = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=True)  # Admin who processed payout
    notes = Column(Text, nullable=True)
    failure_reason = Column(Text, nullable=True)

    # Timestamps
    processed_at = Column(DateTime(timezone=True), nullable=True)
    failed_at = Column(DateTime(timezone=True), nullable=True)
    cancelled_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    institute = relationship('User', foreign_keys=[institute_id], backref='received_payouts')
    event = relationship('Event', backref='payouts')
    creator = relationship('User', foreign_keys=[created_by])
    processor = relationship('User', foreign_keys=[processed_by])
