# Payment Services Documentation

## PayFast Payment Service (Primary)

EduFair now uses PayFast as the primary payment gateway for all payment processing. PayFast is a South African payment gateway that supports multiple payment methods including credit cards, bank transfers, and mobile payments.

## How It Works

The system uses PayFast for all payment processing:

- **All payments are processed through PayFast**
- **Real payment gateway integration**
- **Supports multiple payment methods**
- **Webhook notifications for payment status updates**

## Dummy Payment Service (Deprecated)

The Dummy Payment Service is deprecated and no longer used in production. It's kept for reference and testing purposes only.

## Features

### ✅ **Simulated Payment Processing**
- Creates dummy payment intents with realistic IDs
- Simulates payment confirmation with success/failure scenarios
- Generates fake transaction IDs and charge information
- Supports refund simulation

### ✅ **Configurable Failure Simulation**
- Enable/disable payment failure simulation
- Adjustable failure rate (0-100%)
- Realistic error messages and failure scenarios

### ✅ **Webhook Simulation**
- Simulates payment webhook events
- Compatible with existing webhook handling code
- Generates dummy event data

### ✅ **Database Integration**
- Full integration with EventPayment and EventRegistration models
- Proper status tracking (pending, completed, failed, refunded)
- Transaction history and audit trail

## API Endpoints

### Testing Endpoints (Admin Only)

#### Enable Failure Simulation
```http
POST /api/payments/test/enable-failures?failure_rate=0.1
Authorization: Bearer <admin_token>
```

#### Disable Failure Simulation
```http
POST /api/payments/test/disable-failures
Authorization: Bearer <admin_token>
```

#### Get Service Information
```http
GET /api/payments/test/service-info
Authorization: Bearer <token>
```

### Payment Configuration
```http
GET /api/payments/config
Authorization: Bearer <token>
```

Response includes `is_dummy_mode: true` when using dummy service.

## Usage Examples

### 1. **Testing Event Registration with Payment**

```python
# Register for an event with a paid ticket
registration_data = {
    "event_id": "event-uuid",
    "ticket_id": "ticket-uuid",
    "quantity": 1
}

# This will automatically use dummy payment if Stripe is not configured
response = requests.post("/api/events/{event_id}/register", json=registration_data)
```

### 2. **Testing Payment Confirmation**

```python
# The dummy service will generate a fake payment intent
payment_intent_id = "pi_dummy_1234567890abcdef"

# Confirm the payment (will always succeed unless failure simulation is enabled)
response = requests.post(f"/api/payments/confirm/{payment_intent_id}")
```

### 3. **Testing Refunds**

```python
# Request a refund for a completed payment
response = requests.post(f"/api/payments/payments/{payment_id}/refund", 
                        json={"refund_reason": "Event cancelled"})
```

## PayFast Configuration

### Environment Variables

Configure PayFast credentials in your environment:

```env
# PayFast Configuration
PAYFAST_MERCHANT_ID=your_merchant_id
PAYFAST_MERCHANT_KEY=your_merchant_key
PAYFAST_PASSPHRASE=your_passphrase  # Optional but recommended
PAYFAST_SANDBOX=true  # Set to false for production

# Frontend and Backend URLs
FRONTEND_URL=https://yourdomain.com
BACKEND_URL=https://api.yourdomain.com
```

### PayFast Setup

1. **Create PayFast Account**: Sign up at https://www.payfast.co.za/
2. **Get Credentials**: Obtain your Merchant ID and Merchant Key from PayFast dashboard
3. **Set Webhook URL**: Configure `https://api.yourdomain.com/api/payments/payfast/webhook` in PayFast
4. **Test in Sandbox**: Use sandbox mode for testing before going live

### Usage Example

```python
from services.PayFastService import PayFastService

# Initialize PayFast service
payfast_service = PayFastService()

# Create payment for event registration
payment_result = payfast_service.create_event_payment(
    db=db,
    registration_id=registration_id,
    amount=Decimal('100.00'),
    currency='ZAR',
    user_email='<EMAIL>',
    user_name='John Doe'
)
```

## Testing Scenarios

### 1. **Successful Payment Flow**
1. Register for an event with a paid ticket
2. Payment intent is created automatically
3. Confirm payment (will succeed)
4. Registration status updates to confirmed
5. Payment status updates to completed

### 2. **Failed Payment Flow**
1. Enable failure simulation with admin endpoint
2. Register for an event with a paid ticket
3. Payment intent creation or confirmation may fail
4. Registration remains in pending status
5. Payment status shows as failed

### 3. **Refund Flow**
1. Complete a successful payment
2. Request a refund
3. Payment status updates to refunded
4. Registration status may update based on business logic

## Dummy Data Generated

### Payment Intent IDs
- Format: `pi_dummy_[16-char-hex]`
- Example: `pi_dummy_1a2b3c4d5e6f7890`

### Transaction IDs
- Format: `ch_dummy_[16-char-hex]`
- Example: `ch_dummy_9f8e7d6c5b4a3210`

### Refund IDs
- Format: `re_dummy_[16-char-hex]`
- Example: `re_dummy_abcdef1234567890`

## Benefits

### 🚀 **Fast Development**
- No need to set up Stripe accounts for development
- Instant payment processing simulation
- No external API dependencies

### 🧪 **Comprehensive Testing**
- Test both success and failure scenarios
- Configurable failure rates for stress testing
- Full integration with existing payment flows

### 💰 **Cost-Effective**
- No transaction fees during development
- No need for test credit cards
- Unlimited testing without costs

### 🔒 **Safe Testing**
- No real money involved
- No PCI compliance requirements for development
- Safe to test with any data

## Production Use

The dummy payment service is designed for:

1. **Development and Testing**: Full payment workflow simulation
2. **Educational Platforms**: Where real money transactions aren't needed
3. **Internal Systems**: For organizations that handle payments separately
4. **Prototyping**: Quick setup without payment gateway complexity

The dummy payment service provides a complete payment experience without the complexity of external payment gateways, making it perfect for educational platforms and internal systems.
