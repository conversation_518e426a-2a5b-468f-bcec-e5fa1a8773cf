from pydantic import <PERSON><PERSON><PERSON><PERSON>, <PERSON>, validator
from uuid import UUID
from typing import List, Optional, Dict, Any
from datetime import datetime
from decimal import Decimal
from enum import Enum


# EventCategoryEnum removed - categories are now fixed enums
class EventCategoryEnum(str, Enum):
    WORKSHOP = "WORKSHOP"
    CONFERENCE = "CONFERENCE"
    WEBINAR = "WEBINAR"
    COMPETITION = "COMPETITION"


# EventStatusEnum removed - statuses are now fixed enums
class EventStatusEnum(str, Enum):
    DRAFT = "DRAFT"
    PUBLISHED = "PUBLISHED"
    CANCELLED = "CANCELLED"
    COMPLETED = "COMPLETED"


# TicketStatusEnum removed - statuses are now fixed enums
class TicketStatusEnum(str, Enum):
    ACTIVE = "ACTIVE"
    SOLD_OUT = "SOLD_OUT"
    INACTIVE = "INACTIVE"


# RegistrationStatusEnum removed - statuses are now fixed enums
class RegistrationStatusEnum(str, Enum):
    PENDING = "PENDING"
    CONFIRMED = "CONFIRMED"
    CANCELLED = "CANCELLED"
    ATTENDED = "ATTENDED"


# PaymentStatusEnum removed - statuses are now fixed enums
class PaymentStatusEnum(str, Enum):
    PENDING = "PENDING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    REFUNDED = "REFUNDED"


# EventLocationBase removed - location is now a simple string field
class EventSpeakerBase(BaseModel):
    name: str = Field(..., max_length=200, description="Speaker name")
    title: Optional[str] = Field(None, max_length=200, description="Professional title")
    bio: Optional[str] = Field(None, description="Speaker biography")
    profile_image_url: Optional[str] = Field(None, max_length=500)
    company: Optional[str] = Field(None, max_length=200)
    website: Optional[str] = Field(None, max_length=500)
    linkedin_url: Optional[str] = Field(None, max_length=500)
    twitter_url: Optional[str] = Field(None, max_length=500)
    email: Optional[str] = Field(None, max_length=255)
    phone: Optional[str] = Field(None, max_length=20)
    expertise_areas: Optional[List[str]] = Field(None, description="Areas of expertise")
    is_featured: bool = Field(False, description="Whether speaker is featured")


class EventTicketBase(BaseModel):
    name: str = Field(..., max_length=200, description="Ticket type name")
    description: Optional[str] = Field(None, description="Ticket description")
    price: Decimal = Field(..., ge=0, description="Ticket price")
    currency: str = Field("USD", max_length=3, description="Currency code")
    total_quantity: Optional[int] = Field(None, ge=0, description="Total available tickets")
    status: TicketStatusEnum = Field(TicketStatusEnum.ACTIVE)
    sale_start: Optional[datetime] = Field(None, description="Sale start time")
    sale_end: Optional[datetime] = Field(None, description="Sale end time")
    min_quantity_per_order: int = Field(1, ge=1)
    max_quantity_per_order: Optional[int] = Field(None, ge=1)
    requires_approval: bool = Field(False)
    is_transferable: bool = Field(True)
    is_refundable: bool = Field(False)
    refund_policy: Optional[str] = Field(None)
    terms_and_conditions: Optional[str] = Field(None)

    @validator('sale_start', 'sale_end', pre=True)
    def validate_datetime_fields(cls, v):
        """Convert empty string to None for datetime fields"""
        if v == "" or v is None:
            return None
        return v


class EventBase(BaseModel):
    title: str = Field(..., max_length=300, description="Event title")
    description: Optional[str] = Field(None, description="Event description")
    short_description: Optional[str] = Field(None, max_length=500)
    banner_image_url: Optional[str] = Field(None, max_length=500)
    gallery_images: Optional[List[str]] = Field(None, description="Gallery image URLs")
    start_datetime: datetime = Field(..., description="Event start time")
    end_datetime: datetime = Field(..., description="Event end time")
    registration_start: Optional[datetime] = Field(None)
    registration_end: Optional[datetime] = Field(None)
    category: EventCategoryEnum = Field(..., description="Event category")
    location: Optional[str] = Field(None, max_length=500, description="Event location (address, venue name, or 'Online')")
    status: EventStatusEnum = Field(EventStatusEnum.DRAFT)
    is_featured: bool = Field(False)
    is_public: bool = Field(True)
    requires_approval: bool = Field(False)
    max_attendees: Optional[int] = Field(None, ge=1)
    min_attendees: Optional[int] = Field(None, ge=1)
    agenda: Optional[List[Dict[str, Any]]] = Field(None, description="Event agenda")
    requirements: Optional[str] = Field(None, description="Event requirements")
    tags: Optional[List[str]] = Field(None, description="Event tags")
    external_links: Optional[Dict[str, str]] = Field(None, description="External links")
    is_competition: bool = Field(False)
    competition_exam_id: Optional[UUID] = Field(None)
    competition_rules: Optional[str] = Field(None)
    prize_details: Optional[Dict[str, Any]] = Field(None)

    @validator('competition_exam_id', pre=True)
    def validate_competition_exam_id(cls, v, values):
        """Convert empty string to None for competition_exam_id"""
        if v == "" or v is None:
            return None
        return v

    @validator('end_datetime')
    def validate_end_after_start(cls, v, values):
        if 'start_datetime' in values and v <= values['start_datetime']:
            raise ValueError('End datetime must be after start datetime')
        return v

    @validator('registration_end')
    def validate_registration_end(cls, v, values):
        if v and 'start_datetime' in values and v > values['start_datetime']:
            raise ValueError('Registration end must be before event start')
        return v


# Create Schemas - EventLocation schemas removed as location is now a simple string
class EventSpeakerCreate(EventSpeakerBase):
    pass


class EventTicketCreate(EventTicketBase):
    event_id: UUID = Field(..., description="Event ID")


class EventTicketCreateForEvent(BaseModel):
    """Ticket creation schema for use within event creation (without event_id)"""
    name: str = Field(..., max_length=200, description="Ticket name")
    description: Optional[str] = Field(None, description="Ticket description")
    price: Decimal = Field(..., ge=0, description="Ticket price")
    currency: str = Field("ZAR", max_length=3, description="Currency code")
    total_quantity: int = Field(..., ge=1, description="Total tickets available")
    status: TicketStatusEnum = Field(TicketStatusEnum.ACTIVE, description="Ticket status")
    sale_start: Optional[datetime] = Field(None, description="Ticket sale start time")
    sale_end: Optional[datetime] = Field(None, description="Ticket sale end time")
    min_quantity_per_order: int = Field(1, ge=1, description="Minimum tickets per order")
    max_quantity_per_order: int = Field(10, ge=1, description="Maximum tickets per order")
    requires_approval: bool = Field(False, description="Whether ticket requires approval")
    is_transferable: bool = Field(True, description="Whether ticket can be transferred")
    is_refundable: bool = Field(True, description="Whether ticket is refundable")
    refund_policy: Optional[str] = Field(None, description="Refund policy details")
    terms_and_conditions: Optional[str] = Field(None, description="Terms and conditions")
    benefits: Optional[List[str]] = Field(None, description="Ticket benefits")
    includes: Optional[List[str]] = Field(None, description="What's included with ticket")

    @validator('sale_start', 'sale_end', pre=True)
    def validate_datetime_fields(cls, v):
        """Convert empty string to None for datetime fields"""
        if v == "" or v is None:
            return None
        return v


class EventCreate(EventBase):
    organizer_id: Optional[UUID] = Field(None, description="Organizer ID (auto-filled from current user)")
    tickets: Optional[List[EventTicketCreateForEvent]] = Field(None, description="Event tickets to create")


# Update Schemas - EventLocation schemas removed as location is now a simple string
class EventSpeakerUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=200)
    title: Optional[str] = Field(None, max_length=200)
    bio: Optional[str] = None
    profile_image_url: Optional[str] = Field(None, max_length=500)
    company: Optional[str] = Field(None, max_length=200)
    website: Optional[str] = Field(None, max_length=500)
    linkedin_url: Optional[str] = Field(None, max_length=500)
    twitter_url: Optional[str] = Field(None, max_length=500)
    email: Optional[str] = Field(None, max_length=255)
    phone: Optional[str] = Field(None, max_length=20)
    expertise_areas: Optional[List[str]] = None
    is_featured: Optional[bool] = None


class EventTicketUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=200)
    description: Optional[str] = None
    price: Optional[Decimal] = Field(None, ge=0)
    currency: Optional[str] = Field(None, max_length=3)
    total_quantity: Optional[int] = Field(None, ge=0)
    status: Optional[TicketStatusEnum] = None
    sale_start: Optional[datetime] = None
    sale_end: Optional[datetime] = None
    min_quantity_per_order: Optional[int] = Field(None, ge=1)
    max_quantity_per_order: Optional[int] = Field(None, ge=1)
    requires_approval: Optional[bool] = None
    is_transferable: Optional[bool] = None
    is_refundable: Optional[bool] = None
    refund_policy: Optional[str] = None
    terms_and_conditions: Optional[str] = None


class EventUpdate(BaseModel):
    title: Optional[str] = Field(None, max_length=300)
    description: Optional[str] = None
    short_description: Optional[str] = Field(None, max_length=500)
    banner_image_url: Optional[str] = Field(None, max_length=500)
    gallery_images: Optional[List[str]] = None
    start_datetime: Optional[datetime] = None
    end_datetime: Optional[datetime] = None
    registration_start: Optional[datetime] = None
    registration_end: Optional[datetime] = None
    category: Optional[EventCategoryEnum] = None
    location: Optional[str] = None
    status: Optional[EventStatusEnum] = None
    is_featured: Optional[bool] = None
    is_public: Optional[bool] = None
    requires_approval: Optional[bool] = None
    max_attendees: Optional[int] = Field(None, ge=1)
    min_attendees: Optional[int] = Field(None, ge=1)
    agenda: Optional[List[Dict[str, Any]]] = None
    requirements: Optional[str] = None
    tags: Optional[List[str]] = None
    external_links: Optional[Dict[str, str]] = None
    is_competition: Optional[bool] = None
    competition_exam_id: Optional[UUID] = None
    competition_rules: Optional[str] = None
    prize_details: Optional[Dict[str, Any]] = None


# Output Schemas - EventLocation schemas removed as location is now a simple string
class EventSpeakerOut(EventSpeakerBase):
    id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class EventTicketOut(EventTicketBase):
    id: UUID
    event_id: UUID
    sold_quantity: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class EventMinimalOut(BaseModel):
    id: UUID
    title: str
    short_description: Optional[str]
    banner_image_url: Optional[str]
    start_datetime: datetime
    end_datetime: datetime
    status: EventStatusEnum
    is_featured: bool
    category: EventCategoryEnum
    location: Optional[str]

    class Config:
        from_attributes = True


class EventOut(EventBase):
    id: UUID
    organizer_id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class EventDetailedOut(EventOut):
    category: EventCategoryEnum
    location: Optional[str] = None
    speakers: List[EventSpeakerOut] = []
    tickets: List[EventTicketOut] = []
    total_registrations: int = 0
    available_tickets: int = 0
    is_registered: bool = False
    registration_status: Optional[RegistrationStatusEnum] = None

    class Config:
        from_attributes = True


# Registration Schemas
class EventRegistrationBase(BaseModel):
    quantity: int = Field(1, ge=1, description="Number of tickets")
    attendee_info: Optional[Dict[str, Any]] = Field(None, description="Additional attendee information")
    special_requirements: Optional[str] = Field(None, description="Special requirements")
    emergency_contact: Optional[Dict[str, str]] = Field(None, description="Emergency contact info")


class EventRegistrationCreate(EventRegistrationBase):
    event_id: UUID = Field(..., description="Event ID")
    ticket_id: Optional[UUID] = Field(None, description="Ticket type ID")


class EventRegistrationOut(EventRegistrationBase):
    id: UUID
    event_id: UUID
    ticket_id: Optional[UUID]
    user_id: UUID
    registration_number: str
    status: RegistrationStatusEnum
    total_amount: Decimal
    currency: str
    registered_at: datetime
    confirmed_at: Optional[datetime]
    cancelled_at: Optional[datetime]
    attended_at: Optional[datetime]
    payment_status: PaymentStatusEnum
    payment_reference: Optional[str]
    payment_method: Optional[str]

    class Config:
        from_attributes = True


class EventRegistrationDetailedOut(EventRegistrationOut):
    event: EventMinimalOut
    ticket: Optional[EventTicketOut] = None

    class Config:
        from_attributes = True


# Payment Schemas
class EventPaymentCreate(BaseModel):
    registration_id: UUID = Field(..., description="Registration ID")
    payment_method: str = Field(..., description="Payment method")
    amount: Optional[Decimal] = Field(None, description="Payment amount (auto-calculated if not provided)")


class EventPaymentOut(BaseModel):
    id: UUID
    registration_id: UUID
    amount: Decimal
    currency: str
    payment_method: Optional[str]
    payment_gateway: Optional[str]
    gateway_transaction_id: Optional[str]
    status: PaymentStatusEnum
    processed_at: Optional[datetime]
    failed_at: Optional[datetime]
    refunded_at: Optional[datetime]
    failure_reason: Optional[str]
    refund_reason: Optional[str]
    refund_amount: Optional[Decimal]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# List and Filter Schemas
class EventListFilter(BaseModel):
    category: Optional[EventCategoryEnum] = None
    location: Optional[str] = None
    status: Optional[EventStatusEnum] = None
    is_featured: Optional[bool] = None
    is_public: Optional[bool] = None
    is_competition: Optional[bool] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    search: Optional[str] = None
    tags: Optional[List[str]] = None


class EventListResponse(BaseModel):
    events: List[EventMinimalOut]
    total: int
    page: int
    size: int
    has_next: bool
    has_prev: bool


# Calendar Schemas
class CalendarEventOut(BaseModel):
    id: UUID
    title: str
    start_datetime: datetime
    end_datetime: datetime
    category: EventCategoryEnum
    location: Optional[str]
    is_registered: bool = False
    registration_status: Optional[RegistrationStatusEnum] = None

    class Config:
        from_attributes = True


class CalendarResponse(BaseModel):
    events: List[CalendarEventOut]
    month: int
    year: int
