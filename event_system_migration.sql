-- Event System Redesign Migration Script
-- This script adds new tables and fields for the enhanced event system

-- Add new enums
DO $$ BEGIN
    -- Event Type Enum
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'eventtypeenum') THEN
        CREATE TYPE eventtypeenum AS ENUM ('workshop', 'conference', 'webinar', 'competition');
    END IF;
    
    -- Judging Type Enum
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'judgingtypeenum') THEN
        CREATE TYPE judgingtypeenum AS ENUM ('automated', 'mentor_review', 'hybrid');
    END IF;
    
    -- Mentor Assignment Strategy Enum
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mentorassignmentstrategyenum') THEN
        CREATE TYPE mentorassignmentstrategyenum AS ENUM ('auto', 'manual', 'preference');
    END IF;
    
    -- Assignment Type Enum
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'assignmenttypeenum') THEN
        CREATE TYPE assignmenttypeenum AS ENUM ('auto', 'manual', 'volunteer');
    END IF;
    
    -- Assignment Status Enum
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'assignmentstatusenum') THEN
        CREATE TYPE assignmentstatusenum AS ENUM ('pending', 'accepted', 'declined', 'completed');
    END IF;
    
    -- Submission Status Enum
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'submissionstatusenum') THEN
        CREATE TYPE submissionstatusenum AS ENUM ('submitted', 'under_review', 'judged', 'appealed');
    END IF;
    
    -- Judgment Status Enum
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'judgmentstatusenum') THEN
        CREATE TYPE judgmentstatusenum AS ENUM ('pending', 'in_progress', 'completed', 'disputed');
    END IF;
    
    -- Collaboration Level Enum
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'collaborationlevelenum') THEN
        CREATE TYPE collaborationlevelenum AS ENUM ('basic', 'verified', 'premium');
    END IF;
END $$;

-- Create event_types table
CREATE TABLE IF NOT EXISTS event_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL UNIQUE,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    default_settings JSON,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add new fields to events table
DO $$ BEGIN
    -- Add event_type enum field
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'events' AND column_name = 'event_type') THEN
        ALTER TABLE events ADD COLUMN event_type eventtypeenum DEFAULT 'workshop' NOT NULL;
    END IF;
    
    -- Add event_type_id foreign key
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'events' AND column_name = 'event_type_id') THEN
        ALTER TABLE events ADD COLUMN event_type_id UUID REFERENCES event_types(id);
    END IF;
    
    -- Make institute_id required (only institutes can create events)
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'events' AND column_name = 'institute_id' AND is_nullable = 'YES') THEN
        UPDATE events SET institute_id = organizer_id WHERE institute_id IS NULL;
        ALTER TABLE events ALTER COLUMN institute_id SET NOT NULL;
    END IF;
    
    -- Add collaboration requirements
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'events' AND column_name = 'requires_collaboration') THEN
        ALTER TABLE events ADD COLUMN requires_collaboration BOOLEAN DEFAULT FALSE;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'events' AND column_name = 'collaboration_required_level') THEN
        ALTER TABLE events ADD COLUMN collaboration_required_level collaborationlevelenum DEFAULT 'basic';
    END IF;
END $$;

-- Add mentor-related fields to users table
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'users' AND column_name = 'can_act_as_mentor') THEN
        ALTER TABLE users ADD COLUMN can_act_as_mentor BOOLEAN DEFAULT FALSE;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'users' AND column_name = 'mentor_specializations') THEN
        ALTER TABLE users ADD COLUMN mentor_specializations JSON;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'users' AND column_name = 'judging_experience_years') THEN
        ALTER TABLE users ADD COLUMN judging_experience_years INTEGER DEFAULT 0;
    END IF;
END $$;

-- Add competition fields to exams table
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'exams' AND column_name = 'is_competition_exam') THEN
        ALTER TABLE exams ADD COLUMN is_competition_exam BOOLEAN DEFAULT FALSE;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'exams' AND column_name = 'auto_grading_enabled') THEN
        ALTER TABLE exams ADD COLUMN auto_grading_enabled BOOLEAN DEFAULT TRUE;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'exams' AND column_name = 'manual_review_required') THEN
        ALTER TABLE exams ADD COLUMN manual_review_required BOOLEAN DEFAULT FALSE;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'exams' AND column_name = 'competition_specific_settings') THEN
        ALTER TABLE exams ADD COLUMN competition_specific_settings JSON;
    END IF;
END $$;

-- Create event_type_configurations table
CREATE TABLE IF NOT EXISTS event_type_configurations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_type_id UUID NOT NULL REFERENCES event_types(id) ON DELETE CASCADE,
    institute_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    custom_settings JSON,
    is_enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(event_type_id, institute_id)
);

-- Create competition_details table
CREATE TABLE IF NOT EXISTS competition_details (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_id UUID UNIQUE NOT NULL REFERENCES events(id) ON DELETE CASCADE,
    exam_id UUID NOT NULL REFERENCES exams(id) ON DELETE CASCADE,
    judging_type judgingtypeenum DEFAULT 'mentor_review',
    mentor_assignment_strategy mentorassignmentstrategyenum DEFAULT 'auto',
    min_mentors_required INTEGER DEFAULT 1,
    max_mentors_per_submission INTEGER DEFAULT 3,
    judging_deadline TIMESTAMP WITH TIME ZONE,
    result_publication_date TIMESTAMP WITH TIME ZONE,
    prize_distribution JSON,
    evaluation_criteria JSON,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create competition_mentor_assignments table
CREATE TABLE IF NOT EXISTS competition_mentor_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    competition_id UUID NOT NULL REFERENCES competition_details(id) ON DELETE CASCADE,
    mentor_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    institute_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    assignment_type assignmenttypeenum DEFAULT 'auto',
    collaboration_verified BOOLEAN DEFAULT FALSE,
    assignment_status assignmentstatusenum DEFAULT 'pending',
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    accepted_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    workload_capacity INTEGER DEFAULT 10,
    current_workload INTEGER DEFAULT 0,
    specialization_match_score DECIMAL(3,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(competition_id, mentor_id)
);

-- Create competition_submissions table
CREATE TABLE IF NOT EXISTS competition_submissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    competition_id UUID NOT NULL REFERENCES competition_details(id) ON DELETE CASCADE,
    participant_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    exam_attempt_id UUID NOT NULL REFERENCES student_exam_attempts(id) ON DELETE CASCADE,
    submission_status submissionstatusenum DEFAULT 'submitted',
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    judging_started_at TIMESTAMP WITH TIME ZONE,
    judging_completed_at TIMESTAMP WITH TIME ZONE,
    final_score DECIMAL(5,2),
    final_rank INTEGER,
    is_winner BOOLEAN DEFAULT FALSE,
    prize_category VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(competition_id, participant_id)
);

-- Create competition_judgments table
CREATE TABLE IF NOT EXISTS competition_judgments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    submission_id UUID NOT NULL REFERENCES competition_submissions(id) ON DELETE CASCADE,
    mentor_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    judgment_status judgmentstatusenum DEFAULT 'pending',
    score DECIMAL(5,2),
    feedback TEXT,
    evaluation_criteria_scores JSON,
    time_spent_minutes INTEGER,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(submission_id, mentor_id)
);

-- Insert default event types
INSERT INTO event_types (name, display_name, description, default_settings) VALUES
('workshop', 'Workshop', 'Hands-on skill-building sessions with practical activities', '{"max_attendees": 50, "requires_registration": true, "default_duration_hours": 4}'),
('conference', 'Conference', 'Large-scale knowledge sharing events with multiple speakers', '{"max_attendees": 500, "requires_registration": true, "default_duration_hours": 8}'),
('webinar', 'Webinar', 'Online educational sessions and presentations', '{"max_attendees": 1000, "requires_registration": true, "default_duration_hours": 2, "is_virtual": true}'),
('competition', 'Competition', 'Exam-based contests with mentor judging and prizes', '{"requires_registration": true, "requires_collaboration": true, "default_duration_hours": 3, "auto_grading": true}')
ON CONFLICT (name) DO NOTHING;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_events_event_type ON events(event_type);
CREATE INDEX IF NOT EXISTS idx_events_institute_id ON events(institute_id);
CREATE INDEX IF NOT EXISTS idx_events_requires_collaboration ON events(requires_collaboration);
CREATE INDEX IF NOT EXISTS idx_competition_details_event_id ON competition_details(event_id);
CREATE INDEX IF NOT EXISTS idx_competition_mentor_assignments_competition_id ON competition_mentor_assignments(competition_id);
CREATE INDEX IF NOT EXISTS idx_competition_mentor_assignments_mentor_id ON competition_mentor_assignments(mentor_id);
CREATE INDEX IF NOT EXISTS idx_competition_submissions_competition_id ON competition_submissions(competition_id);
CREATE INDEX IF NOT EXISTS idx_competition_submissions_participant_id ON competition_submissions(participant_id);
CREATE INDEX IF NOT EXISTS idx_competition_judgments_submission_id ON competition_judgments(submission_id);
CREATE INDEX IF NOT EXISTS idx_competition_judgments_mentor_id ON competition_judgments(mentor_id);
CREATE INDEX IF NOT EXISTS idx_users_can_act_as_mentor ON users(can_act_as_mentor);

-- Update existing events to have proper event types
UPDATE events SET event_type = 'competition' WHERE is_competition = true;
UPDATE events SET event_type = 'workshop' WHERE is_competition = false AND event_type IS NULL;

-- Update exams that are used in competitions
UPDATE exams SET is_competition_exam = true 
WHERE id IN (SELECT DISTINCT competition_exam_id FROM events WHERE competition_exam_id IS NOT NULL);

RAISE NOTICE 'Event system migration completed successfully!';
