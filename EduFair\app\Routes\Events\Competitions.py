"""
Competition Routes for EduFair Platform

This module contains all API routes for the Competition system including:
- Competition creation and management
- Mentor assignment and workload management
- Competition participation and submission
- Result viewing and leaderboards
"""

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID

# Import dependencies
from config.session import get_db
from config.deps import oauth2_scheme, get_current_user
from config.permission import require_type

# Import CRUD operations
from Cruds.Events.Competitions import (
    create_competition_event, get_competition_by_id, update_competition_event,
    delete_competition_event, get_competitions_by_institute,
    assign_mentor_to_competition, auto_assign_mentors_to_competition,
    get_mentor_assignments_for_competition, update_mentor_assignment,
    get_competition_statistics, register_student_for_competition,
    get_student_competition_submission, get_competition_leaderboard,
    get_mentor_workload_summary, update_mentor_workload,
    get_mentor_assigned_submissions, submit_mentor_evaluation,
    get_submission_details_for_mentor, calculate_final_competition_results,
    publish_competition_results, get_competition_final_results,
    get_competition_analytics, enable_competition_security_features,
    get_competition_security_violations, monitor_competition_session,
    get_competition_monitoring_dashboard
)

# Import schemas
from Schemas.Events.Competitions import (
    CompetitionEventCreate, CompetitionEventOut, CompetitionEventUpdate,
    CompetitionMentorAssignmentCreate, CompetitionMentorAssignmentOut,
    CompetitionMentorAssignmentUpdate, CompetitionStatisticsOut
)

# Import models for type checking
from Models.users import User

router = APIRouter()


# ==================== COMPETITION MANAGEMENT ROUTES ====================

@router.post("/", response_model=CompetitionEventOut)
def create_competition_endpoint(
    competition: CompetitionEventCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Create a new competition event.
    
    Only institutes can create competitions.
    The competition will be linked to an existing exam.
    """
    current_user = get_current_user(token, db)
    return create_competition_event(db, competition, current_user.id)


@router.get("/{competition_id}", response_model=CompetitionEventOut)
def get_competition_endpoint(
    competition_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get competition details by ID.
    
    Returns comprehensive competition information including statistics.
    """
    current_user = get_current_user(token, db)
    return get_competition_by_id(db, competition_id)


@router.put("/{competition_id}", response_model=CompetitionEventOut)
def update_competition_endpoint(
    competition_id: UUID,
    competition_update: CompetitionEventUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Update competition details.
    
    Only the organizing institute can update the competition.
    """
    current_user = get_current_user(token, db)
    return update_competition_event(db, competition_id, competition_update, current_user.id)


@router.delete("/{competition_id}")
def delete_competition_endpoint(
    competition_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Delete a competition.
    
    Only the organizing institute can delete the competition.
    Cannot delete competitions with participants.
    """
    current_user = get_current_user(token, db)
    return delete_competition_event(db, competition_id, current_user.id)


@router.get("/institute/{institute_id}", response_model=List[CompetitionEventOut])
def get_institute_competitions_endpoint(
    institute_id: UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get all competitions for an institute.
    
    Returns paginated list of competitions with basic statistics.
    """
    current_user = get_current_user(token, db)
    
    # Check authorization - institutes can only see their own competitions
    if current_user.user_type.value == "institute" and current_user.id != institute_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Can only view your own competitions"
        )
    
    return get_competitions_by_institute(db, institute_id, skip, limit)


@router.get("/{competition_id}/statistics", response_model=CompetitionStatisticsOut)
def get_competition_statistics_endpoint(
    competition_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get detailed competition statistics.
    
    Returns participant count, submission rate, mentor assignments, etc.
    """
    current_user = get_current_user(token, db)
    stats = get_competition_statistics(db, competition_id)
    return CompetitionStatisticsOut(**stats)


# ==================== MENTOR ASSIGNMENT ROUTES ====================

@router.post("/{competition_id}/mentors", response_model=CompetitionMentorAssignmentOut)
def assign_mentor_endpoint(
    competition_id: UUID,
    assignment_data: CompetitionMentorAssignmentCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Manually assign a mentor to a competition.
    
    Only institutes can assign mentors to their competitions.
    """
    current_user = get_current_user(token, db)
    
    # Ensure the assignment is for the correct competition
    assignment_data.competition_id = competition_id
    assignment_data.assigned_by = current_user.id
    assignment_data.institute_id = current_user.id
    
    return assign_mentor_to_competition(db, assignment_data)


@router.post("/{competition_id}/mentors/auto-assign", response_model=List[CompetitionMentorAssignmentOut])
def auto_assign_mentors_endpoint(
    competition_id: UUID,
    min_mentors: int = Query(1, ge=1, le=10),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Automatically assign mentors to a competition.
    
    Uses algorithm to select best available mentors based on expertise and workload.
    """
    current_user = get_current_user(token, db)
    return auto_assign_mentors_to_competition(db, competition_id, min_mentors)


@router.get("/{competition_id}/mentors", response_model=List[CompetitionMentorAssignmentOut])
def get_competition_mentors_endpoint(
    competition_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get all mentor assignments for a competition.
    
    Returns list of mentors assigned to judge the competition.
    """
    current_user = get_current_user(token, db)
    return get_mentor_assignments_for_competition(db, competition_id)


@router.put("/mentors/{assignment_id}", response_model=CompetitionMentorAssignmentOut)
def update_mentor_assignment_endpoint(
    assignment_id: UUID,
    assignment_update: CompetitionMentorAssignmentUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Update mentor assignment details.

    Can update workload capacity, compensation, and assignment notes.
    """
    current_user = get_current_user(token, db)
    return update_mentor_assignment(db, assignment_id, assignment_update)


@router.get("/mentors/{mentor_id}/workload")
def get_mentor_workload_endpoint(
    mentor_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get mentor's current workload summary.

    Returns active assignments, capacity, and availability status.
    """
    current_user = get_current_user(token, db)

    # Mentors can view their own workload, institutes can view their assigned mentors
    if current_user.user_type.value == "mentor" and current_user.id != mentor_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Can only view your own workload"
        )

    return get_mentor_workload_summary(db, mentor_id)


@router.put("/mentors/assignments/{assignment_id}/workload", response_model=CompetitionMentorAssignmentOut)
def update_mentor_workload_endpoint(
    assignment_id: UUID,
    new_workload: int = Query(..., ge=0),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Update mentor's current workload for an assignment.

    Used to track progress as mentor evaluates submissions.
    """
    current_user = get_current_user(token, db)
    return update_mentor_workload(db, assignment_id, new_workload)


# ==================== COMPETITION PARTICIPATION ROUTES ====================

@router.post("/{competition_id}/register")
def register_for_competition_endpoint(
    competition_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))
):
    """
    Register a student for a competition.

    Creates an exam attempt record for the student.
    """
    current_user = get_current_user(token, db)
    return register_student_for_competition(db, competition_id, current_user.id)


@router.get("/{competition_id}/my-submission")
def get_my_competition_submission_endpoint(
    competition_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))
):
    """
    Get student's own competition submission and results.

    Returns submission details and evaluation status.
    """
    current_user = get_current_user(token, db)
    return get_student_competition_submission(db, competition_id, current_user.id)


@router.get("/{competition_id}/leaderboard")
def get_competition_leaderboard_endpoint(
    competition_id: UUID,
    limit: int = Query(50, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get competition leaderboard.

    Returns ranked list of participants with scores and completion times.
    """
    current_user = get_current_user(token, db)
    return get_competition_leaderboard(db, competition_id, limit)


# ==================== MENTOR EVALUATION ROUTES ====================

@router.get("/mentors/my-assignments")
def get_my_mentor_assignments_endpoint(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("mentor"))
):
    """
    Get all submissions assigned to the current mentor for evaluation.

    Returns list of student submissions that need mentor review.
    """
    current_user = get_current_user(token, db)
    return get_mentor_assigned_submissions(db, current_user.id)


@router.get("/{competition_id}/submissions-to-evaluate")
def get_submissions_to_evaluate_endpoint(
    competition_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("mentor"))
):
    """
    Get competition submissions assigned to the mentor for evaluation.

    Returns list of student submissions that need mentor review.
    """
    current_user = get_current_user(token, db)
    return get_mentor_assigned_submissions(db, current_user.id, competition_id)


@router.get("/submissions/{attempt_id}/details")
def get_submission_details_endpoint(
    attempt_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("mentor"))
):
    """
    Get detailed submission information for mentor evaluation.

    Returns student answers, questions, and AI evaluation if available.
    """
    current_user = get_current_user(token, db)
    return get_submission_details_for_mentor(db, current_user.id, attempt_id)


@router.post("/submissions/{attempt_id}/evaluate")
def submit_mentor_evaluation_endpoint(
    attempt_id: UUID,
    evaluation_data: dict,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("mentor"))
):
    """
    Submit mentor evaluation for a student's competition submission.

    Records mentor's scores and feedback for the submission.
    """
    current_user = get_current_user(token, db)
    return submit_mentor_evaluation(db, current_user.id, attempt_id, evaluation_data)


# ==================== COMPETITION RESULTS AND ANALYTICS ROUTES ====================

@router.post("/{competition_id}/calculate-results")
def calculate_competition_results_endpoint(
    competition_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Calculate final competition results.

    Processes all mentor evaluations and AI scores to determine final rankings.
    """
    current_user = get_current_user(token, db)
    return calculate_final_competition_results(db, competition_id)


@router.post("/{competition_id}/publish-results")
def publish_competition_results_endpoint(
    competition_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Publish competition results to make them visible to participants.

    Only the organizing institute can publish results.
    """
    current_user = get_current_user(token, db)
    return publish_competition_results(db, competition_id, current_user.id)


@router.get("/{competition_id}/results")
def get_competition_results_endpoint(
    competition_id: UUID,
    published_only: bool = Query(True),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get final competition results with rankings.

    Returns published results by default, institutes can see unpublished results.
    """
    current_user = get_current_user(token, db)

    # Institutes can see unpublished results for their own competitions
    if current_user.user_type.value == "institute":
        # Verify competition belongs to institute
        from Models.Events import Event
        competition = db.query(Event).filter(
            Event.id == competition_id,
            Event.institute_id == current_user.id
        ).first()

        if competition:
            published_only = False  # Institute can see unpublished results

    return get_competition_final_results(db, competition_id, published_only)


@router.get("/{competition_id}/analytics")
def get_competition_analytics_endpoint(
    competition_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Get comprehensive competition analytics.

    Returns detailed statistics, score distribution, and evaluation status.
    """
    current_user = get_current_user(token, db)
    return get_competition_analytics(db, competition_id)


# ==================== COMPETITION SECURITY AND MONITORING ROUTES ====================

@router.put("/{competition_id}/security")
def update_competition_security_endpoint(
    competition_id: UUID,
    security_settings: dict,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Update security settings for a competition.

    Configure proctoring, cheating detection, and other security features.
    """
    current_user = get_current_user(token, db)
    return enable_competition_security_features(db, competition_id, security_settings)


@router.get("/{competition_id}/security/violations")
def get_competition_violations_endpoint(
    competition_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Get security violations for a competition.

    Returns list of detected violations and suspicious activities.
    """
    current_user = get_current_user(token, db)
    return get_competition_security_violations(db, competition_id)


@router.post("/{competition_id}/monitor")
def monitor_competition_session_endpoint(
    competition_id: UUID,
    session_data: dict,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))
):
    """
    Monitor competition session activity.

    Log student activity and detect security violations in real-time.
    """
    current_user = get_current_user(token, db)
    return monitor_competition_session(db, competition_id, current_user.id, session_data)


@router.get("/{competition_id}/monitoring/dashboard")
def get_competition_monitoring_dashboard_endpoint(
    competition_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Get real-time monitoring dashboard for a competition.

    Returns active sessions, violations, and security status.
    """
    current_user = get_current_user(token, db)
    return get_competition_monitoring_dashboard(db, competition_id)


# ==================== EXAM LIBRARY FOR COMPETITIONS ====================

@router.get("/available-exams")
def get_available_exams_for_competitions_endpoint(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["institute", "teacher"]))
):
    """
    Get list of exams available for use in competitions.

    Returns exams created by the user (teachers) or available to the institute
    with usage information to help select appropriate exams.
    """
    current_user = get_current_user(token, db)

    # Import the function from exam CRUD
    from Cruds.Exams.Exam import get_available_exams_for_competition

    user_id = getattr(current_user, 'id', None)
    user_type = getattr(current_user, 'user_type', None)

    if user_type:
        user_type_str = user_type.value if hasattr(user_type, 'value') else str(user_type)
    else:
        user_type_str = "teacher"  # Default fallback

    return get_available_exams_for_competition(db, user_id, user_type_str)
