"""
PayFast Payment Gateway Routes for EduFair Platform

This module provides API endpoints for PayFast payment integration including:
- Event payment creation
- Subscription payment creation
- Webhook handling
- Payment status checking
- Refund processing
"""

from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Request, BackgroundTasks
from sqlalchemy.orm import Session
from uuid import UUID
import logging

# Import services and dependencies
from services.PayFastService import PayFastService
from config.session import get_db
from config.security import oauth2_scheme
from config.deps import get_current_user

# Import schemas
from Schemas.PayFast import (
    PayFastEventPaymentRequest,
    PayFastSubscriptionPaymentRequest,
    PayFastPaymentResponse,
    PayFastSubscriptionPaymentResponse,
    PayFastWebhookNotification,
    PayFastWebhookResponse,
    PayFastRefundRequest,
    PayFastRefundResponse,
    PayFastPaymentStatusResponse,
    PayFastConfigResponse,
    PayFastFormHelper
)

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()
payfast_service = PayFastService()


# ===== EVENT PAYMENT ENDPOINTS =====

@router.post("/events/payment", response_model=PayFastPaymentResponse)
def create_event_payment(
    payment_request: PayFastEventPaymentRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Create PayFast payment for event registration"""
    
    current_user = get_current_user(token, db)
    
    try:
        payment_result = payfast_service.create_event_payment(
            db=db,
            registration_id=payment_request.registration_id,
            amount=payment_request.amount,
            currency=payment_request.currency,
            user_email=payment_request.user_email or current_user.email,
            user_name=payment_request.user_name or f"{current_user.first_name} {current_user.last_name}"
        )
        
        return PayFastPaymentResponse(**payment_result)
        
    except Exception as e:
        logger.error(f"Error creating PayFast event payment: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/events/payment/{payment_id}/form", response_model=PayFastFormHelper)
def get_event_payment_form(
    payment_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get PayFast payment form data for frontend integration"""
    
    current_user = get_current_user(token, db)
    
    try:
        payment_status = payfast_service.get_payment_status(db, payment_id)
        
        if not payment_status.gateway_response:
            raise HTTPException(status_code=404, detail="Payment form data not found")
        
        return PayFastFormHelper(
            action_url=payfast_service.base_url,
            method="POST",
            form_data=payment_status.gateway_response
        )
        
    except Exception as e:
        logger.error(f"Error getting PayFast payment form: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


# ===== SUBSCRIPTION PAYMENT ENDPOINTS =====

@router.post("/subscriptions/payment", response_model=PayFastSubscriptionPaymentResponse)
def create_subscription_payment(
    payment_request: PayFastSubscriptionPaymentRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Create PayFast payment for subscription"""
    
    current_user = get_current_user(token, db)
    
    try:
        payment_result = payfast_service.create_subscription_payment(
            db=db,
            subscription_id=payment_request.subscription_id,
            amount=payment_request.amount,
            currency=payment_request.currency,
            user_email=payment_request.user_email or current_user.email,
            user_name=payment_request.user_name or f"{current_user.first_name} {current_user.last_name}"
        )
        
        return PayFastSubscriptionPaymentResponse(**payment_result)
        
    except Exception as e:
        logger.error(f"Error creating PayFast subscription payment: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


# ===== WEBHOOK ENDPOINTS =====

@router.post("/webhook", response_model=PayFastWebhookResponse)
async def payfast_webhook(
    request: Request,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """Handle PayFast webhook notifications"""
    
    try:
        # Get form data from PayFast
        form_data = await request.form()
        notification_data = dict(form_data)

        logger.info(f"Received PayFast webhook: {notification_data}")

        # Log important fields for debugging
        payment_status = notification_data.get('payment_status')
        custom_str1 = notification_data.get('custom_str1')
        custom_str2 = notification_data.get('custom_str2')
        custom_str3 = notification_data.get('custom_str3')

        logger.info(f"Payment status: {payment_status}, Custom fields: {custom_str1}, {custom_str2}, {custom_str3}")

        # Process webhook in background to respond quickly to PayFast
        background_tasks.add_task(
            process_webhook_background,
            db,
            notification_data
        )

        # Respond immediately to PayFast
        return PayFastWebhookResponse(
            status="received",
            message="Webhook received and will be processed"
        )
        
    except Exception as e:
        logger.error(f"Error processing PayFast webhook: {str(e)}")
        raise HTTPException(status_code=400, detail="Webhook processing failed")


def process_webhook_background(db: Session, notification_data: Dict[str, Any]):
    """Process PayFast webhook in background"""

    try:
        # Create a new database session for background processing
        from config.session import get_db

        # Get a fresh database session
        db_gen = get_db()
        fresh_db = next(db_gen)

        try:
            result = payfast_service.process_webhook_notification(fresh_db, notification_data)
            logger.info(f"PayFast webhook processed successfully: {result}")

        finally:
            # Close the database session
            fresh_db.close()

    except Exception as e:
        logger.error(f"Error in background webhook processing: {str(e)}")
        # Log the full traceback for debugging
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")


# ===== PAYMENT STATUS ENDPOINTS =====

@router.get("/payment/{payment_id}/status", response_model=PayFastPaymentStatusResponse)
def get_payment_status(
    payment_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get PayFast payment status"""
    
    current_user = get_current_user(token, db)
    
    try:
        payment_status = payfast_service.get_payment_status(db, payment_id)
        
        return PayFastPaymentStatusResponse(
            payment_id=payment_status.id,
            payfast_payment_id=payment_status.gateway_payment_intent_id,
            status=payment_status.status,
            amount=payment_status.amount,
            currency=payment_status.currency,
            created_at=payment_status.created_at,
            processed_at=payment_status.processed_at,
            failed_at=payment_status.failed_at,
            failure_reason=payment_status.failure_reason,
            gateway_response=payment_status.gateway_response
        )
        
    except Exception as e:
        logger.error(f"Error getting PayFast payment status: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


# ===== REFUND ENDPOINTS =====

@router.post("/payment/{payment_id}/refund", response_model=PayFastRefundResponse)
def refund_payment(
    payment_id: UUID,
    refund_request: PayFastRefundRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Process PayFast payment refund"""
    
    current_user = get_current_user(token, db)
    
    # Only allow admins or the payment owner to refund
    # This should be enhanced with proper permission checking
    
    try:
        refund_result = payfast_service.refund_payment(
            db=db,
            payment_id=payment_id,
            refund_amount=refund_request.refund_amount,
            reason=refund_request.reason
        )
        
        return PayFastRefundResponse(**refund_result)
        
    except Exception as e:
        logger.error(f"Error processing PayFast refund: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


# ===== CONFIGURATION ENDPOINTS =====

@router.get("/config", response_model=PayFastConfigResponse)
def get_payfast_config():
    """Get PayFast configuration information"""
    
    return PayFastConfigResponse(
        is_sandbox=payfast_service.is_sandbox,
        merchant_id=payfast_service.merchant_id,
        payment_url=payfast_service.base_url,
        supported_currencies=["ZAR", "USD", "EUR", "GBP"],
        webhook_url=f"{payfast_service.base_url}/api/payments/payfast/webhook"
    )


# ===== TESTING ENDPOINTS (Sandbox only) =====

@router.get("/test/check-status/{registration_id}")
def check_payment_status_detailed(
    registration_id: UUID,
    db: Session = Depends(get_db)
):
    """Check detailed payment status for debugging"""

    try:
        from Models.Events import EventRegistration, EventPayment

        # Find the registration
        registration = db.query(EventRegistration).filter(
            EventRegistration.id == registration_id
        ).first()

        if not registration:
            raise HTTPException(status_code=404, detail="Registration not found")

        # Find the payment
        payment = db.query(EventPayment).filter(
            EventPayment.registration_id == registration_id
        ).first()

        return {
            "registration_id": str(registration.id),
            "registration_status": registration.status.value,
            "registration_payment_status": registration.payment_status.value if registration.payment_status else None,
            "registration_payment_reference": registration.payment_reference,
            "registration_confirmed_at": registration.confirmed_at.isoformat() if registration.confirmed_at else None,
            "payment_found": payment is not None,
            "payment_id": str(payment.id) if payment else None,
            "payment_status": payment.status.value if payment else None,
            "payment_gateway_transaction_id": payment.gateway_transaction_id if payment else None,
            "payment_processed_at": payment.processed_at.isoformat() if payment and payment.processed_at else None,
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to check status: {str(e)}")

@router.post("/test/confirm-payment/{registration_id}")
def manually_confirm_payment(
    registration_id: UUID,
    db: Session = Depends(get_db)
):
    """Manually confirm a payment for testing purposes"""

    if not payfast_service.is_sandbox:
        raise HTTPException(status_code=403, detail="Manual confirmation only available in sandbox mode")

    try:
        from Models.Events import EventRegistration, EventPayment, RegistrationStatusEnum, PaymentStatusEnum
        from datetime import datetime, timezone

        # Find the registration
        registration = db.query(EventRegistration).filter(
            EventRegistration.id == registration_id
        ).first()

        if not registration:
            raise HTTPException(status_code=404, detail="Registration not found")

        # Find the payment
        payment = db.query(EventPayment).filter(
            EventPayment.registration_id == registration_id
        ).first()

        if not payment:
            raise HTTPException(status_code=404, detail="Payment not found")

        # Update payment status
        payment.status = PaymentStatusEnum.COMPLETED
        payment.processed_at = datetime.now(timezone.utc)
        payment.gateway_transaction_id = f"TEST_{registration_id}"

        # Update registration status
        registration.status = RegistrationStatusEnum.CONFIRMED
        registration.confirmed_at = datetime.now(timezone.utc)
        registration.payment_status = PaymentStatusEnum.COMPLETED
        registration.payment_reference = f"TEST_{registration_id}"
        registration.payment_method = "payfast_test"

        db.commit()

        return {
            "status": "success",
            "message": "Payment manually confirmed",
            "registration_id": registration_id,
            "registration_status": registration.status.value,
            "payment_status": payment.status.value
        }

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to confirm payment: {str(e)}")

@router.post("/test/payment")
def create_test_payment(
    db: Session = Depends(get_db)
):
    """Create a test payment for PayFast sandbox testing"""

    if not payfast_service.is_sandbox:
        raise HTTPException(status_code=403, detail="Test payments only available in sandbox mode")

    try:
        # Create a test payment form
        test_payment_form = payfast_service.create_payment_form_data(
            amount=100.00,
            item_name="Test Payment",
            item_description="Test payment for PayFast integration",
            custom_str1="test_payment",
            custom_str2="sandbox_test",
            custom_str3="",
            email_address="<EMAIL>",
            name_first="Test",
            name_last="User"
        )

        return {
            "message": "Test payment created",
            "payment_url": test_payment_form['payment_url'],
            "payment_data": test_payment_form['payment_data'],
            "note": "This is a sandbox test payment"
        }

    except Exception as e:
        logger.error(f"Error creating test payment: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/pay")
def create_simple_test_payment():
    """Simple test payment endpoint - visit this URL to get a PayFast payment link"""

    if not payfast_service:
        raise HTTPException(status_code=503, detail="PayFast service not available. Please set environment variables.")

    if not payfast_service.is_sandbox:
        raise HTTPException(status_code=403, detail="This endpoint is only available in sandbox mode")

    try:
        # Create a simple test payment
        test_payment_form = payfast_service.create_payment_form_data(
            amount=100.00,
            item_name="EduFair Test Payment",
            item_description="Test payment for EduFair PayFast integration",
            custom_str1="test_payment_123",
            custom_str2="simple_test",
            custom_str3="endpoint_test",
            email_address="<EMAIL>",
            name_first="Test",
            name_last="User"
        )

        # Create HTML form that auto-submits to PayFast
        payment_url = test_payment_form['payment_url']
        payment_data = test_payment_form['payment_data']

        # Generate HTML form
        form_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>PayFast Test Payment</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                .container {{ max-width: 600px; margin: 0 auto; }}
                .info {{ background: #e3f2fd; padding: 20px; border-radius: 8px; margin-bottom: 20px; }}
                .form {{ background: #f5f5f5; padding: 20px; border-radius: 8px; }}
                button {{ background: #4CAF50; color: white; padding: 15px 32px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }}
                button:hover {{ background: #45a049; }}
                .auto-submit {{ background: #ff9800; }}
                .details {{ margin-top: 20px; font-size: 12px; color: #666; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🧪 PayFast Test Payment</h1>

                <div class="info">
                    <h3>Test Payment Details:</h3>
                    <p><strong>Amount:</strong> R 100.00</p>
                    <p><strong>Item:</strong> EduFair Test Payment</p>
                    <p><strong>Mode:</strong> Sandbox (Test)</p>
                    <p><strong>Test Card:</strong> ****************</p>
                </div>

                <div class="form">
                    <form action="{payment_url}" method="post" id="payfast_form">
        """

        # Add all payment data as hidden fields
        for key, value in payment_data.items():
            form_html += f'        <input type="hidden" name="{key}" value="{value}">\n'

        form_html += f"""
                        <button type="submit">🚀 Pay with PayFast (Sandbox)</button>
                        <p style="margin-top: 10px; font-size: 14px;">
                            Click to redirect to PayFast sandbox payment page
                        </p>
                    </form>

                    <button onclick="autoSubmit()" class="auto-submit" style="margin-top: 10px;">
                        ⚡ Auto-Submit (3 seconds)
                    </button>
                </div>

                <div class="details">
                    <h4>Test Card Details (PayFast Sandbox):</h4>
                    <p><strong>Card Number:</strong> ****************</p>
                    <p><strong>Expiry:</strong> Any future date</p>
                    <p><strong>CVV:</strong> Any 3 digits</p>

                    <h4>Expected Flow:</h4>
                    <ol>
                        <li>Click "Pay with PayFast" → Redirects to PayFast sandbox</li>
                        <li>Enter test card details → Complete payment</li>
                        <li>PayFast redirects to success/cancel page</li>
                        <li>PayFast sends webhook to: <code>/api/payments/payfast/webhook</code></li>
                    </ol>

                    <h4>Webhook URL:</h4>
                    <p><code>{payment_data.get('notify_url', 'Not set')}</code></p>

                    <h4>Return URLs:</h4>
                    <p><strong>Success:</strong> <code>{payment_data.get('return_url', 'Not set')}</code></p>
                    <p><strong>Cancel:</strong> <code>{payment_data.get('cancel_url', 'Not set')}</code></p>
                </div>
            </div>

            <script>
                function autoSubmit() {{
                    let countdown = 3;
                    const button = event.target;
                    const originalText = button.innerHTML;

                    const timer = setInterval(() => {{
                        button.innerHTML = `⚡ Auto-submitting in ${{countdown}}...`;
                        countdown--;

                        if (countdown < 0) {{
                            clearInterval(timer);
                            document.getElementById('payfast_form').submit();
                        }}
                    }}, 1000);
                }}
            </script>
        </body>
        </html>
        """

        from fastapi.responses import HTMLResponse
        return HTMLResponse(content=form_html)

    except Exception as e:
        logger.error(f"Error creating simple test payment: {str(e)}")
        raise HTTPException(status_code=400, detail=f"Failed to create test payment: {str(e)}")
