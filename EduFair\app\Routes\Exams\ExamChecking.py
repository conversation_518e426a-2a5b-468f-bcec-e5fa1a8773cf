"""
Simple AI Exam Checking Routes - Just check exams with AI, no bullshit
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from uuid import UUID

from Cruds.Exams.ExamChecking import check_exam_with_ai, get_ai_results, get_basic_exam_results
from Schemas.Exams.ExamChecking import BasicExamResultsResponse
from config.session import get_db
from config.permission import require_type
from config.deps import oauth2_scheme, get_current_user

router = APIRouter()

# Simple AI checking endpoints

@router.post("/ai-check/{exam_id}")
async def ai_check_exam_endpoint(
    exam_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))
):
    current_user = get_current_user(token ,db)
    result = await check_exam_with_ai(db, exam_id, current_user.id)
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("message"))
    return result

@router.get("/ai-results/{exam_id}")
def get_ai_results_endpoint(
    exam_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))
):
    current_user = get_current_user(token ,db)
    result = get_ai_results(db, exam_id, current_user.id)
    if not result.get("success"):
        raise HTTPException(status_code=404, detail=result.get("message"))
    return result

@router.post("/teacher/ai-check/{exam_id}/{student_id}")
async def teacher_ai_check_exam_endpoint(
    exam_id: UUID,
    student_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["institute", "teacher"]))
):
    result = await check_exam_with_ai(db, exam_id, student_id)
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("message"))
    return result

@router.get("/teacher/ai-results/{exam_id}/{student_id}")
def teacher_get_ai_results_endpoint(
    exam_id: UUID,
    student_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["institute", "teacher"]))
):
    result = get_ai_results(db, exam_id, student_id)
    if not result.get("success"):
        raise HTTPException(status_code=404, detail=result.get("message"))
    return result

# Basic exam results endpoints (without AI processing)

@router.get("/results/{exam_id}", response_model=BasicExamResultsResponse)
def get_basic_results_endpoint(
    exam_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))
):
    current_user = get_current_user(token , db)
    result = get_basic_exam_results(db, exam_id, current_user.id)
    if not result.get("success"):
        raise HTTPException(status_code=404, detail=result.get("message"))
    return result

@router.get("/teacher/results/{exam_id}/{student_id}", response_model=BasicExamResultsResponse)
def teacher_get_basic_results_endpoint(
    exam_id: UUID,
    student_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """
    👨‍🏫 TEACHER ENDPOINT: Get basic exam results for any student

    Teachers can view basic exam results for any student.
    Shows teacher results if available, otherwise AI results.
    Properly handles disqualified students.

    Need:
    - exam_id: The exam UUID
    - student_id: The student UUID

    Example: GET /api/exams/checking/teacher/results/123e4567.../987fcdeb...
    """
    result = get_basic_exam_results(db, exam_id, student_id)
    if not result.get("success"):
        raise HTTPException(status_code=404, detail=result.get("message"))
    return result
