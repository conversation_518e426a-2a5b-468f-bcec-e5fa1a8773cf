"""
DEPRECATED: Simple PayFast Payment Integration for Event Tickets

This file is deprecated and not used in the application.
The main PayFast integration is in Routes/PayFast.py which uses the PayFastService.

Following MVP principles:
1. Database as source of truth for prices
2. Backend creates payment links
3. PayFast handles payment processing
4. Backend updates status via ITN notifications

NOTE: This file is kept for reference only. Use Routes/PayFast.py instead.
"""

from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy.orm import Session
from typing import Dict, Any
from decimal import Decimal
from datetime import datetime, timezone
import hashlib
import urllib.parse

# Import dependencies
from config.session import get_db
from config.deps import oauth2_scheme, get_current_user

# Import models
from Models.Events import EventTicket, EventRegistration, EventPayment, PaymentStatusEnum, RegistrationStatusEnum
from Models.users import User

# Import schemas
from pydantic import BaseModel, Field

router = APIRouter()

# DEPRECATED: PayFast Configuration
# This configuration is no longer used. PayFast is now configured via environment variables
# in the PayFastService class. See app/services/PayFastService.py for the current implementation.

# PAYFAST_MERCHANT_ID = "REMOVED"  # Use environment variables instead
# PAYFAST_MERCHANT_KEY = "REMOVED"  # Use environment variables instead
# PAYFAST_PASSPHRASE = ""  # Use environment variables instead
# PAYFAST_URL = "https://sandbox.payfast.co.za/eng/process"  # Configured in PayFastService

# Frontend URLs - These should be configured via environment variables
# FRONTEND_BASE_URL = "http://localhost:3000"  # Use FRONTEND_URL environment variable
# SUCCESS_URL = f"{FRONTEND_BASE_URL}/payment/success"
# CANCEL_URL = f"{FRONTEND_BASE_URL}/payment/cancel"
# NOTIFY_URL = "http://localhost:8000/api/payfast/notify"  # Use BACKEND_URL environment variable


# ==================== SCHEMAS ====================

class CreatePaymentRequest(BaseModel):
    ticket_id: str = Field(..., description="Ticket ID to purchase")
    quantity: int = Field(1, ge=1, le=10, description="Number of tickets (1-10)")
    buyer_info: dict = Field(..., description="Buyer information (name, email, phone)")


class PaymentResponse(BaseModel):
    payment_url: str
    ticket_id: str
    quantity: int
    total_amount: float
    currency: str = "ZAR"
    registration_id: str
    message: str


class TicketStatusResponse(BaseModel):
    ticket_id: str
    registration_id: str
    status: str  # PENDING | PAID | FAILED | CANCELLED
    total_amount: float
    currency: str = "ZAR"
    payment_date: str = None


# ==================== HELPER FUNCTIONS ====================

def generate_payfast_signature(data: Dict[str, Any], passphrase: str = "") -> str:
    """Generate PayFast signature for security validation"""
    # Create parameter string
    param_string = ""
    for key in sorted(data.keys()):
        if key != 'signature':
            param_string += f"{key}={urllib.parse.quote_plus(str(data[key]))}&"
    
    # Remove last ampersand
    param_string = param_string.rstrip('&')
    
    # Add passphrase if provided
    if passphrase:
        param_string += f"&passphrase={urllib.parse.quote_plus(passphrase)}"
    
    # Generate MD5 hash
    return hashlib.md5(param_string.encode()).hexdigest()


def validate_payfast_signature(data: Dict[str, Any], passphrase: str = "") -> bool:
    """Validate PayFast ITN signature"""
    received_signature = data.get('signature', '')
    if not received_signature:
        return False
    
    # Remove signature from data for validation
    validation_data = {k: v for k, v in data.items() if k != 'signature'}
    expected_signature = generate_payfast_signature(validation_data, passphrase)
    
    return received_signature == expected_signature


# ==================== PAYMENT ENDPOINTS ====================

@router.post("/create-payment", response_model=PaymentResponse)
def create_payment(
    request: CreatePaymentRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Create PayFast payment link for ticket purchase
    
    1. Validates ticket exists and is available
    2. Gets price from database (never trust frontend)
    3. Creates registration record
    4. Builds PayFast payment URL
    5. Returns payment URL to frontend
    """
    current_user = get_current_user(token, db)
    
    try:
        # 1. Get ticket from database (source of truth for price)
        ticket = db.query(EventTicket).filter(EventTicket.id == request.ticket_id).first()
        if not ticket:
            raise HTTPException(status_code=404, detail="Ticket not found")
        
        # 2. Check availability
        available = ticket.total_quantity - ticket.sold_quantity if ticket.total_quantity else 999
        if available < request.quantity:
            raise HTTPException(status_code=400, detail=f"Only {available} tickets available")
        
        # 3. Calculate total amount from database price (never trust frontend)
        total_amount = ticket.price * request.quantity
        
        # 4. Generate unique registration number
        import uuid
        registration_number = f"REG-{uuid.uuid4().hex[:12].upper()}"
        
        # 5. Create registration record
        registration = EventRegistration(
            event_id=ticket.event_id,
            user_id=current_user.id,
            ticket_id=ticket.id,
            quantity=request.quantity,
            total_amount=total_amount,
            currency="ZAR",
            status=RegistrationStatusEnum.PENDING,
            registration_number=registration_number,
            attendee_info=request.buyer_info
        )
        
        db.add(registration)
        db.commit()
        db.refresh(registration)
        
        # 6. Create payment record
        payment = EventPayment(
            event_id=ticket.event_id,
            registration_id=registration.id,
            user_id=current_user.id,
            amount=total_amount,
            currency="ZAR",
            payment_method=PaymentGatewayEnum.PAYFAST,
            payment_gateway="payfast",
            status=PaymentStatusEnum.PENDING
        )
        
        db.add(payment)
        db.commit()
        db.refresh(payment)
        
        # 7. Build PayFast payment data
        payfast_data = {
            'merchant_id': PAYFAST_MERCHANT_ID,
            'merchant_key': PAYFAST_MERCHANT_KEY,
            'return_url': SUCCESS_URL,
            'cancel_url': CANCEL_URL,
            'notify_url': NOTIFY_URL,
            'name_first': request.buyer_info.get('name', 'Customer').split()[0],
            'name_last': request.buyer_info.get('name', 'Customer').split()[-1] if len(request.buyer_info.get('name', '').split()) > 1 else '',
            'email_address': request.buyer_info.get('email', current_user.email),
            'cell_number': request.buyer_info.get('phone', ''),
            'amount': f"{total_amount:.2f}",
            'item_name': f"{ticket.name} x{request.quantity}",
            'item_description': f"Event ticket purchase - {registration_number}",
            'custom_str1': str(registration.id),  # For tracking in ITN
            'custom_str2': str(payment.id),       # Payment ID for reference
            'custom_str3': 'event_ticket',        # Payment type identifier
        }
        
        # 8. Generate signature
        signature = generate_payfast_signature(payfast_data, PAYFAST_PASSPHRASE)
        payfast_data['signature'] = signature
        
        # 9. Build payment URL
        query_string = urllib.parse.urlencode(payfast_data)
        payment_url = f"{PAYFAST_URL}?{query_string}"
        
        # 10. Update ticket sold quantity
        ticket.sold_quantity += request.quantity
        db.commit()
        
        return PaymentResponse(
            payment_url=payment_url,
            ticket_id=str(ticket.id),
            quantity=request.quantity,
            total_amount=float(total_amount),
            currency="ZAR",
            registration_id=str(registration.id),
            message="Redirect user to payment_url to complete payment"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create payment: {str(e)}"
        )


@router.post("/notify")
async def payfast_notify(request: Request, db: Session = Depends(get_db)):
    """
    PayFast ITN (Instant Transaction Notification) endpoint
    
    PayFast calls this endpoint when payment is processed:
    1. Validates the ITN signature
    2. Updates registration and payment status
    3. Confirms payment completion
    """
    try:
        # 1. Get form data from PayFast
        form_data = await request.form()
        itn_data = dict(form_data)
        
        print(f"PayFast ITN received: {itn_data}")
        
        # 2. Validate signature (security check)
        if not validate_payfast_signature(itn_data, PAYFAST_PASSPHRASE):
            print("Invalid PayFast signature")
            raise HTTPException(status_code=400, detail="Invalid signature")
        
        # 3. Extract data
        payment_status = itn_data.get('payment_status')
        registration_id = itn_data.get('custom_str1')
        payment_id = itn_data.get('custom_str2')
        pf_payment_id = itn_data.get('pf_payment_id')
        
        if not registration_id:
            raise HTTPException(status_code=400, detail="Missing registration ID")
        
        # 4. Get registration record
        registration = db.query(EventRegistration).filter(
            EventRegistration.id == registration_id
        ).first()
        
        if not registration:
            raise HTTPException(status_code=404, detail="Registration not found")
        
        # 5. Get payment record
        payment = db.query(EventPayment).filter(
            EventPayment.registration_id == registration_id
        ).first()
        
        if not payment:
            raise HTTPException(status_code=404, detail="Payment not found")
        
        # 6. Update status based on PayFast response
        if payment_status == "COMPLETE":
            # Payment successful
            payment.status = PaymentStatusEnum.COMPLETED
            payment.processed_at = datetime.now(timezone.utc)
            payment.gateway_transaction_id = pf_payment_id
            payment.gateway_response = itn_data
            
            registration.status = RegistrationStatusEnum.CONFIRMED
            registration.confirmed_at = datetime.now(timezone.utc)
            registration.payment_status = PaymentStatusEnum.COMPLETED
            registration.payment_reference = pf_payment_id
            registration.payment_method = "payfast"
            
            print(f"Payment completed for registration {registration_id}")
            
        else:
            # Payment failed or cancelled
            payment.status = PaymentStatusEnum.FAILED
            payment.failed_at = datetime.now(timezone.utc)
            payment.failure_reason = f"PayFast status: {payment_status}"
            payment.gateway_response = itn_data
            
            registration.status = RegistrationStatusEnum.CANCELLED
            registration.payment_status = PaymentStatusEnum.FAILED
            
            print(f"Payment failed for registration {registration_id}: {payment_status}")
        
        # 7. Save changes
        db.commit()
        
        # 8. Return success to PayFast
        return {"status": "success"}
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"PayFast ITN error: {e}")
        raise HTTPException(status_code=500, detail="ITN processing failed")


@router.get("/tickets/{registration_id}/status", response_model=TicketStatusResponse)
def get_ticket_status(
    registration_id: str,
    db: Session = Depends(get_db)
):
    """
    Get ticket/registration status for frontend polling
    
    Frontend can call this endpoint to check payment status
    without needing WebSockets or real-time updates
    """
    try:
        registration = db.query(EventRegistration).filter(
            EventRegistration.id == registration_id
        ).first()
        
        if not registration:
            raise HTTPException(status_code=404, detail="Registration not found")
        
        # Map status to simple string
        status_map = {
            RegistrationStatusEnum.PENDING: "PENDING",
            RegistrationStatusEnum.CONFIRMED: "PAID",
            RegistrationStatusEnum.CANCELLED: "FAILED"
        }
        
        status = status_map.get(registration.status, "PENDING")
        payment_date = None
        
        if registration.confirmed_at:
            payment_date = registration.confirmed_at.isoformat()
        
        return TicketStatusResponse(
            ticket_id=str(registration.ticket_id),
            registration_id=str(registration.id),
            status=status,
            total_amount=float(registration.total_amount),
            currency=registration.currency,
            payment_date=payment_date
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get ticket status: {str(e)}"
        )
