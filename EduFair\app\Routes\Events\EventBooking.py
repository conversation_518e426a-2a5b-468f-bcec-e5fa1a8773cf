"""
Event Booking and Payment Routes for EduFair Platform

This module provides comprehensive APIs for users to:
- Register for events
- Purchase tickets
- Process payments through PayFast
- Handle payment confirmations
"""

from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID
from decimal import Decimal
from datetime import datetime, timezone

# Import dependencies
from config.session import get_db
from config.deps import oauth2_scheme, get_current_user
from config.permission import require_type

# Import services
from services.PayFastService import PayFastService

# Import CRUD operations
from Cruds.Events.Events import get_event_by_id
from Cruds.Events.EventTickets import get_event_ticket_by_id, check_ticket_availability
from Cruds.Events.EventRegistrations import create_event_registration, get_registration_by_id

# Import models
from Models.Events import Event, EventTicket, EventRegistration, EventPayment, PaymentStatusEnum, RegistrationStatusEnum
from Models.users import User

# Import schemas
from Schemas.Events.EventManagement import EventRegistrationCreate
from Schemas.PayFast import (
    PayFastEventPaymentRequest, PayFastPaymentResponse, 
    PayFastPaymentStatusResponse
)

router = APIRouter()

# Initialize PayFast service with error handling
try:
    payfast_service = PayFastService()
except ValueError as e:
    print(f"Warning: PayFast service not initialized - {e}")
    payfast_service = None


# ==================== EVENT BOOKING SCHEMAS ====================

from pydantic import BaseModel, Field
from typing import Dict, Any

class EventBookingRequest(BaseModel):
    """Complete event booking request with registration and payment"""
    
    event_id: UUID = Field(..., description="Event ID")
    ticket_id: Optional[UUID] = Field(None, description="Ticket ID (null for free events)")
    quantity: int = Field(1, ge=1, le=10, description="Number of tickets (max 10)")
    
    # Registration details
    attendee_info: Optional[Dict[str, Any]] = Field(None, description="Attendee information")
    special_requirements: Optional[str] = Field(None, description="Special requirements")
    emergency_contact: Optional[Dict[str, str]] = Field(None, description="Emergency contact")
    dietary_preferences: Optional[List[str]] = Field(None, description="Dietary preferences")
    accessibility_needs: Optional[str] = Field(None, description="Accessibility needs")
    
    # Payment details (for paid events)
    payment_method: Optional[str] = Field("payfast", description="Payment method")
    return_url: Optional[str] = Field(None, description="Custom return URL after payment")
    cancel_url: Optional[str] = Field(None, description="Custom cancel URL")


class EventBookingResponse(BaseModel):
    """Response from event booking request"""
    
    registration_id: UUID = Field(..., description="Registration ID")
    event_id: UUID = Field(..., description="Event ID")
    ticket_id: Optional[UUID] = Field(None, description="Ticket ID")
    quantity: int = Field(..., description="Number of tickets")
    total_amount: Decimal = Field(..., description="Total amount")
    currency: str = Field(..., description="Currency")
    status: str = Field(..., description="Registration status")
    
    # Payment details (if applicable)
    requires_payment: bool = Field(..., description="Whether payment is required")
    payment_id: Optional[UUID] = Field(None, description="Payment ID")
    payment_url: Optional[str] = Field(None, description="PayFast payment URL")
    payment_data: Optional[Dict[str, Any]] = Field(None, description="Payment form data")
    
    message: str = Field(..., description="Booking status message")


class EventRegistrationStatusResponse(BaseModel):
    """Event registration status response"""
    
    registration_id: UUID
    event_id: UUID
    user_id: UUID
    status: str
    total_amount: Decimal
    currency: str
    payment_status: Optional[str] = None
    payment_id: Optional[UUID] = None
    registered_at: Optional[str] = None
    confirmed_at: Optional[str] = None


# ==================== EVENT BOOKING ROUTES ====================

@router.post("/book", response_model=EventBookingResponse)
def book_event(
    booking_request: EventBookingRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))
):
    """
    Book an event with automatic registration and payment processing.
    
    This endpoint handles the complete booking flow:
    1. Validates event and ticket availability
    2. Creates registration record
    3. Initiates payment if required
    4. Returns booking confirmation with payment details
    """
    current_user = get_current_user(token, db)
    
    try:
        # 1. Validate event exists and is bookable
        event = get_event_by_id(db, booking_request.event_id)
        if not event:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Event not found"
            )
        
        if event.status != "PUBLISHED":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Event is not available for booking"
            )
        
        # 2. Validate ticket if specified
        ticket = None
        total_amount = Decimal('0.00')
        currency = "PKR"
        
        if booking_request.ticket_id:
            ticket = get_event_ticket_by_id(db, booking_request.ticket_id)
            if not ticket or ticket.event_id != booking_request.event_id:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Ticket not found for this event"
                )
            
            # Check ticket availability
            availability = check_ticket_availability(db, booking_request.ticket_id)
            if availability['available_quantity'] < booking_request.quantity:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Only {availability['available_quantity']} tickets available"
                )
            
            total_amount = ticket.price * booking_request.quantity
            currency = ticket.currency
        
        # 3. Create registration
        registration_data = EventRegistrationCreate(
            event_id=booking_request.event_id,
            ticket_id=booking_request.ticket_id,
            quantity=booking_request.quantity,
            attendee_info=booking_request.attendee_info,
            special_requirements=booking_request.special_requirements,
            emergency_contact=booking_request.emergency_contact,
            dietary_preferences=booking_request.dietary_preferences,
            accessibility_needs=booking_request.accessibility_needs
        )
        
        registration = create_event_registration(db, registration_data, current_user.id)
        
        # 4. Handle payment if required
        requires_payment = total_amount > 0
        payment_id = None
        payment_url = None
        payment_data = None
        booking_status = "confirmed"
        message = "Event booked successfully!"
        
        if requires_payment:
            # Check if PayFast service is available
            if not payfast_service:
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="Payment service is not available. Please contact support."
                )

            # Create PayFast payment
            payment_request = PayFastEventPaymentRequest(
                registration_id=registration.id,
                amount=total_amount,
                currency=currency,
                user_email=current_user.email,
                user_name=f"{current_user.username}",
                return_url=booking_request.return_url,
                cancel_url=booking_request.cancel_url
            )

            payment_result = payfast_service.create_event_payment(
                db=db,
                registration_id=registration.id,
                amount=total_amount,
                currency=currency,
                user_email=current_user.email,
                user_name=current_user.username
            )
            
            payment_id = payment_result.get('payment_id')
            payment_url = payment_result.get('payment_url')
            payment_data = payment_result.get('payment_data')
            booking_status = "pending_payment"
            message = "Registration created. Please complete payment to confirm booking."
        
        return EventBookingResponse(
            registration_id=registration.id,
            event_id=booking_request.event_id,
            ticket_id=booking_request.ticket_id,
            quantity=booking_request.quantity,
            total_amount=total_amount,
            currency=currency,
            status=booking_status,
            requires_payment=requires_payment,
            payment_id=payment_id,
            payment_url=payment_url,
            payment_data=payment_data,
            message=message
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to book event: {str(e)}"
        )


@router.get("/registrations/{registration_id}/status", response_model=EventRegistrationStatusResponse)
def get_registration_status(
    registration_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get the status of an event registration including payment status.
    """
    current_user = get_current_user(token, db)

    try:
        registration = get_registration_by_id(db, registration_id, current_user.id)
        if not registration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Registration not found"
            )

        # Check if user owns this registration or is the event organizer
        if registration.user_id != current_user.id:
            event = get_event_by_id(db, registration.event_id)
            if event.organizer_id != current_user.id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied"
                )

        # Get payment status if applicable
        payment = db.query(EventPayment).filter(
            EventPayment.registration_id == registration_id
        ).first()

        payment_status = None
        payment_id = None
        if payment:
            payment_status = payment.status.value
            payment_id = payment.id

        return EventRegistrationStatusResponse(
            registration_id=registration.id,
            event_id=registration.event_id,
            user_id=registration.user_id,
            status=registration.status.value,
            total_amount=registration.total_amount,
            currency=registration.currency,
            payment_status=payment_status,
            payment_id=payment_id,
            registered_at=registration.registered_at.isoformat() if registration.registered_at else None,
            confirmed_at=registration.confirmed_at.isoformat() if registration.confirmed_at else None
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get registration status: {str(e)}"
        )


@router.post("/registrations/{registration_id}/payment", response_model=PayFastPaymentResponse)
def create_payment_for_registration(
    registration_id: UUID,
    payment_request: PayFastEventPaymentRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Create a PayFast payment for an existing registration.

    This is useful when a registration was created but payment failed or was cancelled.
    """
    current_user = get_current_user(token, db)

    try:
        registration = get_registration_by_id(db, registration_id, current_user.id)
        if not registration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Registration not found"
            )

        # Check if user owns this registration
        if registration.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )

        # Check if payment is already completed
        existing_payment = db.query(EventPayment).filter(
            EventPayment.registration_id == registration_id,
            EventPayment.status == PaymentStatusEnum.COMPLETED
        ).first()

        if existing_payment:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Payment already completed for this registration"
            )

        # Check if PayFast service is available
        if not payfast_service:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Payment service is not available. Please contact support."
            )

        # Create PayFast payment
        payment_result = payfast_service.create_event_payment(
            db=db,
            registration_id=registration_id,
            amount=payment_request.amount,
            currency=payment_request.currency,
            user_email=payment_request.user_email or current_user.email,
            user_name=payment_request.user_name or current_user.username
        )

        return PayFastPaymentResponse(**payment_result)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create payment: {str(e)}"
        )


@router.get("/payments/{payment_id}/status", response_model=PayFastPaymentStatusResponse)
def get_payment_status(
    payment_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get the status of a PayFast payment.
    """
    current_user = get_current_user(token, db)

    try:
        payment = db.query(EventPayment).filter(EventPayment.id == payment_id).first()
        if not payment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Payment not found"
            )

        # Check if user owns this payment
        if payment.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )

        return PayFastPaymentStatusResponse(
            payment_id=payment.id,
            payfast_payment_id=payment.gateway_transaction_id,
            status=payment.status,
            amount=payment.amount,
            currency=payment.currency,
            created_at=payment.created_at,
            processed_at=payment.processed_at,
            failed_at=payment.failed_at,
            failure_reason=payment.failure_reason,
            gateway_response=payment.gateway_response
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get payment status: {str(e)}"
        )


@router.get("/my-bookings", response_model=List[EventRegistrationStatusResponse])
def get_my_event_bookings(
    skip: int = 0,
    limit: int = 20,
    status_filter: Optional[str] = None,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get all event bookings for the current user.
    """
    current_user = get_current_user(token, db)

    try:
        query = db.query(EventRegistration).filter(
            EventRegistration.user_id == current_user.id
        )

        if status_filter:
            query = query.filter(EventRegistration.status == status_filter)

        registrations = query.order_by(
            EventRegistration.created_at.desc()
        ).offset(skip).limit(limit).all()

        result = []
        for registration in registrations:
            # Get payment status if applicable
            payment = db.query(EventPayment).filter(
                EventPayment.registration_id == registration.id
            ).first()

            payment_status = None
            payment_id = None
            if payment:
                payment_status = payment.status.value
                payment_id = payment.id

            result.append(EventRegistrationStatusResponse(
                registration_id=registration.id,
                event_id=registration.event_id,
                user_id=registration.user_id,
                status=registration.status.value,
                total_amount=registration.total_amount,
                currency=registration.currency,
                payment_status=payment_status,
                payment_id=payment_id,
                registered_at=registration.registered_at.isoformat() if registration.registered_at else None,
                confirmed_at=registration.confirmed_at.isoformat() if registration.confirmed_at else None
            ))

        return result

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get bookings: {str(e)}"
        )


@router.post("/registrations/{registration_id}/cancel")
def cancel_event_registration(
    registration_id: UUID,
    reason: Optional[str] = "User requested cancellation",
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Cancel an event registration and initiate refund if applicable.
    """
    current_user = get_current_user(token, db)

    try:
        registration = get_registration_by_id(db, registration_id, current_user.id)
        if not registration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Registration not found"
            )

        # Check if user owns this registration
        if registration.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )

        # Check if registration can be cancelled
        if registration.status == "CANCELLED":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Registration is already cancelled"
            )

        if registration.status == "ATTENDED":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot cancel registration after attending event"
            )

        # Update registration status
        registration.status = "CANCELLED"
        registration.cancelled_at = datetime.now(timezone.utc)

        # Handle refund if payment was completed
        payment = db.query(EventPayment).filter(
            EventPayment.registration_id == registration_id,
            EventPayment.status == PaymentStatusEnum.COMPLETED
        ).first()

        refund_initiated = False
        if payment and payment.amount > 0:
            # Note: Actual refund processing would be handled by PayFast service
            # For now, we just mark the payment for refund
            payment.refund_reason = reason
            payment.refunded_at = datetime.now(timezone.utc)
            refund_initiated = True

        db.commit()

        return {
            "message": "Registration cancelled successfully",
            "registration_id": registration_id,
            "status": "cancelled",
            "refund_initiated": refund_initiated,
            "refund_amount": payment.amount if payment else 0
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to cancel registration: {str(e)}"
        )


@router.post("/payments/confirm")
def confirm_payment_success(
    payment_id: UUID,
    payfast_payment_id: str,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Confirm payment success after user returns from PayFast.

    This endpoint is called when user returns from successful PayFast payment.
    """
    current_user = get_current_user(token, db)

    try:
        payment = db.query(EventPayment).filter(EventPayment.id == payment_id).first()
        if not payment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Payment not found"
            )

        # Check if user owns this payment
        if payment.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )

        # Update payment status (will be confirmed by webhook)
        payment.gateway_transaction_id = payfast_payment_id
        payment.processed_at = datetime.now(timezone.utc)

        # Update registration status
        registration = db.query(EventRegistration).filter(
            EventRegistration.id == payment.registration_id
        ).first()

        if registration:
            registration.status = RegistrationStatusEnum.CONFIRMED
            registration.confirmed_at = datetime.now(timezone.utc)

        db.commit()

        return {
            "message": "Payment confirmation received",
            "payment_id": payment_id,
            "registration_id": payment.registration_id,
            "status": "confirmed",
            "note": "Final confirmation will be processed via webhook"
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to confirm payment: {str(e)}"
        )
