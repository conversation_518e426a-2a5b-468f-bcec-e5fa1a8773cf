"""
Institute Payout Dashboard Routes for EduFair Platform

This module provides APIs for institutes to:
- View their sales reports and revenue
- Track payout history and status
- Manage bank account details for payouts
- Monitor pending revenue distributions
"""

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from uuid import UUID

# Import dependencies
from config.session import get_db
from config.deps import oauth2_scheme, get_current_user
from config.permission import require_type

# Import CRUD operations
from Cruds.Institute.PayoutDashboard import (
    get_institute_payout_summary, get_institute_payouts, 
    get_institute_sales_report, update_institute_bank_details
)

# Import schemas
from Schemas.Payouts import InstitutePayoutSummary, PayoutOut
from Models.users import UserTypeEnum

router = APIRouter()


# ==================== PAYOUT DASHBOARD ====================

@router.get("/dashboard", response_model=InstitutePayoutSummary)
def get_institute_payout_dashboard(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Get comprehensive payout dashboard for the institute.
    
    Provides overview of:
    - Total payouts received and pending
    - Recent payout history
    - Events with pending payouts
    - Bank account setup status
    """
    current_user = get_current_user(token, db)
    
    try:
        return get_institute_payout_summary(db, current_user.id)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get payout dashboard: {str(e)}"
        )


@router.get("/payouts", response_model=List[PayoutOut])
def get_institute_payout_history(
    skip: int = Query(0, ge=0, description="Number of payouts to skip"),
    limit: int = Query(20, ge=1, le=100, description="Number of payouts to return"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Get paginated payout history for the institute.
    """
    current_user = get_current_user(token, db)
    
    try:
        return get_institute_payouts(db, current_user.id, skip, limit)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get payout history: {str(e)}"
        )


# ==================== SALES REPORTS ====================

@router.get("/sales-report")
def get_sales_report(
    days: int = Query(30, ge=1, le=365, description="Number of days to include in report"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Get sales report for the institute.
    
    Shows revenue and registration data for the specified period.
    """
    current_user = get_current_user(token, db)
    
    try:
        return get_institute_sales_report(db, current_user.id, days)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get sales report: {str(e)}"
        )


@router.get("/revenue-summary")
def get_revenue_summary(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Get quick revenue summary for the institute.
    """
    current_user = get_current_user(token, db)
    
    try:
        # Get 30-day and all-time reports
        report_30_days = get_institute_sales_report(db, current_user.id, 30)
        report_all_time = get_institute_sales_report(db, current_user.id, 3650)  # ~10 years
        
        payout_summary = get_institute_payout_summary(db, current_user.id)
        
        return {
            "last_30_days": {
                "revenue": report_30_days["total_revenue"],
                "registrations": report_30_days["total_registrations"],
                "events": report_30_days["total_events"]
            },
            "all_time": {
                "revenue": report_all_time["total_revenue"],
                "registrations": report_all_time["total_registrations"],
                "events": report_all_time["total_events"]
            },
            "payouts": {
                "total_received": float(payout_summary.total_amount_received),
                "pending_amount": float(payout_summary.pending_amount),
                "pending_count": payout_summary.pending_payouts,
                "events_pending_payout": len(payout_summary.events_pending_payout)
            },
            "bank_setup": {
                "has_bank_details": payout_summary.has_bank_details,
                "preferred_method": payout_summary.preferred_payout_method.value if payout_summary.preferred_payout_method else None
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get revenue summary: {str(e)}"
        )


# ==================== BANK ACCOUNT MANAGEMENT ====================

@router.put("/bank-details")
def update_bank_details(
    bank_details: Dict[str, Any],
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Update bank account details for payouts.
    
    Accepts any combination of:
    - bank_account_number
    - bank_name
    - account_holder_name
    - bank_branch
    - iban
    - swift_code
    - jazzcash_number
    - easypaisa_number
    - paypal_email
    """
    current_user = get_current_user(token, db)
    
    try:
        return update_institute_bank_details(db, current_user.id, bank_details)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update bank details: {str(e)}"
        )


@router.get("/bank-details")
def get_bank_details(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Get current bank account details (masked for security).
    """
    current_user = get_current_user(token, db)
    
    try:
        from Models.users import User, InstituteProfile
        
        institute = db.query(User).join(InstituteProfile).filter(User.id == current_user.id).first()
        if not institute or not institute.institute_profile:
            raise HTTPException(status_code=404, detail="Institute profile not found")
        
        profile = institute.institute_profile
        
        # Mask sensitive information
        def mask_account_number(account_num):
            if not account_num or len(account_num) < 4:
                return account_num
            return "*" * (len(account_num) - 4) + account_num[-4:]
        
        def mask_phone_number(phone):
            if not phone or len(phone) < 4:
                return phone
            return "*" * (len(phone) - 4) + phone[-4:]
        
        return {
            "bank_account_number": mask_account_number(profile.bank_account_number),
            "bank_name": profile.bank_name,
            "account_holder_name": profile.account_holder_name,
            "bank_branch": profile.bank_branch,
            "iban": mask_account_number(profile.iban),
            "swift_code": profile.swift_code,
            "jazzcash_number": mask_phone_number(profile.jazzcash_number),
            "easypaisa_number": mask_phone_number(profile.easypaisa_number),
            "paypal_email": profile.paypal_email,
            "has_bank_details": bool(profile.bank_account_number and profile.bank_name),
            "has_jazzcash": bool(profile.jazzcash_number),
            "has_easypaisa": bool(profile.easypaisa_number),
            "has_paypal": bool(profile.paypal_email)
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get bank details: {str(e)}"
        )


# ==================== PAYOUT REQUESTS ====================

@router.get("/pending-revenue")
def get_pending_revenue(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Get details of events with revenue but no payout created yet.
    """
    current_user = get_current_user(token, db)
    
    try:
        payout_summary = get_institute_payout_summary(db, current_user.id)
        
        total_pending_amount = sum(
            event.net_payout_amount for event in payout_summary.events_pending_payout
        )
        
        return {
            "events_pending_payout": [
                {
                    "event_id": str(event.event_id),
                    "event_title": event.event_title,
                    "total_revenue": float(event.total_revenue),
                    "commission_amount": float(event.commission_amount),
                    "net_payout_amount": float(event.net_payout_amount),
                    "registrations": event.confirmed_registrations
                }
                for event in payout_summary.events_pending_payout
            ],
            "total_events": len(payout_summary.events_pending_payout),
            "total_pending_amount": float(total_pending_amount),
            "note": "Payouts are processed manually by admin. Contact support if you have questions about pending payouts."
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get pending revenue: {str(e)}"
        )


@router.get("/payout-status/{event_id}")
def get_event_payout_status(
    event_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Get payout status for a specific event.
    """
    current_user = get_current_user(token, db)
    
    try:
        from Models.Events import Event, Payout
        
        # Verify event belongs to this institute
        event = db.query(Event).filter(
            Event.id == event_id,
            Event.organizer_id == current_user.id
        ).first()
        
        if not event:
            raise HTTPException(status_code=404, detail="Event not found")
        
        # Get payout for this event
        payout = db.query(Payout).filter(Payout.event_id == event_id).first()
        
        if not payout:
            # Calculate potential payout
            from Cruds.Admin.Payouts import calculate_event_revenue
            revenue_breakdown = calculate_event_revenue(db, event_id)
            
            return {
                "event_id": str(event_id),
                "event_title": event.title,
                "has_payout": False,
                "total_revenue": float(revenue_breakdown.total_revenue),
                "estimated_payout": float(revenue_breakdown.net_payout_amount),
                "commission_amount": float(revenue_breakdown.commission_amount),
                "status": "No payout created yet"
            }
        else:
            return {
                "event_id": str(event_id),
                "event_title": event.title,
                "has_payout": True,
                "payout_id": str(payout.id),
                "payout_amount": float(payout.amount),
                "status": payout.status.value,
                "payout_method": payout.method.value,
                "created_at": payout.created_at.isoformat(),
                "processed_at": payout.processed_at.isoformat() if payout.processed_at else None,
                "transaction_reference": payout.transaction_reference
            }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get payout status: {str(e)}"
        )
