"""
Mentor Capabilities API Routes for Enhanced Event System
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
import uuid

# Import dependencies
from config.session import get_db
from config.security import oauth2_scheme
from config.deps import get_current_user
from config.permission import require_type

# Import Models
from Models.users import User, UserTypeEnum

# Import CRUD functions
from Cruds.Users.MentorCapabilities import (
    update_mentor_capabilities, get_mentor_users, search_mentors,
    get_mentor_performance_metrics, get_mentor_dashboard_summary
)

# Import Schemas
from Schemas.Users.MentorCapabilities import (
    MentorCapabilitiesUpdate, EnhancedUserBase, MentorUserOut, TeacherMentorOut,
    MentorSearchFilters, MentorSearchResponse, MentorListResponse,
    MentorPerformanceMetrics, MentorDashboardSummary
)

router = APIRouter()


# ===== Mentor Capabilities Management Routes =====

@router.put("/capabilities", response_model=EnhancedUserBase)
def update_my_mentor_capabilities_endpoint(
    capabilities_data: MentorCapabilitiesUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update mentor capabilities for current user"""
    if current_user.user_type not in [UserTypeEnum.teacher, UserTypeEnum.mentor]:
        raise HTTPException(
            status_code=403, 
            detail="Only teachers and mentors can update mentor capabilities"
        )
    
    return update_mentor_capabilities(db, current_user.id, capabilities_data)


@router.put("/{user_id}/capabilities", response_model=EnhancedUserBase)
def update_user_mentor_capabilities_endpoint(
    user_id: uuid.UUID,
    capabilities_data: MentorCapabilitiesUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_type("admin"))
):
    """Update mentor capabilities for any user (Admin only)"""
    return update_mentor_capabilities(db, user_id, capabilities_data)


@router.get("/capabilities/my", response_model=EnhancedUserBase)
def get_my_mentor_capabilities_endpoint(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get mentor capabilities for current user"""
    return EnhancedUserBase.model_validate(current_user)


# ===== Mentor Discovery Routes =====

@router.get("/mentors", response_model=MentorListResponse)
def get_mentors_endpoint(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    user_type: Optional[str] = Query(None, description="Filter by user type: mentor, teacher"),
    available_only: bool = Query(False),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get list of users with mentor capabilities"""
    return get_mentor_users(db, skip=skip, limit=limit, user_type=user_type, available_only=available_only)


@router.post("/mentors/search", response_model=MentorSearchResponse)
def search_mentors_endpoint(
    filters: MentorSearchFilters,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Search for mentors based on filters"""
    return search_mentors(db, filters, skip=skip, limit=limit)


@router.get("/mentors/available", response_model=MentorListResponse)
def get_available_mentors_endpoint(
    specializations: Optional[List[str]] = Query(None),
    min_experience: Optional[int] = Query(None, ge=0),
    institute_id: Optional[uuid.UUID] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get available mentors with optional filters"""
    filters = MentorSearchFilters(
        specializations=specializations,
        min_experience_years=min_experience,
        is_available=True,
        institute_id=institute_id
    )
    
    search_result = search_mentors(db, filters, skip=0, limit=50)
    
    # Convert search results to mentor list format
    mentors = [result.mentor for result in search_result.results]
    
    return MentorListResponse(
        mentors=mentors,
        total=len(mentors),
        available=len(mentors),
        busy=0,
        page=1,
        per_page=50
    )


# ===== Teacher-as-Mentor Routes =====

@router.get("/teachers/mentors", response_model=List[TeacherMentorOut])
def get_teacher_mentors_endpoint(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get teachers who can act as mentors"""
    from sqlalchemy import and_
    
    teachers = db.query(User).filter(
        and_(
            User.user_type == UserTypeEnum.teacher,
            User.can_act_as_mentor == True
        )
    ).offset(skip).limit(limit).all()
    
    teacher_outs = []
    for teacher in teachers:
        # Get mentor performance data
        from Models.Competitions import CompetitionMentorAssignment, MentorAssignmentStatusEnum
        
        competitions_judged = db.query(CompetitionMentorAssignment).filter(
            and_(
                CompetitionMentorAssignment.mentor_id == teacher.id,
                CompetitionMentorAssignment.status == MentorAssignmentStatusEnum.COMPLETED
            )
        ).count()
        
        teacher_dict = {
            "id": teacher.id,
            "username": teacher.username,
            "email": teacher.email,
            "user_type": teacher.user_type.value,
            "profile_picture": teacher.profile_picture,
            "subjects_taught": None,  # Would need to add this field
            "institution": None,  # Would need to add this field
            "can_act_as_mentor": teacher.can_act_as_mentor,
            "mentor_specializations": teacher.mentor_specializations,
            "judging_experience_years": teacher.judging_experience_years,
            "competitions_judged_as_mentor": competitions_judged,
            "mentor_rating": None  # Would calculate from feedback
        }
        teacher_outs.append(TeacherMentorOut.model_validate(teacher_dict))
    
    return teacher_outs


@router.post("/teachers/{teacher_id}/enable-mentor")
def enable_teacher_as_mentor_endpoint(
    teacher_id: uuid.UUID,
    specializations: List[str],
    experience_years: int = 0,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_type("admin"))
):
    """Enable a teacher to act as mentor (Admin only)"""
    teacher = db.query(User).filter(User.id == teacher_id).first()
    if not teacher:
        raise HTTPException(status_code=404, detail="Teacher not found")
    
    if teacher.user_type != UserTypeEnum.teacher:
        raise HTTPException(status_code=400, detail="User is not a teacher")
    
    # Update mentor capabilities
    capabilities_data = MentorCapabilitiesUpdate(
        can_act_as_mentor=True,
        mentor_specializations=specializations,
        judging_experience_years=experience_years
    )
    
    updated_teacher = update_mentor_capabilities(db, teacher_id, capabilities_data)
    
    return {
        "success": True,
        "message": f"Teacher {teacher.username} enabled as mentor",
        "teacher": updated_teacher
    }


@router.post("/teachers/{teacher_id}/disable-mentor")
def disable_teacher_as_mentor_endpoint(
    teacher_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_type("admin"))
):
    """Disable a teacher from acting as mentor (Admin only)"""
    teacher = db.query(User).filter(User.id == teacher_id).first()
    if not teacher:
        raise HTTPException(status_code=404, detail="Teacher not found")
    
    if teacher.user_type != UserTypeEnum.teacher:
        raise HTTPException(status_code=400, detail="User is not a teacher")
    
    # Check if teacher has active assignments
    from Models.Competitions import CompetitionMentorAssignment, MentorAssignmentStatusEnum
    
    active_assignments = db.query(CompetitionMentorAssignment).filter(
        and_(
            CompetitionMentorAssignment.mentor_id == teacher_id,
            CompetitionMentorAssignment.status.in_([
                MentorAssignmentStatusEnum.ASSIGNED,
                MentorAssignmentStatusEnum.ACCEPTED
            ])
        )
    ).count()
    
    if active_assignments > 0:
        raise HTTPException(
            status_code=400, 
            detail=f"Cannot disable mentor capabilities. Teacher has {active_assignments} active assignments."
        )
    
    # Disable mentor capabilities
    capabilities_data = MentorCapabilitiesUpdate(can_act_as_mentor=False)
    updated_teacher = update_mentor_capabilities(db, teacher_id, capabilities_data)
    
    return {
        "success": True,
        "message": f"Teacher {teacher.username} disabled as mentor",
        "teacher": updated_teacher
    }


# ===== Mentor Performance Routes =====

@router.get("/mentors/{mentor_id}/performance", response_model=MentorPerformanceMetrics)
def get_mentor_performance_endpoint(
    mentor_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get performance metrics for a mentor"""
    # Check permissions
    if current_user.user_type in [UserTypeEnum.mentor, UserTypeEnum.teacher]:
        if current_user.id != mentor_id:
            raise HTTPException(status_code=403, detail="Can only view your own performance metrics")
    
    return get_mentor_performance_metrics(db, mentor_id)


@router.get("/mentors/my/performance", response_model=MentorPerformanceMetrics)
def get_my_performance_endpoint(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get performance metrics for current mentor"""
    if current_user.user_type not in [UserTypeEnum.mentor, UserTypeEnum.teacher]:
        raise HTTPException(status_code=403, detail="Only mentors can access this endpoint")
    
    return get_mentor_performance_metrics(db, current_user.id)


@router.get("/mentors/my/dashboard", response_model=MentorDashboardSummary)
def get_my_mentor_dashboard_endpoint(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get dashboard summary for current mentor"""
    if current_user.user_type not in [UserTypeEnum.mentor, UserTypeEnum.teacher]:
        raise HTTPException(status_code=403, detail="Only mentors can access this endpoint")
    
    return get_mentor_dashboard_summary(db, current_user.id)


# ===== Mentor Specialization Routes =====

@router.get("/specializations")
def get_available_specializations_endpoint(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get list of available specializations"""
    # Get unique specializations from all mentors
    from sqlalchemy import func, text
    
    # This is a simplified approach - in production you might want a dedicated specializations table
    specializations = [
        "Mathematics",
        "Science",
        "Programming",
        "Computer Science",
        "Physics",
        "Chemistry",
        "Biology",
        "Engineering",
        "Data Science",
        "Machine Learning",
        "Web Development",
        "Mobile Development",
        "Database Design",
        "System Architecture",
        "Project Management",
        "Business Analysis",
        "Digital Marketing",
        "Graphic Design",
        "UI/UX Design",
        "Technical Writing"
    ]
    
    # Get counts for each specialization
    specialization_counts = {}
    for spec in specializations:
        count = db.query(User).filter(
            and_(
                User.can_act_as_mentor == True,
                func.json_search(User.mentor_specializations, 'one', spec) != None
            )
        ).count()
        specialization_counts[spec] = count
    
    return {
        "specializations": specializations,
        "counts": specialization_counts,
        "total_mentors": db.query(User).filter(User.can_act_as_mentor == True).count()
    }


@router.get("/specializations/popular")
def get_popular_specializations_endpoint(
    limit: int = Query(10, ge=1, le=20),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get most popular specializations"""
    # This would be more sophisticated in production
    popular_specializations = [
        {"name": "Programming", "mentor_count": 45, "demand_score": 95},
        {"name": "Data Science", "mentor_count": 32, "demand_score": 88},
        {"name": "Web Development", "mentor_count": 38, "demand_score": 85},
        {"name": "Mathematics", "mentor_count": 28, "demand_score": 82},
        {"name": "Machine Learning", "mentor_count": 25, "demand_score": 80},
        {"name": "Computer Science", "mentor_count": 35, "demand_score": 78},
        {"name": "Mobile Development", "mentor_count": 22, "demand_score": 75},
        {"name": "UI/UX Design", "mentor_count": 18, "demand_score": 72},
        {"name": "Database Design", "mentor_count": 20, "demand_score": 70},
        {"name": "System Architecture", "mentor_count": 15, "demand_score": 68}
    ]
    
    return {
        "popular_specializations": popular_specializations[:limit],
        "last_updated": "2024-01-01T00:00:00Z",
        "methodology": "Based on mentor count and competition demand"
    }


# ===== Mentor Collaboration Routes =====

@router.get("/mentors/{mentor_id}/collaborations")
def get_mentor_collaborations_endpoint(
    mentor_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get mentor's institute collaborations"""
    # Check permissions
    if current_user.user_type in [UserTypeEnum.mentor, UserTypeEnum.teacher]:
        if current_user.id != mentor_id:
            raise HTTPException(status_code=403, detail="Can only view your own collaborations")
    
    from Models.Competitions import CompetitionMentorAssignment
    from sqlalchemy import distinct
    
    # Get unique institutes this mentor has worked with
    collaborations = db.query(
        CompetitionMentorAssignment.institute_id,
        func.count(CompetitionMentorAssignment.id).label('total_assignments'),
        func.count(
            case(
                (CompetitionMentorAssignment.status == 'completed', 1)
            )
        ).label('completed_assignments')
    ).filter(
        CompetitionMentorAssignment.mentor_id == mentor_id
    ).group_by(CompetitionMentorAssignment.institute_id).all()
    
    collaboration_details = []
    for collab in collaborations:
        institute = db.query(User).filter(User.id == collab.institute_id).first()
        if institute:
            collaboration_details.append({
                "institute_id": collab.institute_id,
                "institute_name": institute.username,
                "total_assignments": collab.total_assignments,
                "completed_assignments": collab.completed_assignments,
                "success_rate": (collab.completed_assignments / collab.total_assignments * 100) if collab.total_assignments > 0 else 0
            })
    
    return {
        "mentor_id": mentor_id,
        "collaborations": collaboration_details,
        "total_institutes": len(collaboration_details),
        "total_assignments": sum(c["total_assignments"] for c in collaboration_details)
    }
