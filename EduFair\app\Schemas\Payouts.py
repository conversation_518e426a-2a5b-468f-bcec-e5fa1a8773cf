"""
Payout Schemas for EduFair Platform

This module contains Pydantic schemas for the Payout system including:
- Payout creation and management
- Revenue distribution tracking
- Bank account and payment method handling
- Admin payout dashboard
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator
from datetime import datetime
from uuid import UUID
from decimal import Decimal

# Import Enums
from Models.Events import PayoutStatusEnum, PayoutMethodEnum


# ===== BASE PAYOUT SCHEMAS =====

class PayoutBase(BaseModel):
    institute_id: UUID = Field(..., description="Institute receiving the payout")
    event_id: UUID = Field(..., description="Event for which payout is being made")
    amount: Decimal = Field(..., gt=0, description="Payout amount")
    currency: str = Field("PKR", max_length=3, description="Currency code")
    method: PayoutMethodEnum = Field(..., description="Payout method")
    notes: Optional[str] = Field(None, max_length=1000, description="Administrative notes")


class PayoutCreate(PayoutBase):
    commission_rate: Optional[Decimal] = Field(None, ge=0, le=100, description="Commission percentage")
    commission_amount: Optional[Decimal] = Field(None, ge=0, description="Commission amount")
    gross_revenue: Optional[Decimal] = Field(None, ge=0, description="Total event revenue")
    
    # Bank details (required for bank transfers)
    bank_account_number: Optional[str] = Field(None, max_length=50)
    bank_name: Optional[str] = Field(None, max_length=100)
    account_holder_name: Optional[str] = Field(None, max_length=100)
    
    # Mobile wallet details (required for JazzCash/Easypaisa)
    mobile_wallet_number: Optional[str] = Field(None, max_length=20)
    
    @validator('bank_account_number')
    def validate_bank_details(cls, v, values):
        method = values.get('method')
        if method == PayoutMethodEnum.BANK_TRANSFER and not v:
            raise ValueError('Bank account number is required for bank transfers')
        return v
    
    @validator('mobile_wallet_number')
    def validate_wallet_details(cls, v, values):
        method = values.get('method')
        if method in [PayoutMethodEnum.JAZZCASH, PayoutMethodEnum.EASYPAISA] and not v:
            raise ValueError('Mobile wallet number is required for mobile wallet transfers')
        return v


class PayoutUpdate(BaseModel):
    status: Optional[PayoutStatusEnum] = None
    transaction_reference: Optional[str] = Field(None, max_length=100)
    payment_proof_url: Optional[str] = Field(None, max_length=500)
    notes: Optional[str] = Field(None, max_length=1000)
    failure_reason: Optional[str] = Field(None, max_length=1000)


class PayoutOut(PayoutBase):
    id: UUID
    status: PayoutStatusEnum
    commission_rate: Optional[Decimal]
    commission_amount: Optional[Decimal]
    gross_revenue: Optional[Decimal]
    
    # Payment details
    bank_account_number: Optional[str]
    bank_name: Optional[str]
    account_holder_name: Optional[str]
    mobile_wallet_number: Optional[str]
    
    # Transaction tracking
    transaction_reference: Optional[str]
    payment_proof_url: Optional[str]
    
    # Administrative details
    created_by: UUID
    processed_by: Optional[UUID]
    failure_reason: Optional[str]
    
    # Timestamps
    payout_date: Optional[datetime]
    processed_at: Optional[datetime]
    failed_at: Optional[datetime]
    cancelled_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime
    
    # Related data
    institute_name: Optional[str] = None
    event_title: Optional[str] = None
    creator_name: Optional[str] = None
    processor_name: Optional[str] = None

    class Config:
        from_attributes = True


# ===== PAYOUT MANAGEMENT SCHEMAS =====

class PayoutListFilter(BaseModel):
    institute_id: Optional[UUID] = None
    event_id: Optional[UUID] = None
    status: Optional[List[PayoutStatusEnum]] = None
    method: Optional[List[PayoutMethodEnum]] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    min_amount: Optional[Decimal] = None
    max_amount: Optional[Decimal] = None


class PayoutListResponse(BaseModel):
    payouts: List[PayoutOut]
    total_count: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool
    filters_applied: Optional[PayoutListFilter] = None


# ===== REVENUE CALCULATION SCHEMAS =====

class EventRevenueBreakdown(BaseModel):
    event_id: UUID
    event_title: str
    institute_id: UUID
    institute_name: str
    
    # Revenue details
    total_registrations: int
    confirmed_registrations: int
    total_revenue: Decimal
    commission_rate: Decimal
    commission_amount: Decimal
    net_payout_amount: Decimal
    
    # Ticket breakdown
    ticket_breakdown: Dict[str, Dict[str, Any]] = Field(
        description="Breakdown by ticket type: {ticket_type: {count, revenue, price}}"
    )
    
    # Payment status
    pending_payments: Decimal
    completed_payments: Decimal
    refunded_payments: Decimal
    
    # Payout status
    has_existing_payout: bool
    existing_payout_id: Optional[UUID] = None
    existing_payout_status: Optional[PayoutStatusEnum] = None


class RevenueCalculationRequest(BaseModel):
    event_id: UUID
    commission_rate: Decimal = Field(default=Decimal('10.0'), ge=0, le=100, description="Commission percentage")
    include_pending_payments: bool = Field(default=False, description="Include pending payments in calculation")


# ===== ADMIN DASHBOARD SCHEMAS =====

class AdminPayoutDashboard(BaseModel):
    # Summary statistics
    total_payouts: int
    pending_payouts: int
    completed_payouts: int
    failed_payouts: int
    
    # Financial summary
    total_payout_amount: Decimal
    total_commission_earned: Decimal
    pending_payout_amount: Decimal
    
    # Recent activity
    recent_payouts: List[PayoutOut]
    events_requiring_payout: List[EventRevenueBreakdown]
    
    # Monthly trends
    monthly_payout_trends: Dict[str, Decimal] = Field(
        description="Monthly payout amounts: {YYYY-MM: amount}"
    )
    monthly_commission_trends: Dict[str, Decimal] = Field(
        description="Monthly commission amounts: {YYYY-MM: amount}"
    )


# ===== INSTITUTE PAYOUT DASHBOARD SCHEMAS =====

class InstitutePayoutSummary(BaseModel):
    institute_id: UUID
    institute_name: str
    
    # Payout statistics
    total_payouts_received: int
    total_amount_received: Decimal
    pending_payouts: int
    pending_amount: Decimal
    
    # Recent payouts
    recent_payouts: List[PayoutOut]
    
    # Events with pending payouts
    events_pending_payout: List[EventRevenueBreakdown]
    
    # Bank account status
    has_bank_details: bool
    preferred_payout_method: Optional[PayoutMethodEnum] = None


# ===== BULK OPERATIONS SCHEMAS =====

class BulkPayoutCreate(BaseModel):
    event_ids: List[UUID] = Field(..., min_items=1, max_items=50)
    commission_rate: Decimal = Field(default=Decimal('10.0'), ge=0, le=100)
    payout_method: PayoutMethodEnum
    notes: Optional[str] = Field(None, max_length=1000)


class BulkPayoutResponse(BaseModel):
    created_payouts: List[PayoutOut]
    failed_events: List[Dict[str, Any]] = Field(
        description="Events that failed payout creation with reasons"
    )
    total_created: int
    total_failed: int
