"""
Migration to fix the case mismatch between database enum and Python enum.

SQLAlchemy expects the enum names (WORKSHOP) but the database has enum values (workshop).
This migration updates the database to use uppercase enum values to match SQLAlchemy expectations.
"""

from sqlalchemy import text
from config.session import engine


def fix_enum_case_mismatch():
    """
    Execute the migration to fix enum case mismatch.
    """
    
    with engine.connect() as connection:
        # Start transaction
        trans = connection.begin()
        
        try:
            print("Starting migration to fix enum case mismatch...")
            
            # Check current enum values
            print("Checking current enum values...")
            current_values = connection.execute(text("""
                SELECT enumlabel 
                FROM pg_enum e
                JOIN pg_type t ON e.enumtypid = t.oid
                WHERE t.typname = 'eventcategoryenum'
                ORDER BY e.enumsortorder;
            """)).fetchall()
            
            current_enum_values = [row[0] for row in current_values]
            print(f"Current enum values: {current_enum_values}")
            
            # Check if there are any events using the enum
            events_count = connection.execute(text("""
                SELECT COUNT(*) FROM events WHERE category IS NOT NULL;
            """)).scalar()
            
            print(f"Found {events_count} events with category values.")
            
            # Convert column to text temporarily
            print("Converting category column to text...")
            connection.execute(text("""
                ALTER TABLE events ALTER COLUMN category TYPE TEXT;
            """))
            
            # Update existing data to uppercase
            print("Updating existing data to uppercase...")
            connection.execute(text("""
                UPDATE events SET category = 'WORKSHOP' WHERE category = 'workshop';
            """))
            connection.execute(text("""
                UPDATE events SET category = 'CONFERENCE' WHERE category = 'conference';
            """))
            connection.execute(text("""
                UPDATE events SET category = 'WEBINAR' WHERE category = 'webinar';
            """))
            connection.execute(text("""
                UPDATE events SET category = 'COMPETITION' WHERE category = 'competition';
            """))
            
            # Drop the old enum type
            print("Dropping old enum type...")
            connection.execute(text("""
                DROP TYPE IF EXISTS eventcategoryenum CASCADE;
            """))
            
            # Create new enum type with uppercase values (matching Python enum names)
            print("Creating new enum type with uppercase values...")
            connection.execute(text("""
                CREATE TYPE eventcategoryenum AS ENUM ('WORKSHOP', 'CONFERENCE', 'WEBINAR', 'COMPETITION');
            """))
            
            # Update the events table to use the new enum
            print("Updating events table to use new enum...")
            connection.execute(text("""
                ALTER TABLE events 
                ALTER COLUMN category TYPE eventcategoryenum 
                USING category::eventcategoryenum;
            """))
            
            # Make sure the column is NOT NULL
            connection.execute(text("""
                ALTER TABLE events 
                ALTER COLUMN category SET NOT NULL;
            """))
            
            # Verify the fix
            print("Verifying the fix...")
            new_values = connection.execute(text("""
                SELECT enumlabel 
                FROM pg_enum e
                JOIN pg_type t ON e.enumtypid = t.oid
                WHERE t.typname = 'eventcategoryenum'
                ORDER BY e.enumsortorder;
            """)).fetchall()
            
            new_enum_values = [row[0] for row in new_values]
            print(f"New enum values: {new_enum_values}")
            
            # Check updated data
            if events_count > 0:
                updated_data = connection.execute(text("""
                    SELECT category, COUNT(*) 
                    FROM events 
                    GROUP BY category;
                """)).fetchall()
                print("Updated event categories:")
                for row in updated_data:
                    print(f"  {row[0]}: {row[1]} events")
            
            # Commit transaction
            trans.commit()
            print("Migration completed successfully!")
            
        except Exception as e:
            # Rollback on error
            trans.rollback()
            print(f"Migration failed: {e}")
            raise


if __name__ == "__main__":
    fix_enum_case_mismatch()
