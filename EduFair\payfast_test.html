<!DOCTYPE html>
<html>
<head>
    <title>PayFast Test Payment</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        .info { background: #e3f2fd; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .form { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; }
        button { background: #4CAF50; color: white; padding: 15px 32px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; margin: 10px; }
        button:hover { background: #45a049; }
        .auto-submit { background: #ff9800; }
        .test-card { background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>PayFast Test Payment</h1>
        <p>EduFair Payment Integration Test</p>
        
        <div class="info">
            <h3>Test Payment Details:</h3>
            <p><strong>Amount:</strong> R 100.00</p>
            <p><strong>Item:</strong> EduFair Test Payment</p>
            <p><strong>Mode:</strong> Sandbox (No real money)</p>
            <p><strong>Merchant ID:</strong> 10000100</p>
        </div>
        
        <div class="test-card">
            <h4>Test Card Details (PayFast Sandbox):</h4>
            <p><strong>Card Number:</strong> ****************</p>
            <p><strong>Expiry:</strong> Any future date (e.g., 12/25)</p>
            <p><strong>CVV:</strong> Any 3 digits (e.g., 123)</p>
            <p><strong>Name:</strong> Any name</p>
        </div>
        
        <div class="form">
            <form action="https://sandbox.payfast.co.za/eng/process" method="post" id="payfast_form">
                <input type="hidden" name="merchant_id" value="10000100">
                <input type="hidden" name="merchant_key" value="46f0cd694581a">
                <input type="hidden" name="return_url" value="http://localhost:3000/payment/success">
                <input type="hidden" name="cancel_url" value="http://localhost:3000/payment/cancel">
                <input type="hidden" name="notify_url" value="http://localhost:8000/api/payments/payfast/webhook">
                <input type="hidden" name="name_first" value="Test">
                <input type="hidden" name="name_last" value="User">
                <input type="hidden" name="email_address" value="<EMAIL>">
                <input type="hidden" name="m_payment_id" value="cefc270b-8ada-40f8-a945-56195ec34e28">
                <input type="hidden" name="amount" value="100.00">
                <input type="hidden" name="item_name" value="EduFair Test Payment">
                <input type="hidden" name="item_description" value="Test payment for EduFair PayFast integration">
                <input type="hidden" name="custom_str1" value="test_payment_123">
                <input type="hidden" name="custom_str2" value="simple_test">
                <input type="hidden" name="custom_str3" value="endpoint_test">
                <input type="hidden" name="signature" value="b711a821e2f0a3c19abb19f17b03632f">

                <button type="submit">Pay with PayFast (Sandbox)</button>
                <br>
                <button type="button" onclick="autoSubmit()" class="auto-submit">
                    Auto-Submit in 3 seconds
                </button>
            </form>
        </div>
        
        <div style="margin-top: 20px; font-size: 14px;">
            <h4>Expected Flow:</h4>
            <ol>
                <li>Click "Pay with PayFast" → Redirects to PayFast sandbox</li>
                <li>Enter test card details → Complete payment</li>
                <li>PayFast redirects to success/cancel page</li>
                <li>PayFast sends webhook notification</li>
            </ol>
            
            <h4>URLs:</h4>
            <p><strong>Payment URL:</strong> https://sandbox.payfast.co.za/eng/process</p>
            <p><strong>Webhook:</strong> http://localhost:8000/api/payments/payfast/webhook</p>
            <p><strong>Success:</strong> http://localhost:3000/payment/success</p>
            <p><strong>Cancel:</strong> http://localhost:3000/payment/cancel</p>
        </div>
    </div>
    
    <script>
        function autoSubmit() {
            let countdown = 3;
            const button = event.target;
            
            const timer = setInterval(() => {
                button.innerHTML = `Auto-submitting in ${countdown}...`;
                countdown--;
                
                if (countdown < 0) {
                    clearInterval(timer);
                    document.getElementById('payfast_form').submit();
                }
            }, 1000);
        }
    </script>
</body>
</html>