#!/usr/bin/env python3
"""
Run event system migration using SQLAlchemy
"""
import sys
import os

# Add the EduFair app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'EduFair', 'app'))

try:
    from sqlalchemy import text
    from config.session import engine
    
    def run_migration():
        """Run the event system migration"""
        print("🚀 Running Event System Migration")
        print("=" * 50)
        
        # Read the migration SQL file
        migration_file = "event_system_migration.sql"
        
        try:
            with open(migration_file, 'r') as f:
                migration_sql = f.read()
            
            print(f"✅ Read migration file: {migration_file}")
            
            # Execute the migration
            with engine.connect() as connection:
                # Split the SQL into individual statements
                statements = migration_sql.split(';')
                
                for i, statement in enumerate(statements):
                    statement = statement.strip()
                    if statement and not statement.startswith('--'):
                        try:
                            print(f"   Executing statement {i+1}/{len(statements)}...")
                            connection.execute(text(statement))
                            connection.commit()
                        except Exception as e:
                            if "already exists" in str(e).lower() or "duplicate" in str(e).lower():
                                print(f"   ⚠️  Statement {i+1} - Object already exists (skipping): {str(e)[:100]}...")
                            else:
                                print(f"   ❌ Error in statement {i+1}: {e}")
                                raise
                
                print("✅ Migration completed successfully!")
                
        except FileNotFoundError:
            print(f"❌ Migration file not found: {migration_file}")
            return False
        except Exception as e:
            print(f"❌ Migration failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        return True
    
    def verify_migration():
        """Verify the migration was successful"""
        print("\n🔍 Verifying Migration...")
        
        verification_queries = [
            ("Event Types Table", "SELECT COUNT(*) FROM event_types"),
            ("Events with Event Type", "SELECT COUNT(*) FROM events WHERE event_type IS NOT NULL"),
            ("Competition Details Table", "SELECT COUNT(*) FROM competition_details"),
            ("Users with Mentor Capabilities", "SELECT COUNT(*) FROM users WHERE can_act_as_mentor IS NOT NULL"),
            ("Competition Exams", "SELECT COUNT(*) FROM exams WHERE is_competition_exam IS NOT NULL"),
        ]
        
        try:
            with engine.connect() as connection:
                for description, query in verification_queries:
                    try:
                        result = connection.execute(text(query)).scalar()
                        print(f"   ✅ {description}: {result} records")
                    except Exception as e:
                        print(f"   ❌ {description}: Error - {e}")
                        
        except Exception as e:
            print(f"❌ Verification failed: {e}")
            return False
        
        return True
    
    if __name__ == "__main__":
        success = run_migration()
        if success:
            verify_migration()
            print("\n🎉 Event System Migration Complete!")
            print("\nNext steps:")
            print("1. Run: python test_event_system_migration.py")
            print("2. Test event creation with new types")
            print("3. Test competition functionality")
        else:
            print("\n❌ Migration failed. Please check the errors above.")
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running this from the correct directory")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
