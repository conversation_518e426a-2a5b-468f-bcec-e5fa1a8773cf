"""
Institute Event Control Routes for EduFair Platform

This module contains API routes for institute event management including:
- Registration management and control
- Refund and cancellation processing
- Event analytics and reporting
- Bulk operations on registrations
"""

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID

# Import dependencies
from config.session import get_db
from config.deps import oauth2_scheme, get_current_user
from config.permission import require_type

# Import CRUD operations
from Cruds.Institute.EventControl import (
    get_institute_event_registrations, control_registrations,
    get_institute_event_analytics, cancel_institute_event,
    transfer_ticket, get_refund_summary
)

# Import schemas
from Schemas.Institute.EventControl import (
    InstituteEventRegistrationsResponse, InstituteRegistrationControl,
    BulkRegistrationControlResponse, InstituteEventAnalytics,
    EventCancellationRequest, EventCancellationResponse,
    TicketTransferRequest, TicketTransferResponse,
    RegistrationAction, RefundType
)
from Models.Events import RegistrationStatusEnum

router = APIRouter()


# ==================== EVENT REGISTRATION MANAGEMENT ====================

@router.get("/events/{event_id}/registrations", response_model=InstituteEventRegistrationsResponse)
def get_event_registrations(
    event_id: UUID,
    skip: int = Query(0, ge=0, description="Number of registrations to skip"),
    limit: int = Query(50, ge=1, le=200, description="Number of registrations to return"),
    status_filter: Optional[List[RegistrationStatusEnum]] = Query(None, description="Filter by registration status"),
    search: Optional[str] = Query(None, description="Search by user name, email, or registration number"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Get all registrations for an institute's event.
    
    Provides comprehensive registration management with filtering and search capabilities.
    Includes payment status, user details, and available actions for each registration.
    """
    current_user = get_current_user(token, db)
    
    return get_institute_event_registrations(
        db, event_id, current_user.id, skip, limit, status_filter, search
    )


@router.post("/events/{event_id}/registrations/control", response_model=BulkRegistrationControlResponse)
def control_event_registrations(
    event_id: UUID,
    control_request: InstituteRegistrationControl,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Perform bulk actions on event registrations.
    
    Supported actions:
    - APPROVE: Approve pending registrations
    - REJECT: Reject pending registrations
    - CANCEL: Cancel confirmed registrations
    - REFUND: Process refunds (full or partial)
    - MARK_ATTENDED: Mark registrations as attended
    - TRANSFER_TICKET: Transfer tickets to other users
    - UPDATE_INFO: Update registration information
    - SEND_REMINDER: Send reminder notifications
    """
    current_user = get_current_user(token, db)
    
    return control_registrations(
        db, event_id, current_user.id, control_request, current_user.username
    )


@router.post("/events/{event_id}/registrations/{registration_id}/approve")
def approve_registration(
    event_id: UUID,
    registration_id: UUID,
    send_notification: bool = Query(True, description="Send approval notification to user"),
    custom_message: Optional[str] = Query(None, description="Custom message for notification"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Approve a single registration.
    """
    current_user = get_current_user(token, db)
    
    control_request = InstituteRegistrationControl(
        registration_ids=[registration_id],
        action=RegistrationAction.APPROVE,
        send_notification=send_notification,
        custom_message=custom_message
    )
    
    result = control_registrations(db, event_id, current_user.id, control_request, current_user.username)
    
    if result.successful_actions == 1:
        return {"message": "Registration approved successfully", "result": result.results[0]}
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result.results[0].message if result.results else "Approval failed"
        )


@router.post("/events/{event_id}/registrations/{registration_id}/refund")
def refund_registration(
    event_id: UUID,
    registration_id: UUID,
    refund_type: RefundType = RefundType.FULL,
    refund_amount: Optional[float] = Query(None, description="Custom refund amount for partial refunds"),
    reason: Optional[str] = Query(None, description="Reason for refund"),
    send_notification: bool = Query(True, description="Send refund notification to user"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Process refund for a single registration.
    """
    current_user = get_current_user(token, db)
    
    from decimal import Decimal
    
    control_request = InstituteRegistrationControl(
        registration_ids=[registration_id],
        action=RegistrationAction.REFUND,
        refund_type=refund_type,
        refund_amount=Decimal(str(refund_amount)) if refund_amount else None,
        reason=reason,
        send_notification=send_notification
    )
    
    result = control_registrations(db, event_id, current_user.id, control_request, current_user.username)
    
    if result.successful_actions == 1:
        return {
            "message": "Refund processed successfully", 
            "refund_amount": result.total_refund_amount,
            "result": result.results[0]
        }
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result.results[0].message if result.results else "Refund failed"
        )


@router.post("/events/{event_id}/registrations/{registration_id}/mark-attended")
def mark_registration_attended(
    event_id: UUID,
    registration_id: UUID,
    send_notification: bool = Query(True, description="Send attendance confirmation to user"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Mark a registration as attended.
    """
    current_user = get_current_user(token, db)
    
    control_request = InstituteRegistrationControl(
        registration_ids=[registration_id],
        action=RegistrationAction.MARK_ATTENDED,
        send_notification=send_notification
    )
    
    result = control_registrations(db, event_id, current_user.id, control_request, current_user.username)
    
    if result.successful_actions == 1:
        return {"message": "Registration marked as attended", "result": result.results[0]}
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result.results[0].message if result.results else "Failed to mark as attended"
        )


# ==================== EVENT ANALYTICS ====================

@router.get("/events/{event_id}/analytics", response_model=InstituteEventAnalytics)
def get_event_analytics(
    event_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Get comprehensive analytics for an event.
    
    Includes registration metrics, financial data, attendance rates,
    and demographic breakdowns.
    """
    current_user = get_current_user(token, db)
    
    return get_institute_event_analytics(db, event_id, current_user.id)


# ==================== EVENT CANCELLATION ====================

@router.post("/events/{event_id}/cancel", response_model=EventCancellationResponse)
def cancel_event(
    event_id: UUID,
    cancellation_request: EventCancellationRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Cancel an event and process refunds for attendees.
    
    Supports full or partial refunds, rescheduling options,
    and automatic notification to all registered users.
    """
    current_user = get_current_user(token, db)
    
    return cancel_institute_event(
        db, event_id, current_user.id, cancellation_request, current_user.username
    )


# ==================== BULK OPERATIONS ====================

@router.post("/events/{event_id}/registrations/bulk-approve")
def bulk_approve_registrations(
    event_id: UUID,
    registration_ids: List[UUID],
    send_notifications: bool = Query(True, description="Send approval notifications"),
    custom_message: Optional[str] = Query(None, description="Custom message for notifications"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Approve multiple registrations at once.
    """
    current_user = get_current_user(token, db)
    
    control_request = InstituteRegistrationControl(
        registration_ids=registration_ids,
        action=RegistrationAction.APPROVE,
        send_notification=send_notifications,
        custom_message=custom_message
    )
    
    return control_registrations(db, event_id, current_user.id, control_request, current_user.username)


@router.post("/events/{event_id}/registrations/bulk-refund")
def bulk_refund_registrations(
    event_id: UUID,
    registration_ids: List[UUID],
    refund_type: RefundType = RefundType.FULL,
    refund_percentage: Optional[float] = Query(None, ge=0, le=100, description="Refund percentage for partial refunds"),
    reason: Optional[str] = Query(None, description="Reason for bulk refund"),
    send_notifications: bool = Query(True, description="Send refund notifications"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Process refunds for multiple registrations at once.
    """
    current_user = get_current_user(token, db)
    
    from decimal import Decimal
    
    control_request = InstituteRegistrationControl(
        registration_ids=registration_ids,
        action=RegistrationAction.REFUND,
        refund_type=refund_type,
        reason=reason,
        send_notification=send_notifications
    )
    
    return control_registrations(db, event_id, current_user.id, control_request, current_user.username)


@router.post("/events/{event_id}/registrations/bulk-cancel")
def bulk_cancel_registrations(
    event_id: UUID,
    registration_ids: List[UUID],
    reason: Optional[str] = Query(None, description="Reason for cancellation"),
    send_notifications: bool = Query(True, description="Send cancellation notifications"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Cancel multiple registrations at once.
    """
    current_user = get_current_user(token, db)
    
    control_request = InstituteRegistrationControl(
        registration_ids=registration_ids,
        action=RegistrationAction.CANCEL,
        reason=reason,
        send_notification=send_notifications
    )
    
    return control_registrations(db, event_id, current_user.id, control_request, current_user.username)


# ==================== TICKET TRANSFER ====================

@router.post("/events/{event_id}/registrations/{registration_id}/transfer", response_model=TicketTransferResponse)
def transfer_registration_ticket(
    event_id: UUID,
    registration_id: UUID,
    transfer_request: TicketTransferRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Transfer a ticket to another user.

    Cancels the original registration and creates a new one for the target user.
    Supports notification to both original and new users.
    """
    current_user = get_current_user(token, db)

    return transfer_ticket(db, registration_id, current_user.id, transfer_request, current_user.username)


# ==================== REFUND MANAGEMENT ====================

@router.get("/events/{event_id}/refunds/summary")
def get_event_refund_summary(
    event_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Get comprehensive refund summary for an event.

    Includes total refunds, amounts, reasons, and timeline breakdown.
    """
    current_user = get_current_user(token, db)

    return get_refund_summary(db, event_id, current_user.id)
