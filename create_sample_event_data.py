#!/usr/bin/env python3
"""
Create sample event data for testing the new event system
"""
import sys
import os
from datetime import datetime, timezone, timedelta

# Add the EduFair app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'EduFair', 'app'))

try:
    from sqlalchemy.orm import Session
    from config.session import get_db
    from Models.Events import EventType, Event, EventTypeEnum
    from Models.users import User, UserTypeEnum
    from Models.Exam import Exam
    from Models.Competitions import CompetitionMentorAssignment
    import uuid
    
    def create_sample_data():
        """Create sample event data for testing"""
        print("🎯 Creating Sample Event Data")
        print("=" * 50)
        
        # Get database session
        db = next(get_db())
        
        try:
            # Find an institute user
            institute = db.query(User).filter(User.user_type == UserTypeEnum.institute).first()
            if not institute:
                print("❌ No institute user found. Please create one first.")
                return False
            
            print(f"✅ Found institute: {institute.username}")
            
            # Enable teacher-as-mentor capability for existing teachers
            print("\n1. Enabling teacher-as-mentor capabilities...")
            teachers = db.query(User).filter(User.user_type == UserTypeEnum.teacher).all()
            
            for teacher in teachers:
                if not teacher.can_act_as_mentor:
                    teacher.can_act_as_mentor = True
                    teacher.mentor_specializations = ["Mathematics", "Science", "Programming"]
                    teacher.judging_experience_years = 2
                    print(f"   ✅ Enabled mentor capabilities for teacher: {teacher.username}")
            
            db.commit()
            
            # Create sample events of different types
            print("\n2. Creating sample events...")
            
            # First, we need to get a category and location
            # For now, let's create events without these required fields by using existing ones or creating minimal ones

            # Find existing category or create a default one
            from Models.Events import EventCategory
            category = db.query(EventCategory).first()
            if not category:
                category = EventCategory(name="Technology", description="Technology related events")
                db.add(category)
                db.commit()
                db.refresh(category)

            # Workshop event
            workshop_event = Event(
                title="Python Programming Workshop",
                description="Learn Python programming from basics to advanced",
                event_type=EventTypeEnum.WORKSHOP,
                start_datetime=datetime.now(timezone.utc) + timedelta(days=7),
                end_datetime=datetime.now(timezone.utc) + timedelta(days=7, hours=4),
                organizer_id=institute.id,
                institute_id=institute.id,
                category_id=category.id,
                requires_collaboration=False,
                is_competition=False,
                max_attendees=50
            )
            
            # Conference event
            conference_event = Event(
                title="Tech Innovation Conference 2024",
                description="Annual conference on technology innovations",
                event_type=EventTypeEnum.CONFERENCE,
                start_datetime=datetime.now(timezone.utc) + timedelta(days=30),
                end_datetime=datetime.now(timezone.utc) + timedelta(days=30, hours=8),
                organizer_id=institute.id,
                institute_id=institute.id,
                category_id=category.id,
                requires_collaboration=False,
                is_competition=False,
                max_attendees=500
            )

            # Webinar event
            webinar_event = Event(
                title="AI in Education Webinar",
                description="Online session about AI applications in education",
                event_type=EventTypeEnum.WEBINAR,
                start_datetime=datetime.now(timezone.utc) + timedelta(days=14),
                end_datetime=datetime.now(timezone.utc) + timedelta(days=14, hours=2),
                organizer_id=institute.id,
                institute_id=institute.id,
                category_id=category.id,
                requires_collaboration=False,
                is_competition=False,
                max_attendees=1000
            )
            
            # Add events to database
            db.add(workshop_event)
            db.add(conference_event)
            db.add(webinar_event)
            db.commit()
            
            print(f"   ✅ Created workshop event: {workshop_event.title}")
            print(f"   ✅ Created conference event: {conference_event.title}")
            print(f"   ✅ Created webinar event: {webinar_event.title}")
            
            # Create a sample exam for competition
            print("\n3. Creating sample exam for competition...")
            
            sample_exam = Exam(
                title="Programming Competition Exam",
                description="Test your programming skills",
                total_marks=100,
                total_duration=180,  # 3 hours
                start_time=datetime.now(timezone.utc) + timedelta(days=21),
                is_competition_exam=True,
                auto_grading_enabled=True,
                manual_review_required=True,
                competition_specific_settings={
                    "programming_languages": ["Python", "Java", "C++"],
                    "difficulty_level": "intermediate",
                    "topics": ["algorithms", "data_structures", "problem_solving"]
                }
            )
            
            db.add(sample_exam)
            db.commit()
            db.refresh(sample_exam)
            
            print(f"   ✅ Created competition exam: {sample_exam.title}")
            
            # Create competition event
            print("\n4. Creating competition event...")
            
            competition_event = Event(
                title="Annual Programming Competition",
                description="Test your programming skills against other students",
                event_type=EventTypeEnum.COMPETITION,
                start_datetime=datetime.now(timezone.utc) + timedelta(days=21),
                end_datetime=datetime.now(timezone.utc) + timedelta(days=21, hours=3),
                organizer_id=institute.id,
                institute_id=institute.id,
                category_id=category.id,
                requires_collaboration=True,
                is_competition=True,
                competition_exam_id=sample_exam.id,
                max_attendees=100
            )
            
            db.add(competition_event)
            db.commit()
            db.refresh(competition_event)
            
            print(f"   ✅ Created competition event: {competition_event.title}")
            
            # Create mentor assignments for the competition
            print("\n5. Creating mentor assignments...")
            
            # Find mentors and teachers who can act as mentors
            mentors = db.query(User).filter(User.user_type == UserTypeEnum.mentor).all()
            teachers_as_mentors = db.query(User).filter(
                User.user_type == UserTypeEnum.teacher,
                User.can_act_as_mentor == True
            ).all()
            
            all_judges = mentors + teachers_as_mentors
            
            if all_judges:
                for i, judge in enumerate(all_judges[:3]):  # Assign up to 3 judges
                    assignment = CompetitionMentorAssignment(
                        competition_id=competition_event.id,
                        mentor_id=judge.id,
                        assigned_by=institute.id,
                        institute_id=institute.id,
                        assignment_type="manual",
                        collaboration_verified=True,
                        workload_capacity=20,
                        current_workload=0,
                        specialization_match_score=0.85 + (i * 0.05),  # Vary scores
                        estimated_hours=10.0,
                        hourly_rate=50.0,
                        total_compensation=500.0,
                        assignment_notes=f"Assigned to judge programming competition - Judge #{i+1}"
                    )
                    
                    db.add(assignment)
                    print(f"   ✅ Assigned judge: {judge.username} ({judge.user_type.value})")
                
                db.commit()
            else:
                print("   ⚠️  No mentors or teachers available for assignment")
            
            # Summary
            print(f"\n🎉 Sample Data Creation Complete!")
            print(f"   ✅ Created 4 events (workshop, conference, webinar, competition)")
            print(f"   ✅ Created 1 competition exam")
            print(f"   ✅ Enabled mentor capabilities for {len(teachers)} teachers")
            print(f"   ✅ Created {len(all_judges[:3])} mentor assignments")
            
            return True
            
        except Exception as e:
            print(f"❌ Error creating sample data: {e}")
            import traceback
            traceback.print_exc()
            db.rollback()
            return False
        finally:
            db.close()
    
    def verify_sample_data():
        """Verify the sample data was created correctly"""
        print("\n🔍 Verifying Sample Data...")
        
        db = next(get_db())
        
        try:
            # Check events by type
            for event_type in EventTypeEnum:
                count = db.query(Event).filter(Event.event_type == event_type).count()
                print(f"   ✅ {event_type.value.title()} events: {count}")
            
            # Check competition exams
            comp_exams = db.query(Exam).filter(Exam.is_competition_exam == True).count()
            print(f"   ✅ Competition exams: {comp_exams}")
            
            # Check mentor assignments
            assignments = db.query(CompetitionMentorAssignment).count()
            print(f"   ✅ Mentor assignments: {assignments}")
            
            # Check teachers with mentor capabilities
            teacher_mentors = db.query(User).filter(
                User.user_type == UserTypeEnum.teacher,
                User.can_act_as_mentor == True
            ).count()
            print(f"   ✅ Teachers with mentor capabilities: {teacher_mentors}")
            
        except Exception as e:
            print(f"❌ Verification failed: {e}")
        finally:
            db.close()
    
    if __name__ == "__main__":
        success = create_sample_data()
        if success:
            verify_sample_data()
            print("\n🚀 Ready to test the enhanced event system!")
            print("\nNext steps:")
            print("1. Test event creation APIs")
            print("2. Test competition workflow")
            print("3. Test mentor assignment system")
            print("4. Test teacher-as-mentor functionality")
        else:
            print("\n❌ Sample data creation failed.")
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running this from the correct directory")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
