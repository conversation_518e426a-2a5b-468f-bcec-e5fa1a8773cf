"""
Certification Schemas for Competition System
"""
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from uuid import UUID
from datetime import datetime
from enum import Enum

# Import enums from models
from Models.Certifications import (
    CertificationTypeEnum, CertificationStatusEnum, PerformanceTierEnum
)


class CertificationTemplateBase(BaseModel):
    """Base schema for certification templates"""
    name: str = Field(..., max_length=200, description="Template name")
    description: Optional[str] = Field(None, description="Template description")
    certification_type: CertificationTypeEnum = Field(..., description="Type of certification")
    template_design: Optional[Dict[str, Any]] = Field(None, description="Design configuration")
    certificate_text: Optional[str] = Field(None, description="Certificate content template")
    logo_url: Optional[str] = Field(None, max_length=500, description="Logo URL")
    background_image_url: Optional[str] = Field(None, max_length=500, description="Background image URL")
    
    # Criteria
    min_score_percentage: Optional[float] = Field(None, ge=0, le=100, description="Minimum score percentage")
    max_score_percentage: Optional[float] = Field(None, ge=0, le=100, description="Maximum score percentage")
    min_percentile: Optional[float] = Field(None, ge=0, le=100, description="Minimum percentile")
    max_percentile: Optional[float] = Field(None, ge=0, le=100, description="Maximum percentile")
    required_performance_tier: Optional[PerformanceTierEnum] = Field(None, description="Required performance tier")
    
    # Settings
    min_participants_required: int = Field(1, ge=1, description="Minimum participants required")
    requires_mentor_approval: bool = Field(True, description="Requires mentor approval")
    auto_award: bool = Field(False, description="Automatically award when criteria met")
    is_active: bool = Field(True, description="Template is active")
    is_default: bool = Field(False, description="Default template for this type")


class CertificationTemplateCreate(CertificationTemplateBase):
    """Schema for creating certification templates"""
    institute_id: Optional[UUID] = Field(None, description="Institute ID (auto-filled)")


class CertificationTemplateUpdate(BaseModel):
    """Schema for updating certification templates"""
    name: Optional[str] = Field(None, max_length=200)
    description: Optional[str] = None
    template_design: Optional[Dict[str, Any]] = None
    certificate_text: Optional[str] = None
    logo_url: Optional[str] = Field(None, max_length=500)
    background_image_url: Optional[str] = Field(None, max_length=500)
    min_score_percentage: Optional[float] = Field(None, ge=0, le=100)
    max_score_percentage: Optional[float] = Field(None, ge=0, le=100)
    min_percentile: Optional[float] = Field(None, ge=0, le=100)
    max_percentile: Optional[float] = Field(None, ge=0, le=100)
    required_performance_tier: Optional[PerformanceTierEnum] = None
    min_participants_required: Optional[int] = Field(None, ge=1)
    requires_mentor_approval: Optional[bool] = None
    auto_award: Optional[bool] = None
    is_active: Optional[bool] = None
    is_default: Optional[bool] = None


class CertificationTemplateOut(CertificationTemplateBase):
    """Schema for certification template output"""
    id: UUID
    institute_id: UUID
    created_at: datetime
    updated_at: datetime
    
    # Related data
    institute: Optional[Dict[str, Any]] = None

    class Config:
        from_attributes = True


class CompetitionCertificationBase(BaseModel):
    """Base schema for competition certifications"""
    certification_type: CertificationTypeEnum = Field(..., description="Type of certification")
    certificate_title: str = Field(..., max_length=300, description="Certificate title")
    certificate_description: Optional[str] = Field(None, description="Certificate description")
    achievement_details: Optional[Dict[str, Any]] = Field(None, description="Specific achievements")
    mentor_comments: Optional[str] = Field(None, description="Mentor comments")
    mentor_rating: Optional[float] = Field(None, ge=1, le=5, description="Mentor rating (1-5)")


class CompetitionCertificationCreate(CompetitionCertificationBase):
    """Schema for creating competition certifications"""
    competition_id: UUID = Field(..., description="Competition ID")
    participant_id: UUID = Field(..., description="Participant ID")
    template_id: UUID = Field(..., description="Template ID")
    final_score: float = Field(..., ge=0, description="Final score")
    score_percentage: float = Field(..., ge=0, le=100, description="Score percentage")
    percentile_rank: float = Field(..., ge=0, le=100, description="Percentile rank")
    performance_tier: PerformanceTierEnum = Field(..., description="Performance tier")
    rank_position: int = Field(..., ge=1, description="Rank position")
    total_participants: int = Field(..., ge=1, description="Total participants")


class CompetitionCertificationUpdate(BaseModel):
    """Schema for updating competition certifications"""
    status: Optional[CertificationStatusEnum] = None
    certificate_title: Optional[str] = Field(None, max_length=300)
    certificate_description: Optional[str] = None
    achievement_details: Optional[Dict[str, Any]] = None
    mentor_comments: Optional[str] = None
    mentor_rating: Optional[float] = Field(None, ge=1, le=5)
    approval_notes: Optional[str] = None


class CompetitionCertificationOut(CompetitionCertificationBase):
    """Schema for competition certification output"""
    id: UUID
    competition_id: UUID
    participant_id: UUID
    template_id: UUID
    status: CertificationStatusEnum
    
    # Performance metrics
    final_score: float
    score_percentage: float
    percentile_rank: float
    performance_tier: PerformanceTierEnum
    rank_position: int
    total_participants: int
    
    # Workflow dates
    evaluation_date: Optional[datetime] = None
    approval_date: Optional[datetime] = None
    issued_date: Optional[datetime] = None
    expiry_date: Optional[datetime] = None
    
    # Certificate details
    certificate_url: Optional[str] = None
    verification_code: Optional[str] = None
    verification_url: Optional[str] = None
    is_verified: bool
    
    # Related data
    competition: Optional[Dict[str, Any]] = None
    participant: Optional[Dict[str, Any]] = None
    template: Optional[Dict[str, Any]] = None
    evaluator: Optional[Dict[str, Any]] = None
    approver: Optional[Dict[str, Any]] = None
    
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class CertificationCriteriaBase(BaseModel):
    """Base schema for certification criteria"""
    certification_type: CertificationTypeEnum = Field(..., description="Certification type")
    min_score: Optional[float] = Field(None, ge=0, description="Minimum score")
    min_percentage: Optional[float] = Field(None, ge=0, le=100, description="Minimum percentage")
    max_rank: Optional[int] = Field(None, ge=1, description="Maximum rank position")
    min_percentile: Optional[float] = Field(None, ge=0, le=100, description="Minimum percentile")
    required_tier: Optional[PerformanceTierEnum] = Field(None, description="Required performance tier")
    
    # Additional requirements
    min_completion_time: Optional[int] = Field(None, ge=0, description="Minimum completion time (minutes)")
    max_completion_time: Optional[int] = Field(None, ge=0, description="Maximum completion time (minutes)")
    requires_all_questions_attempted: bool = Field(False, description="All questions must be attempted")
    min_correct_answers: Optional[int] = Field(None, ge=0, description="Minimum correct answers")
    
    # Mentor requirements
    requires_mentor_approval: bool = Field(True, description="Requires mentor approval")
    min_mentor_rating: Optional[float] = Field(None, ge=1, le=5, description="Minimum mentor rating")
    
    # Award limits
    max_awards: Optional[int] = Field(None, ge=1, description="Maximum number of awards")
    award_percentage: Optional[float] = Field(None, ge=0, le=100, description="Percentage of participants to award")
    
    # Settings
    is_active: bool = Field(True, description="Criteria is active")
    auto_award: bool = Field(False, description="Automatically award")


class CertificationCriteriaCreate(CertificationCriteriaBase):
    """Schema for creating certification criteria"""
    competition_id: UUID = Field(..., description="Competition ID")
    template_id: UUID = Field(..., description="Template ID")


class CertificationCriteriaUpdate(BaseModel):
    """Schema for updating certification criteria"""
    min_score: Optional[float] = Field(None, ge=0)
    min_percentage: Optional[float] = Field(None, ge=0, le=100)
    max_rank: Optional[int] = Field(None, ge=1)
    min_percentile: Optional[float] = Field(None, ge=0, le=100)
    required_tier: Optional[PerformanceTierEnum] = None
    min_completion_time: Optional[int] = Field(None, ge=0)
    max_completion_time: Optional[int] = Field(None, ge=0)
    requires_all_questions_attempted: Optional[bool] = None
    min_correct_answers: Optional[int] = Field(None, ge=0)
    requires_mentor_approval: Optional[bool] = None
    min_mentor_rating: Optional[float] = Field(None, ge=1, le=5)
    max_awards: Optional[int] = Field(None, ge=1)
    award_percentage: Optional[float] = Field(None, ge=0, le=100)
    is_active: Optional[bool] = None
    auto_award: Optional[bool] = None


class CertificationCriteriaOut(CertificationCriteriaBase):
    """Schema for certification criteria output"""
    id: UUID
    competition_id: UUID
    template_id: UUID
    created_at: datetime
    updated_at: datetime
    
    # Related data
    competition: Optional[Dict[str, Any]] = None
    template: Optional[Dict[str, Any]] = None

    class Config:
        from_attributes = True


class MentorEvaluationRequest(BaseModel):
    """Schema for mentor evaluation of competition submissions"""
    certification_id: UUID = Field(..., description="Certification ID")
    mentor_rating: float = Field(..., ge=1, le=5, description="Mentor rating (1-5)")
    mentor_comments: str = Field(..., description="Mentor evaluation comments")
    recommendation: str = Field(..., description="approve, reject, or request_changes")
    evaluation_details: Optional[Dict[str, Any]] = Field(None, description="Detailed evaluation")


class CertificationApprovalRequest(BaseModel):
    """Schema for certification approval"""
    certification_id: UUID = Field(..., description="Certification ID")
    action: str = Field(..., description="approve, reject, or request_changes")
    approval_notes: Optional[str] = Field(None, description="Approval notes")


class CertificationBatchRequest(BaseModel):
    """Schema for batch certification operations"""
    competition_id: UUID = Field(..., description="Competition ID")
    participant_ids: Optional[List[UUID]] = Field(None, description="Specific participants (optional)")
    certification_type: Optional[CertificationTypeEnum] = Field(None, description="Specific certification type")
    auto_evaluate: bool = Field(True, description="Automatically evaluate based on criteria")
    auto_approve: bool = Field(False, description="Automatically approve (admin only)")


class CertificationAnalytics(BaseModel):
    """Schema for certification analytics"""
    competition_id: UUID
    total_participants: int
    total_certifications: int
    certifications_by_type: Dict[str, int]
    certifications_by_tier: Dict[str, int]
    average_score: float
    average_percentile: float
    mentor_evaluation_rate: float
    approval_rate: float
    issuance_rate: float
    
    # Performance distribution
    score_distribution: Dict[str, int]
    tier_distribution: Dict[str, int]
    
    # Timeline
    evaluation_timeline: List[Dict[str, Any]]
    approval_timeline: List[Dict[str, Any]]


class CertificationListResponse(BaseModel):
    """Response schema for certification lists"""
    certifications: List[CompetitionCertificationOut]
    total: int
    pending_evaluation: int
    pending_approval: int
    issued: int
    page: int = 1
    per_page: int = 20


class CertificationVerificationResponse(BaseModel):
    """Response schema for certificate verification"""
    is_valid: bool
    certification: Optional[CompetitionCertificationOut] = None
    verification_details: Dict[str, Any]
    error_message: Optional[str] = None
