#!/usr/bin/env python3
"""
Comprehensive Phase 1 Testing for Event System Redesign
"""
import sys
import os
from datetime import datetime, timezone, timedelta

# Add the EduFair app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'EduFair', 'app'))

try:
    from sqlalchemy.orm import Session
    from config.session import get_db
    from Models.Events import EventType, Event, EventTypeEnum, EventCategory
    from Models.users import User, UserTypeEnum
    from Models.Exam import Exam
    from Models.Competitions import CompetitionMentorAssignment
    import uuid
    
    def test_event_types():
        """Test event types functionality"""
        print("\n🔍 Testing Event Types...")
        
        db = next(get_db())
        try:
            # Test 1: Check all event types exist
            event_types = db.query(EventType).all()
            expected_types = ['workshop', 'conference', 'webinar', 'competition']
            
            found_types = [et.name for et in event_types]
            print(f"   ✅ Found event types: {found_types}")
            
            for expected in expected_types:
                if expected in found_types:
                    print(f"   ✅ {expected.title()} type exists")
                else:
                    print(f"   ❌ {expected.title()} type missing")
            
            # Test 2: Check event type properties
            for event_type in event_types:
                print(f"   📋 {event_type.display_name}:")
                print(f"      - Name: {event_type.name}")
                print(f"      - Active: {event_type.is_active}")
                print(f"      - Settings: {event_type.default_settings}")
            
            return len(event_types) == 4
            
        except Exception as e:
            print(f"   ❌ Error testing event types: {e}")
            return False
        finally:
            db.close()
    
    def test_event_creation():
        """Test creating events of different types"""
        print("\n🔍 Testing Event Creation...")
        
        db = next(get_db())
        try:
            # Check existing events by type
            for event_type in EventTypeEnum:
                count = db.query(Event).filter(Event.event_type == event_type).count()
                print(f"   ✅ {event_type.value.title()} events: {count}")
            
            # Test event properties
            events = db.query(Event).all()
            for event in events:
                print(f"   📋 Event: {event.title}")
                print(f"      - Type: {event.event_type.value}")
                print(f"      - Institute: {event.institute_id}")
                print(f"      - Collaboration Required: {event.requires_collaboration}")
                print(f"      - Is Competition: {event.is_competition}")
                print(f"      - Max Attendees: {event.max_attendees}")
            
            return len(events) >= 4
            
        except Exception as e:
            print(f"   ❌ Error testing event creation: {e}")
            return False
        finally:
            db.close()
    
    def test_competition_exams():
        """Test competition exam functionality"""
        print("\n🔍 Testing Competition Exams...")
        
        db = next(get_db())
        try:
            # Check competition exams
            comp_exams = db.query(Exam).filter(Exam.is_competition_exam == True).all()
            print(f"   ✅ Competition exams found: {len(comp_exams)}")
            
            for exam in comp_exams:
                print(f"   📋 Exam: {exam.title}")
                print(f"      - Competition Exam: {exam.is_competition_exam}")
                print(f"      - Auto Grading: {exam.auto_grading_enabled}")
                print(f"      - Manual Review: {exam.manual_review_required}")
                print(f"      - Settings: {exam.competition_specific_settings}")
            
            return len(comp_exams) >= 1
            
        except Exception as e:
            print(f"   ❌ Error testing competition exams: {e}")
            return False
        finally:
            db.close()
    
    def test_mentor_capabilities():
        """Test mentor capabilities and teacher-as-mentor"""
        print("\n🔍 Testing Mentor Capabilities...")
        
        db = next(get_db())
        try:
            # Check mentors
            mentors = db.query(User).filter(User.user_type == UserTypeEnum.mentor).all()
            print(f"   ✅ Mentor users: {len(mentors)}")
            
            # Check teachers with mentor capabilities
            teacher_mentors = db.query(User).filter(
                User.user_type == UserTypeEnum.teacher,
                User.can_act_as_mentor == True
            ).all()
            print(f"   ✅ Teachers with mentor capabilities: {len(teacher_mentors)}")
            
            # Check all users with mentor capabilities
            all_mentors = db.query(User).filter(User.can_act_as_mentor == True).all()
            print(f"   ✅ Total users with mentor capabilities: {len(all_mentors)}")
            
            for user in all_mentors:
                print(f"   📋 Mentor: {user.username} ({user.user_type.value})")
                print(f"      - Specializations: {user.mentor_specializations}")
                print(f"      - Experience: {user.judging_experience_years} years")
            
            return len(mentors) + len(teacher_mentors) >= 1
            
        except Exception as e:
            print(f"   ❌ Error testing mentor capabilities: {e}")
            return False
        finally:
            db.close()
    
    def test_mentor_assignments():
        """Test mentor assignment system"""
        print("\n🔍 Testing Mentor Assignments...")
        
        db = next(get_db())
        try:
            # Check mentor assignments
            assignments = db.query(CompetitionMentorAssignment).all()
            print(f"   ✅ Mentor assignments: {len(assignments)}")
            
            for assignment in assignments:
                print(f"   📋 Assignment ID: {assignment.id}")
                print(f"      - Competition: {assignment.competition_id}")
                print(f"      - Mentor: {assignment.mentor_id}")
                print(f"      - Institute: {assignment.institute_id}")
                print(f"      - Status: {assignment.status}")
                print(f"      - Assignment Type: {assignment.assignment_type}")
                print(f"      - Collaboration Verified: {assignment.collaboration_verified}")
                print(f"      - Workload Capacity: {assignment.workload_capacity}")
                print(f"      - Current Workload: {assignment.current_workload}")
                print(f"      - Match Score: {assignment.specialization_match_score}")
            
            return len(assignments) >= 1
            
        except Exception as e:
            print(f"   ❌ Error testing mentor assignments: {e}")
            return False
        finally:
            db.close()
    
    def test_competition_workflow():
        """Test complete competition workflow"""
        print("\n🔍 Testing Competition Workflow...")
        
        db = next(get_db())
        try:
            # Find competition events
            competitions = db.query(Event).filter(Event.event_type == EventTypeEnum.COMPETITION).all()
            print(f"   ✅ Competition events: {len(competitions)}")
            
            for comp in competitions:
                print(f"   📋 Competition: {comp.title}")
                print(f"      - Requires Collaboration: {comp.requires_collaboration}")
                print(f"      - Collaboration Level: {comp.collaboration_required_level}")
                print(f"      - Exam ID: {comp.competition_exam_id}")
                
                # Check if exam exists
                if comp.competition_exam_id:
                    exam = db.query(Exam).filter(Exam.id == comp.competition_exam_id).first()
                    if exam:
                        print(f"      - Exam Found: {exam.title}")
                        print(f"      - Exam Duration: {exam.total_duration} minutes")
                    else:
                        print(f"      - ❌ Exam not found")
                
                # Check mentor assignments for this competition
                assignments = db.query(CompetitionMentorAssignment).filter(
                    CompetitionMentorAssignment.competition_id == comp.id
                ).all()
                print(f"      - Mentor Assignments: {len(assignments)}")
            
            return len(competitions) >= 1
            
        except Exception as e:
            print(f"   ❌ Error testing competition workflow: {e}")
            return False
        finally:
            db.close()
    
    def test_data_integrity():
        """Test data integrity and relationships"""
        print("\n🔍 Testing Data Integrity...")
        
        db = next(get_db())
        try:
            # Test 1: All events have required fields
            events = db.query(Event).all()
            integrity_issues = []
            
            for event in events:
                if not event.event_type:
                    integrity_issues.append(f"Event {event.title} missing event_type")
                if not event.institute_id:
                    integrity_issues.append(f"Event {event.title} missing institute_id")
                if not event.organizer_id:
                    integrity_issues.append(f"Event {event.title} missing organizer_id")
                if not event.category_id:
                    integrity_issues.append(f"Event {event.title} missing category_id")
            
            if integrity_issues:
                for issue in integrity_issues:
                    print(f"   ❌ {issue}")
                return False
            else:
                print(f"   ✅ All {len(events)} events have required fields")
            
            # Test 2: Competition events have exams
            competitions = db.query(Event).filter(Event.event_type == EventTypeEnum.COMPETITION).all()
            for comp in competitions:
                if not comp.competition_exam_id:
                    print(f"   ❌ Competition {comp.title} missing exam")
                    return False
            
            print(f"   ✅ All {len(competitions)} competitions have exams")
            
            # Test 3: Mentor assignments reference valid users and competitions
            assignments = db.query(CompetitionMentorAssignment).all()
            for assignment in assignments:
                mentor = db.query(User).filter(User.id == assignment.mentor_id).first()
                if not mentor:
                    print(f"   ❌ Assignment references invalid mentor: {assignment.mentor_id}")
                    return False
                
                competition = db.query(Event).filter(Event.id == assignment.competition_id).first()
                if not competition:
                    print(f"   ❌ Assignment references invalid competition: {assignment.competition_id}")
                    return False
            
            print(f"   ✅ All {len(assignments)} assignments have valid references")
            
            return True
            
        except Exception as e:
            print(f"   ❌ Error testing data integrity: {e}")
            return False
        finally:
            db.close()
    
    def run_phase1_tests():
        """Run all Phase 1 tests"""
        print("🧪 PHASE 1 COMPREHENSIVE TESTING")
        print("=" * 60)
        
        tests = [
            ("Event Types", test_event_types),
            ("Event Creation", test_event_creation),
            ("Competition Exams", test_competition_exams),
            ("Mentor Capabilities", test_mentor_capabilities),
            ("Mentor Assignments", test_mentor_assignments),
            ("Competition Workflow", test_competition_workflow),
            ("Data Integrity", test_data_integrity),
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            print(f"\n{'='*20} {test_name} {'='*20}")
            try:
                results[test_name] = test_func()
            except Exception as e:
                print(f"❌ Test {test_name} failed with exception: {e}")
                results[test_name] = False
        
        # Summary
        print(f"\n🎯 PHASE 1 TEST SUMMARY")
        print("=" * 60)
        
        passed = 0
        total = len(tests)
        
        for test_name, result in results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} {test_name}")
            if result:
                passed += 1
        
        print(f"\nResults: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 ALL PHASE 1 TESTS PASSED!")
            print("✅ Ready to proceed to Phase 2")
            return True
        else:
            print("❌ Some tests failed. Please fix issues before proceeding to Phase 2")
            return False
    
    if __name__ == "__main__":
        success = run_phase1_tests()
        
        if success:
            print("\n🚀 PHASE 1 COMPLETE - READY FOR PHASE 2")
            print("\nNext: Implementing logical schemas and CRUDs")
        else:
            print("\n🔧 PHASE 1 NEEDS FIXES")
            print("Please address the failing tests before proceeding")
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running this from the correct directory")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
