# Institute-Mentor Relationship System
## Dedicated API for Exam and Competition Assignment

---

## 🎯 **Overview**

The Institute-Mentor Relationship System provides a dedicated API for managing relationships between institutes and mentors. This system replaces direct mentor access to exam routes with a controlled assignment-based approach.

---

## 🔄 **New Architecture**

### **Before (Direct Access)**:
- Mentors had direct access to exam creation/management routes
- No proper access control for exam viewing
- Mixed permissions in exam routes

### **After (Assignment-Based)**:
- Institutes assign mentors to their organization
- Institutes assign specific exams/competitions to mentors
- Mentors only access assigned content
- Clear separation of concerns

---

## 🏗️ **System Components**

### **1. Institute-Mentor Assignment**
```typescript
// Institutes assign mentors to their organization
POST /api/institute-mentor/assign-mentor
{
  "mentor_id": "uuid",
  "workload_capacity": 10,
  "specialization_areas": ["Mathematics", "Physics"],
  "hourly_rate": 50.0,
  "assignment_notes": "Specialized in advanced mathematics"
}
```

### **2. Exam Assignment to Mentors**
```typescript
// Institutes assign specific exams to mentors
POST /api/institute-mentor/assign-exam-to-mentor
{
  "exam_id": "uuid",
  "mentor_id": "uuid",
  "access_type": "review", // or "competition", "evaluation"
  "deadline": "2024-01-15T10:00:00Z",
  "instructions": "Please review for competition suitability"
}
```

### **3. Mentor Dashboard**
```typescript
// Mentors get dedicated dashboard
GET /api/institute-mentor/mentor/dashboard
// Returns: assigned exams, competitions, workload, etc.
```

---

## 🔌 **API Endpoints**

### **Institute Management (Institute Access Only)**

#### **Mentor Assignment**
```typescript
POST   /api/institute-mentor/assign-mentor           // Assign mentor to institute
GET    /api/institute-mentor/my-mentors              // Get institute's mentors
PUT    /api/institute-mentor/mentor-assignment/{id}  // Update assignment
DELETE /api/institute-mentor/mentor/{id}             // Remove mentor
```

#### **Exam/Competition Assignment**
```typescript
POST /api/institute-mentor/assign-exam-to-mentor     // Assign exam to mentor
GET  /api/institute-mentor/mentor/{id}/exams         // Get mentor's exams
GET  /api/institute-mentor/mentor/{id}/competitions  // Get mentor's competitions
```

### **Mentor Access (Mentor Access Only)**

#### **Dashboard and Assignments**
```typescript
GET /api/institute-mentor/my-assigned-exams          // Get assigned exams
GET /api/institute-mentor/my-assigned-competitions   // Get assigned competitions
GET /api/institute-mentor/mentor/dashboard           // Get dashboard summary
```

#### **Access Validation**
```typescript
GET /api/institute-mentor/exam/{id}/access-check         // Check exam access
GET /api/institute-mentor/competition/{id}/access-check  // Check competition access
```

#### **Content Access**
```typescript
GET /api/institute-mentor/exam/{id}/details         // Get exam details (if assigned)
GET /api/institute-mentor/competition/{id}/details  // Get competition details (if assigned)
```

---

## 📊 **Data Models**

### **Institute-Mentor Assignment**
```typescript
interface InstituteMentorAssignment {
  id: UUID;
  mentor_id: UUID;
  institute_id: UUID;
  assigned_by: UUID;
  assignment_type: "institute_mentor";
  status: "assigned" | "accepted" | "declined" | "suspended";
  assigned_at: datetime;
  workload_capacity: number;
  current_workload: number;
  specialization_areas: string[];
  hourly_rate?: number;
  assignment_notes?: string;
}
```

### **Mentor Exam Access**
```typescript
interface MentorExamAccess {
  exam_id: UUID;
  exam_title: string;
  access_type: "review" | "competition" | "evaluation";
  assigned_at: string;
  status: "active" | "pending" | "completed";
  institute_id: UUID;
  deadline?: datetime;
  instructions?: string;
}
```

### **Mentor Dashboard Summary**
```typescript
interface MentorDashboardSummary {
  mentor_id: UUID;
  mentor_name: string;
  total_institutes: number;
  active_assignments: number;
  pending_evaluations: number;
  completed_evaluations: number;
  current_workload_percentage: number;
  recent_exam_assignments: MentorExamAccess[];
  recent_competition_assignments: MentorCompetitionAccess[];
}
```

---

## 🔒 **Access Control**

### **Permission Matrix**

| Action | Institute | Teacher | Mentor | Student |
|--------|-----------|---------|--------|---------|
| Assign Mentor to Institute | ✅ | ❌ | ❌ | ❌ |
| Assign Exam to Mentor | ✅ | ❌ | ❌ | ❌ |
| View Institute Mentors | ✅ | ❌ | ❌ | ❌ |
| Access Assigned Exams | ❌ | ❌ | ✅ | ❌ |
| View Mentor Dashboard | ❌ | ❌ | ✅ | ❌ |
| Create/Manage Exams | ✅ | ✅ | ❌ | ❌ |

### **Access Validation**
- **Mentors** can only access exams/competitions explicitly assigned to them
- **Institutes** can only manage their own mentor assignments
- **Cross-validation** ensures mentors belong to requesting institute

---

## 🔄 **Workflow Examples**

### **1. Institute Assigns Mentor**
```typescript
// Step 1: Institute assigns mentor
POST /api/institute-mentor/assign-mentor
{
  "mentor_id": "mentor-123",
  "workload_capacity": 15,
  "specialization_areas": ["Mathematics", "Physics"]
}

// Step 2: Institute assigns exam to mentor
POST /api/institute-mentor/assign-exam-to-mentor
{
  "exam_id": "exam-456",
  "mentor_id": "mentor-123",
  "access_type": "review"
}

// Step 3: Mentor can now access the exam
GET /api/institute-mentor/exam/exam-456/details
// Returns exam details if mentor has access
```

### **2. Mentor Checks Dashboard**
```typescript
// Mentor views dashboard
GET /api/institute-mentor/mentor/dashboard
// Returns:
{
  "mentor_id": "mentor-123",
  "mentor_name": "Dr. John Smith",
  "total_institutes": 2,
  "active_assignments": 5,
  "pending_evaluations": 3,
  "recent_exam_assignments": [...],
  "recent_competition_assignments": [...]
}
```

### **3. Competition Assignment**
```typescript
// Institute creates competition (existing flow)
POST /api/competitions/
{
  "title": "Math Competition 2024",
  "exam_id": "exam-456",
  // ... other competition details
}

// System automatically assigns mentors based on exam assignments
// OR institute manually assigns mentors to competition
POST /api/competitions/{competition-id}/mentors
{
  "mentor_id": "mentor-123",
  "workload_capacity": 10
}
```

---

## 🎨 **Frontend Implementation**

### **Institute Dashboard Components**
```jsx
// Institute mentor management
<InstituteMentorManagement>
  <MentorAssignmentForm />
  <AssignedMentorsList />
  <ExamAssignmentModal />
  <MentorWorkloadChart />
</InstituteMentorManagement>

// Exam assignment to mentors
<ExamMentorAssignment>
  <ExamSelector />
  <MentorSelector />
  <AssignmentSettings />
  <AssignmentPreview />
</ExamMentorAssignment>
```

### **Mentor Dashboard Components**
```jsx
// Mentor dashboard
<MentorDashboard>
  <WorkloadSummary />
  <AssignedExamsList />
  <AssignedCompetitionsList />
  <PendingEvaluations />
  <RecentActivity />
</MentorDashboard>

// Exam access for mentors
<MentorExamAccess>
  <ExamDetails />
  <AccessValidation />
  <EvaluationInterface />
  <SubmissionHistory />
</MentorExamAccess>
```

---

## 🚀 **Benefits**

### **For Institutes**
- ✅ **Full Control**: Complete control over mentor access
- ✅ **Selective Assignment**: Assign specific exams to specific mentors
- ✅ **Workload Management**: Track and manage mentor workload
- ✅ **Quality Control**: Ensure only qualified mentors access content

### **For Mentors**
- ✅ **Clear Assignments**: Know exactly what they need to work on
- ✅ **Organized Dashboard**: Centralized view of all assignments
- ✅ **Access Validation**: Clear understanding of access permissions
- ✅ **Professional Workflow**: Structured assignment-based approach

### **For System**
- ✅ **Better Security**: Controlled access to sensitive content
- ✅ **Audit Trail**: Track who has access to what and when
- ✅ **Scalability**: Easy to manage large numbers of mentors
- ✅ **Separation of Concerns**: Clear boundaries between roles

---

## 🔧 **Migration Notes**

### **Changes Made**
1. **Removed** mentors from general exam routes
2. **Created** dedicated institute-mentor relationship system
3. **Added** assignment-based access control
4. **Implemented** mentor dashboard and access validation

### **Breaking Changes**
- Mentors can no longer directly access `/api/exams/*` routes
- Mentors must be assigned by institutes to access content
- New permission structure requires frontend updates

### **Migration Steps**
1. Update frontend to use new institute-mentor endpoints
2. Implement mentor assignment workflow in institute dashboard
3. Update mentor interfaces to use assignment-based access
4. Test access control and validation flows

---

This new system provides much better control and organization for institute-mentor relationships while maintaining security and proper access control.
