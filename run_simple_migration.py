#!/usr/bin/env python3
"""
Run event system migration using SQLAlchemy create_all
"""
import sys
import os

# Add the EduFair app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'EduFair', 'app'))

try:
    from sqlalchemy import text
    from config.session import engine, Base
    from Models.Events import (
        EventType, Event, CompetitionDetails, CompetitionMentorAssignment,
        CompetitionSubmission, CompetitionJudgment, EventTypeConfiguration
    )
    from Models.users import User
    from Models.Exam import Exam
    
    def run_simple_migration():
        """Run the event system migration using SQLAlchemy"""
        print("🚀 Running Simple Event System Migration")
        print("=" * 50)
        
        try:
            # Create all tables
            print("1. Creating database tables...")
            Base.metadata.create_all(bind=engine)
            print("   ✅ Tables created successfully")
            
            # Add new columns manually
            print("2. Adding new columns...")
            
            with engine.connect() as connection:
                # Add columns to events table
                alter_statements = [
                    "ALTER TABLE events ADD COLUMN IF NOT EXISTS event_type VARCHAR(20) DEFAULT 'workshop'",
                    "ALTER TABLE events ADD COLUMN IF NOT EXISTS requires_collaboration BOOLEAN DEFAULT FALSE",
                    "ALTER TABLE events ADD COLUMN IF NOT EXISTS collaboration_required_level VARCHAR(20) DEFAULT 'basic'",
                    
                    # Add columns to users table
                    "ALTER TABLE users ADD COLUMN IF NOT EXISTS can_act_as_mentor BOOLEAN DEFAULT FALSE",
                    "ALTER TABLE users ADD COLUMN IF NOT EXISTS mentor_specializations JSON",
                    "ALTER TABLE users ADD COLUMN IF NOT EXISTS judging_experience_years INTEGER DEFAULT 0",
                    
                    # Add columns to exams table
                    "ALTER TABLE exams ADD COLUMN IF NOT EXISTS is_competition_exam BOOLEAN DEFAULT FALSE",
                    "ALTER TABLE exams ADD COLUMN IF NOT EXISTS auto_grading_enabled BOOLEAN DEFAULT TRUE",
                    "ALTER TABLE exams ADD COLUMN IF NOT EXISTS manual_review_required BOOLEAN DEFAULT FALSE",
                    "ALTER TABLE exams ADD COLUMN IF NOT EXISTS competition_specific_settings JSON",
                ]
                
                for statement in alter_statements:
                    try:
                        connection.execute(text(statement))
                        connection.commit()
                        print(f"   ✅ {statement[:50]}...")
                    except Exception as e:
                        if "already exists" in str(e).lower() or "duplicate" in str(e).lower():
                            print(f"   ⚠️  Column already exists: {statement[:50]}...")
                        else:
                            print(f"   ❌ Error: {e}")
            
            # Insert default event types
            print("3. Inserting default event types...")
            
            with engine.connect() as connection:
                # Check if event types already exist
                result = connection.execute(text("SELECT COUNT(*) FROM event_types")).scalar()
                
                if result == 0:
                    insert_statements = [
                        """INSERT INTO event_types (id, name, display_name, description, default_settings, is_active) VALUES 
                        (gen_random_uuid(), 'workshop', 'Workshop', 'Hands-on skill-building sessions', '{"max_attendees": 50}', true)""",
                        
                        """INSERT INTO event_types (id, name, display_name, description, default_settings, is_active) VALUES 
                        (gen_random_uuid(), 'conference', 'Conference', 'Large-scale knowledge sharing events', '{"max_attendees": 500}', true)""",
                        
                        """INSERT INTO event_types (id, name, display_name, description, default_settings, is_active) VALUES 
                        (gen_random_uuid(), 'webinar', 'Webinar', 'Online educational sessions', '{"max_attendees": 1000, "is_virtual": true}', true)""",
                        
                        """INSERT INTO event_types (id, name, display_name, description, default_settings, is_active) VALUES 
                        (gen_random_uuid(), 'competition', 'Competition', 'Exam-based contests with mentor judging', '{"requires_collaboration": true}', true)""",
                    ]
                    
                    for statement in insert_statements:
                        try:
                            connection.execute(text(statement))
                            connection.commit()
                            print(f"   ✅ Inserted event type")
                        except Exception as e:
                            print(f"   ❌ Error inserting event type: {e}")
                else:
                    print(f"   ⚠️  Event types already exist ({result} records)")
            
            # Update existing data
            print("4. Updating existing data...")
            
            with engine.connect() as connection:
                update_statements = [
                    "UPDATE events SET event_type = 'competition' WHERE is_competition = true",
                    "UPDATE events SET event_type = 'workshop' WHERE event_type IS NULL OR event_type = ''",
                    "UPDATE exams SET is_competition_exam = true WHERE id IN (SELECT DISTINCT competition_exam_id FROM events WHERE competition_exam_id IS NOT NULL)",
                ]
                
                for statement in update_statements:
                    try:
                        result = connection.execute(text(statement))
                        connection.commit()
                        print(f"   ✅ Updated {result.rowcount} records")
                    except Exception as e:
                        print(f"   ❌ Error updating: {e}")
            
            print("✅ Simple migration completed successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Migration failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def verify_migration():
        """Verify the migration was successful"""
        print("\n🔍 Verifying Migration...")
        
        verification_queries = [
            ("Event Types Table", "SELECT COUNT(*) FROM event_types"),
            ("Events Table", "SELECT COUNT(*) FROM events"),
            ("Users Table", "SELECT COUNT(*) FROM users"),
            ("Exams Table", "SELECT COUNT(*) FROM exams"),
            ("Competition Details Table", "SELECT COUNT(*) FROM competition_details"),
            ("Competition Mentor Assignments Table", "SELECT COUNT(*) FROM competition_mentor_assignments"),
            ("Competition Submissions Table", "SELECT COUNT(*) FROM competition_submissions"),
            ("Competition Judgments Table", "SELECT COUNT(*) FROM competition_judgments"),
        ]
        
        try:
            with engine.connect() as connection:
                for description, query in verification_queries:
                    try:
                        result = connection.execute(text(query)).scalar()
                        print(f"   ✅ {description}: {result} records")
                    except Exception as e:
                        print(f"   ❌ {description}: Error - {e}")
                        
        except Exception as e:
            print(f"❌ Verification failed: {e}")
            return False
        
        return True
    
    if __name__ == "__main__":
        success = run_simple_migration()
        if success:
            verify_migration()
            print("\n🎉 Event System Migration Complete!")
            print("\nNext steps:")
            print("1. Run: python test_event_system_migration.py")
            print("2. Test event creation with new types")
            print("3. Test competition functionality")
        else:
            print("\n❌ Migration failed. Please check the errors above.")
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running this from the correct directory")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
