# Event System Redesign Plan

## 📋 **Current State Analysis**

### ✅ **Existing Components:**
- Basic Event model with competition support
- Event categories, locations, speakers, tickets
- Event registration and payment system
- Basic competition exam integration
- Institute event management

### ❌ **Issues Identified:**
1. **Limited Event Types**: Only basic events and competitions
2. **Incomplete Competition System**: No proper mentor judging system
3. **Missing Role Flexibility**: Teachers can't act as mentors
4. **Weak Institute-Mentor Collaboration**: No proper collaboration-based judging
5. **Insufficient Event Type Differentiation**: All events use same structure

## 🎯 **Redesign Objectives**

### 1. **Event Type Specialization**
- **Workshops**: Skill-building sessions with hands-on activities
- **Conferences**: Large-scale knowledge sharing events
- **Webinars**: Online educational sessions
- **Competitions**: Exam-based contests with mentor judging

### 2. **Enhanced Competition System**
- Mentor-based exam evaluation
- Institute-mentor collaboration requirements
- Teacher-as-mentor functionality
- Automated judging workflow

### 3. **Role-Based Access Control**
- Institutes: Create all event types
- Mentors: Judge competitions (with collaboration)
- Teachers: Act as mentors + create events
- Students: Participate in events

## 🗄️ **Database Schema Redesign**

### **Core Tables**

#### 1. **events** (Enhanced)
```sql
-- Keep existing fields, add:
event_type ENUM('workshop', 'conference', 'webinar', 'competition') NOT NULL
institute_id UUID REFERENCES users(id) NOT NULL -- Only institutes create events
requires_collaboration BOOLEAN DEFAULT FALSE -- For competitions
collaboration_required_level ENUM('basic', 'verified', 'premium') DEFAULT 'basic'
```

#### 2. **event_types** (New)
```sql
id UUID PRIMARY KEY
name VARCHAR(100) NOT NULL -- workshop, conference, webinar, competition
display_name VARCHAR(100) NOT NULL
description TEXT
default_settings JSON -- Default configurations per type
is_active BOOLEAN DEFAULT TRUE
created_at TIMESTAMP
updated_at TIMESTAMP
```

#### 3. **competition_details** (New - Specialized for competitions)
```sql
id UUID PRIMARY KEY
event_id UUID REFERENCES events(id) UNIQUE NOT NULL
exam_id UUID REFERENCES exams(id) NOT NULL
judging_type ENUM('automated', 'mentor_review', 'hybrid') DEFAULT 'mentor_review'
mentor_assignment_strategy ENUM('auto', 'manual', 'preference') DEFAULT 'auto'
min_mentors_required INTEGER DEFAULT 1
max_mentors_per_submission INTEGER DEFAULT 3
judging_deadline TIMESTAMP
result_publication_date TIMESTAMP
prize_distribution JSON
evaluation_criteria JSON
created_at TIMESTAMP
updated_at TIMESTAMP
```

#### 4. **competition_mentor_assignments** (New)
```sql
id UUID PRIMARY KEY
competition_id UUID REFERENCES competition_details(id) NOT NULL
mentor_id UUID REFERENCES users(id) NOT NULL -- mentor or teacher acting as mentor
institute_id UUID REFERENCES users(id) NOT NULL -- institute organizing the competition
assignment_type ENUM('auto', 'manual', 'volunteer') DEFAULT 'auto'
collaboration_verified BOOLEAN DEFAULT FALSE
assignment_status ENUM('pending', 'accepted', 'declined', 'completed') DEFAULT 'pending'
assigned_at TIMESTAMP DEFAULT NOW()
accepted_at TIMESTAMP
completed_at TIMESTAMP
workload_capacity INTEGER DEFAULT 10 -- max submissions to judge
current_workload INTEGER DEFAULT 0
specialization_match_score DECIMAL(3,2) -- how well mentor matches competition topic
created_at TIMESTAMP
updated_at TIMESTAMP

UNIQUE(competition_id, mentor_id)
```

#### 5. **competition_submissions** (New)
```sql
id UUID PRIMARY KEY
competition_id UUID REFERENCES competition_details(id) NOT NULL
participant_id UUID REFERENCES users(id) NOT NULL -- student
exam_attempt_id UUID REFERENCES student_exam_attempts(id) NOT NULL
submission_status ENUM('submitted', 'under_review', 'judged', 'appealed') DEFAULT 'submitted'
submitted_at TIMESTAMP DEFAULT NOW()
judging_started_at TIMESTAMP
judging_completed_at TIMESTAMP
final_score DECIMAL(5,2)
final_rank INTEGER
is_winner BOOLEAN DEFAULT FALSE
prize_category VARCHAR(100) -- 1st, 2nd, 3rd, participation, etc.
created_at TIMESTAMP
updated_at TIMESTAMP

UNIQUE(competition_id, participant_id) -- One submission per participant per competition
```

#### 6. **competition_judgments** (New)
```sql
id UUID PRIMARY KEY
submission_id UUID REFERENCES competition_submissions(id) NOT NULL
mentor_id UUID REFERENCES users(id) NOT NULL
judgment_status ENUM('pending', 'in_progress', 'completed', 'disputed') DEFAULT 'pending'
score DECIMAL(5,2)
feedback TEXT
evaluation_criteria_scores JSON -- breakdown by criteria
time_spent_minutes INTEGER
started_at TIMESTAMP
completed_at TIMESTAMP
created_at TIMESTAMP
updated_at TIMESTAMP

UNIQUE(submission_id, mentor_id)
```

#### 7. **mentor_institute_collaborations** (Enhanced from existing invitations)
```sql
-- Enhance existing mentor_institute_invites table
collaboration_level ENUM('basic', 'verified', 'premium') DEFAULT 'basic'
can_judge_competitions BOOLEAN DEFAULT FALSE
specialization_areas JSON -- areas mentor can judge
max_concurrent_judgments INTEGER DEFAULT 5
performance_rating DECIMAL(3,2) DEFAULT 0.0
total_judgments_completed INTEGER DEFAULT 0
```

#### 8. **event_type_configurations** (New)
```sql
id UUID PRIMARY KEY
event_type_id UUID REFERENCES event_types(id) NOT NULL
institute_id UUID REFERENCES users(id) NOT NULL
custom_settings JSON -- Institute-specific settings for event types
is_enabled BOOLEAN DEFAULT TRUE
created_at TIMESTAMP
updated_at TIMESTAMP

UNIQUE(event_type_id, institute_id)
```

### **Enhanced Existing Tables**

#### **users** (Add fields)
```sql
-- Add to existing users table
can_act_as_mentor BOOLEAN DEFAULT FALSE -- For teachers
mentor_specializations JSON -- Areas of expertise
judging_experience_years INTEGER DEFAULT 0
```

#### **exams** (Enhance for competitions)
```sql
-- Add to existing exams table
is_competition_exam BOOLEAN DEFAULT FALSE
auto_grading_enabled BOOLEAN DEFAULT TRUE
manual_review_required BOOLEAN DEFAULT FALSE
competition_specific_settings JSON
```

## 🔄 **Workflow Design**

### **Competition Creation Workflow**
1. **Institute creates competition event**
2. **System validates institute has active mentors**
3. **Competition details configured**
4. **Mentor assignment strategy selected**
5. **Event published**

### **Mentor Assignment Workflow**
1. **System identifies eligible mentors** (with collaboration)
2. **Matches mentors based on specialization**
3. **Sends assignment requests**
4. **Mentors accept/decline**
5. **Backup assignment if needed**

### **Competition Judging Workflow**
1. **Student submits exam**
2. **Auto-grading (if enabled)**
3. **Submission assigned to mentors**
4. **Mentors review and score**
5. **Scores aggregated**
6. **Results published**

### **Teacher-as-Mentor Workflow**
1. **Teacher profile enhanced with mentor capabilities**
2. **Teacher can accept mentor invitations**
3. **Teacher can judge competitions**
4. **Dual role management**

## 🚀 **Implementation Phases**

### **Phase 1: Core Event Types** (Week 1)
- [ ] Create event_types table
- [ ] Enhance events table with event_type
- [ ] Update event creation to support types
- [ ] Basic CRUD for all event types

### **Phase 2: Competition Enhancement** (Week 2)
- [ ] Create competition_details table
- [ ] Create competition_submissions table
- [ ] Enhance exam system for competitions
- [ ] Basic competition creation

### **Phase 3: Mentor System** (Week 3)
- [ ] Create competition_mentor_assignments table
- [ ] Create competition_judgments table
- [ ] Enhance mentor-institute collaborations
- [ ] Mentor assignment algorithms

### **Phase 4: Teacher-as-Mentor** (Week 4)
- [ ] Enhance user model for dual roles
- [ ] Teacher mentor capabilities
- [ ] Role switching functionality
- [ ] Permission management

### **Phase 5: Advanced Features** (Week 5)
- [ ] Automated mentor matching
- [ ] Performance analytics
- [ ] Advanced judging workflows
- [ ] Reporting and insights

## 🧪 **Testing Strategy**

### **Unit Tests**
- Model validations
- CRUD operations
- Business logic functions

### **Integration Tests**
- End-to-end workflows
- Role-based access
- Competition judging process

### **Performance Tests**
- Large-scale event handling
- Concurrent mentor assignments
- Database query optimization

## 📊 **Success Metrics**

- [ ] All 4 event types fully functional
- [ ] Competition judging system operational
- [ ] Teacher-mentor role switching working
- [ ] Institute-mentor collaboration enforced
- [ ] Performance benchmarks met
- [ ] User acceptance testing passed
