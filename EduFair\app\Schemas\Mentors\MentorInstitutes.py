from pydantic import BaseModel, <PERSON>, validator, EmailStr
from uuid import UUID
from typing import List, Optional, Dict, Any
from datetime import datetime
from decimal import Decimal
from enum import Enum

class InviteReceivedByEnum(str, Enum):
    mentor = "mentor"
    institute = "institute"


# Basic schemas for Mentor and Institute in collaborations
class Mentor(BaseModel):
    id: UUID
    username: str
    email: str
    full_name: Optional[str] = None
    bio: Optional[str] = None
    experience_years: Optional[int] = None
    hourly_rate: Optional[float] = None

    class Config:
        from_attributes = True


class Institute(BaseModel):
    id: UUID
    username: str
    email: str
    institute_name: Optional[str] = None
    description: Optional[str] = None
    website: Optional[str] = None

    class Config:
        from_attributes = True


class MentorInstituteInvite(BaseModel):
    receiver_id: UUID
    invitation_message: str  # Required
    proposed_hourly_rate: float  # Required
    proposed_hours_per_week: int  # Required
    expertise_areas_needed: Optional[List[str]] = None
    contract_terms: Optional[str] = None
    expires_at: Optional[datetime] = None

    # Legacy fields for backward compatibility
    hourly_rate: Optional[float] = None
    hours_per_week: Optional[int] = None
    received_by: Optional[InviteReceivedByEnum] = None

    class Config:
        from_attributes = True
# Sender details for invitations
class InvitationSenderDetails(BaseModel):
    id: UUID
    username: str
    email: str
    profile_picture: Optional[str] = None
    profile_image: Optional[Dict[str, Any]] = None  # Base64 image data

    # Mentor-specific fields (when sender is mentor)
    mentor_bio: Optional[str] = None
    mentor_experience_years: Optional[int] = None
    mentor_hourly_rate: Optional[float] = None
    mentor_languages: Optional[List[str]] = None
    mentor_full_name: Optional[str] = None
    mentor_current_position: Optional[str] = None
    mentor_current_organization: Optional[str] = None
    mentor_education: Optional[str] = None
    mentor_certifications: Optional[str] = None
    mentor_is_verified: Optional[bool] = None
    mentor_rating: Optional[float] = None
    # Additional mentor fields from MentorProfile
    mentor_phone: Optional[str] = None
    mentor_linkedin_url: Optional[str] = None
    mentor_website: Optional[str] = None
    mentor_portfolio_url: Optional[str] = None
    mentor_resume_url: Optional[str] = None
    mentor_availability_hours: Optional[Dict[str, Any]] = None
    mentor_verification_status: Optional[str] = None
    mentor_total_reviews: Optional[int] = None

    # Institute-specific fields (when sender is institute) - ENHANCED
    institute_name: Optional[str] = None
    institute_description: Optional[str] = None
    institute_website: Optional[str] = None
    institute_city: Optional[str] = None
    institute_state: Optional[str] = None
    institute_country: Optional[str] = None
    institute_address: Optional[str] = None
    institute_postal_code: Optional[str] = None
    institute_established_year: Optional[int] = None
    institute_type: Optional[str] = None
    institute_accreditation: Optional[str] = None
    institute_linkedin_url: Optional[str] = None
    institute_facebook_url: Optional[str] = None
    institute_twitter_url: Optional[str] = None
    institute_is_verified: Optional[bool] = None
    institute_verification_status: Optional[str] = None
    institute_logo: Optional[Dict[str, Any]] = None  # Base64 image data
    institute_banner: Optional[Dict[str, Any]] = None  # Base64 banner data

    def dict(self, **kwargs):
        """Custom dict method to exclude null/empty fields"""
        data = super().dict(**kwargs)
        # Remove null, empty string, and empty list values
        return {k: v for k, v in data.items() if v is not None and v != "" and v != []}

    class Config:
        from_attributes = True


class MentorInstituteInviteOut(BaseModel):
    id: UUID
    receiver_id: UUID
    mentor_id: Optional[UUID] = None
    institute_id: Optional[UUID] = None
    invitation_message: Optional[str] = None
    status: Optional[str] = None
    proposed_hourly_rate: Optional[float] = None
    proposed_hours_per_week: Optional[int] = None
    expertise_areas_needed: Optional[List[str]] = None  # Now contains subject names, not UUIDs
    expertise_areas_needed_uuids: Optional[List[str]] = None  # Original UUIDs for reference
    contract_terms: Optional[str] = None
    invited_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    responded_at: Optional[datetime] = None
    response_message: Optional[str] = None
    received_by: Optional[InviteReceivedByEnum] = None

    # Legacy fields for backward compatibility
    hourly_rate: Optional[float] = None
    hours_per_week: Optional[int] = None

    created_at: datetime
    updated_at: datetime

    # Enhanced details
    sender: Optional[InvitationSenderDetails] = None
    receiver: Optional[InvitationSenderDetails] = None  # Add receiver details too

    def dict(self, **kwargs):
        """Custom dict method to exclude null/empty fields"""
        data = super().dict(**kwargs)
        # Remove null, empty string, and empty list values
        filtered_data = {k: v for k, v in data.items() if v is not None and v != "" and v != []}

        # Also filter nested objects (sender and receiver)
        if 'sender' in filtered_data and filtered_data['sender']:
            if hasattr(filtered_data['sender'], 'dict'):
                filtered_data['sender'] = filtered_data['sender'].dict()
            elif isinstance(filtered_data['sender'], dict):
                filtered_data['sender'] = {k: v for k, v in filtered_data['sender'].items() if v is not None and v != "" and v != []}

        if 'receiver' in filtered_data and filtered_data['receiver']:
            if hasattr(filtered_data['receiver'], 'dict'):
                filtered_data['receiver'] = filtered_data['receiver'].dict()
            elif isinstance(filtered_data['receiver'], dict):
                filtered_data['receiver'] = {k: v for k, v in filtered_data['receiver'].items() if v is not None and v != "" and v != []}

        return filtered_data

    class Config:
        from_attributes = True
class InvitationListResponse(BaseModel):
    invitations: List[MentorInstituteInviteOut]
    total: int
    page: int
    size: int
    has_next: bool
    has_prev: bool

    class Config:
        from_attributes = True
# CRUD Schemas for Collaboration
class CollaborationCreate(BaseModel):
    mentor_id: UUID
    institute_id: UUID
    hourly_rate: Optional[float] = None
    hours_per_week: Optional[int] = None
    contract_terms: Optional[str] = None
    start_date: Optional[datetime] = None

    class Config:
        from_attributes = True


class CollaborationUpdate(BaseModel):
    status: Optional[str] = None
    hourly_rate: Optional[float] = None
    hours_per_week: Optional[int] = None
    contract_terms: Optional[str] = None
    end_date: Optional[datetime] = None

    class Config:
        from_attributes = True


class CollaborationDetails(BaseModel):
    id: UUID
    mentor: Mentor
    institute: Institute
    status: str
    hourly_rate: Optional[float] = None
    hours_per_week: Optional[int] = None
    contract_terms: Optional[str] = None
    start_date: datetime
    end_date: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class CollaborationListResponse(BaseModel):
    collaborations: List[CollaborationDetails]
    total: int
    page: int
    size: int
    has_next: bool
    has_prev: bool

    class Config:
        from_attributes = True